# CLI 帮助信息更新总结

## 更新概述

已成功更新 `cisa_cli.py` 的所有帮助信息，添加了对新的 `all_resources_tools` 分析类型的支持和说明。

## 更新内容

### 1. 主程序标题和描述
**更新前:**
```
CISA Services Analyzer CLI Tool
Comprehensive command-line interface for CISA services analysis
```

**更新后:**
```
CISA Resources & Tools Analyzer CLI Tool
Comprehensive command-line interface for CISA resources, services, programs, and tools analysis
```

### 2. 应用程序帮助信息
**更新前:**
```
CISA Services Analyzer - 命令行工具
```

**更新后:**
```
CISA Resources & Tools Analyzer - 全面的CISA资源、服务、项目和工具分析命令行工具
```

### 3. Banner 横幅信息
**更新前:**
```
🔧 CISA Services Analyzer
• 分步骤执行分析流程
• JSON 数据转换为 Markdown 表格
• 状态管理和恢复
```

**更新后:**
```
🔧 CISA Resources & Tools Analyzer
• 支持多种分析类型：Services, Programs, Trainings, Resources, Groups, All Resources & Tools
• 分步骤执行分析流程，支持断点续传
• JSON 数据转换为 Markdown/CSV 表格
• 状态管理和恢复功能
• 🆕 全面的资源和工具爬取功能
```

### 4. step 命令帮助信息
**新增详细说明:**
```
分步骤执行分析模式

支持的分析类型：
• services: CISA 服务分析
• programs: CISA 项目分析
• trainings: CISA 培训分析
• resources: CISA 资源分析
• groups: CISA 组织分析
• all_resources_tools: 🆕 CISA 所有资源和工具分析（包含所有类型）
• both: 同时分析服务和项目

示例：
python cisa_cli.py step --type all_resources_tools
python cisa_cli.py step --type services --from-step scrape_content
```

### 5. full 命令帮助信息
**新增详细说明:**
```
完整分析模式（一键执行所有步骤）

支持的分析类型：
• services: CISA 服务分析
• programs: CISA 项目分析
• trainings: CISA 培训分析
• resources: CISA 资源分析
• groups: CISA 组织分析
• all_resources_tools: 🆕 CISA 所有资源和工具分析（包含所有类型，约183页数据）
• both: 同时分析服务和项目

示例：
python cisa_cli.py full --type all_resources_tools
python cisa_cli.py full --type services --force-update
```

### 6. analyze 命令帮助信息
**新增详细说明:**
```
灵活的分析命令 - 支持指定目标和模式

分析目标：
• services: CISA 服务分析
• programs: CISA 项目分析
• trainings: CISA 培训分析
• resources: CISA 资源分析
• groups: CISA 组织分析
• all_resources_tools: 🆕 CISA 所有资源和工具分析
• both: 同时分析服务和项目

执行模式：
• step: 分步骤执行，支持断点续传
• full: 一键完整执行

示例：
python cisa_cli.py analyze all_resources_tools --mode full
python cisa_cli.py analyze services --mode step --from-step scrape_content
```

### 7. export 命令帮助信息
**新增详细说明:**
```
导出 JSON 数据为 Markdown 表格或 CSV 文件

支持导出所有分析结果，包括：
• CISA Services 分析结果
• CISA Programs 分析结果
• CISA Trainings 分析结果
• CISA Resources 分析结果
• CISA Groups 分析结果
• 🆕 CISA All Resources & Tools 分析结果

导出格式：
• markdown: 生成易读的 Markdown 表格
• csv: 生成 CSV 文件，便于数据分析

示例：
python cisa_cli.py export data/cisa_all_resourcess_and_tools_info.json
python cisa_cli.py export data/cisa_services.json --format csv -o services.csv
python cisa_cli.py export data/cisa_groups.json --fields cisa_official_name,url,description
```

### 8. 主程序使用示例
**新增示例:**
```
指定分析类型:
• cisa_cli.py step --type programs                    # 分步骤分析 programs
• cisa_cli.py step --type all_resources_tools         # 🆕 分步骤分析所有资源和工具
• cisa_cli.py full --type both                        # 完整分析 services 和 programs
• cisa_cli.py full --type all_resources_tools         # 🆕 完整分析所有资源和工具
• cisa_cli.py analyze services --mode step            # 使用新的 analyze 命令

基础命令:
• cisa_cli.py export data/cisa_all_resourcess_and_tools_info.json # 🆕 导出所有资源和工具
```

## 参数选项更新

### 所有命令的 --type 参数
**更新前:**
```
services, programs, trainings, groups, resources, 或 both
```

**更新后:**
```
services, programs, trainings, groups, resources, all_resources_tools, 或 both
```

### 验证列表更新
所有命令中的有效类型验证列表都已更新，包含 `all_resources_tools`：
```python
valid_types = ["services", "programs", "trainings", "groups", "resources", "all_resources_tools", "both"]
```

## 测试结果

✅ **所有帮助信息测试通过:**
- `cisa_cli.py --help`: 显示更新后的主程序帮助
- `cisa_cli.py step --help`: 显示详细的分步骤模式帮助
- `cisa_cli.py full --help`: 显示详细的完整分析模式帮助
- `cisa_cli.py export --help`: 显示详细的导出功能帮助
- `cisa_cli.py analyze --help`: 显示详细的灵活分析命令帮助

✅ **用户体验改进:**
- 清晰的功能分类和说明
- 丰富的使用示例
- 突出显示新功能（🆕 标识）
- 详细的参数说明

## 使用效果

用户现在可以通过帮助信息清楚地了解：
1. 支持的所有分析类型，包括新的 `all_resources_tools`
2. 每种分析类型的具体用途
3. 详细的使用示例
4. 各种参数的作用和用法
5. 新功能的特点和优势

这些更新使得CLI工具的帮助信息更加完整、用户友好，并且突出了新增的 `all_resources_tools` 功能。
