# 20250721.CISA.gov.Analyzer 项目开发规则

## 开发环境和工具

### 编程语言
- **主要开发语言**: Python
- 使用现代 Python 特性和最佳实践
- 遵循 PEP 8 代码风格规范

### 优先使用的 Python 软件包
在 Python 开发中，优先使用以下现代化软件包：

#### 网络请求
- **httpx**: 优先使用 httpx 替代 requests，支持异步操作和 HTTP/2
- 配置示例：
  ```python
  import httpx

  # 同步客户端
  with httpx.Client(proxies={'http://': 'http://127.0.0.1:8118/', 'https://': 'http://127.0.0.1:8118/'}) as client:
      response = client.get('https://example.com')

  # 异步客户端
  async with httpx.AsyncClient(proxies={'http://': 'http://127.0.0.1:8118/', 'https://': 'http://127.0.0.1:8118/'}) as client:
      response = await client.get('https://example.com')
  ```

#### 图形和链式处理
- **graphlang**: 用于图形语言处理和分析
- **graphchain**: 用于构建图形化的处理链和工作流
- 这些包特别适用于复杂的数据处理和分析任务

#### 其他推荐包
- **pydantic**: 数据验证和设置管理
- **typer**: 命令行界面构建
- **loguru**: 现代化日志记录
- **rich**: 丰富的终端输出

### 包管理
- **包管理器**: uv
- 使用 `uv` 进行依赖管理和虚拟环境管理
- 不要手动编辑 `pyproject.toml` 或 `requirements.txt`，使用 uv 命令进行包管理
- 使用代理进行 uv 操作
- 常用命令：
  - `HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ add <package>` - 添加依赖
  - `HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ remove <package>` - 移除依赖
  - `HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ sync` - 同步依赖
  - `HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run <command>` - 在虚拟环境中运行命令

## 网络访问配置

### 代理设置
由于网络限制，所有网络访问都需要通过代理：

#### 控制台访问网站
```bash
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ <command>
```

#### Git 操作
所有 git 网络操作都需要使用代理：
```bash
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ git clone <repository>
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ git push
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ git pull
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ git fetch
```

#### Python 代码中的网络访问
在 Python 代码中访问网络时，需要配置代理。**优先使用 httpx**：

```python
import os
import httpx

# 方法1: 设置代理环境变量
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:8118/'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:8118/'

# 方法2: 使用 httpx 客户端直接指定代理（推荐）
proxies = {
    'http://': 'http://127.0.0.1:8118/',
    'https://': 'http://127.0.0.1:8118/'
}

# 同步请求
with httpx.Client(proxies=proxies) as client:
    response = client.get('https://example.com')

# 异步请求
async with httpx.AsyncClient(proxies=proxies) as client:
    response = await client.get('https://example.com')

# 如果必须使用 requests（不推荐）
import requests
response = requests.get('https://example.com', proxies={
    'http': 'http://127.0.0.1:8118/',
    'https': 'http://127.0.0.1:8118/'
})
```

## 项目特定规则

### CISA.gov 分析器
- 本项目专注于分析 CISA.gov 网站
- 需要处理网络请求时必须使用代理配置
- 遵循数据安全和隐私保护最佳实践
- 实现适当的错误处理和日志记录

### 代码组织
- 使用清晰的模块结构
- 实现适当的异常处理
- 添加详细的文档字符串
- 编写单元测试

### 代码文件命名和组织规范

#### 非通用代码命名规范
非通用的代码应保存在单独文件中，并遵循命名前缀规范：
- **Programs 相关代码**: `cisa_programs_*.py`
- **Services 相关代码**: `cisa_services_*.py`

#### 代码行数控制
- 每个文件代码不超过 500 行
- 当文件超过 500 行时，应按功能拆分为多个文件
- 优先保持功能内聚性，避免过度拆分

## 项目结构规范

### 目录结构
- `src/` - 主要源代码目录
- `test/` - 测试代码、调试代码、基准测试代码、演示代码
- `docs/` - 文档目录
  - `docs/dev_logs/` - 开发文档和开发日志
  - `docs/debug_logs/` - 调试日志（自动生成）
- `data/` - 数据文件目录
  - `data/backups/` - 备份数据

### 文件放置规则
#### 测试和调试代码放到 test 目录
- 单元测试：`test/test_*.py`
- 集成测试：`test/test_integration_*.py`
- 基准测试：`test/benchmark_*.py`
- 调试脚本：`test/debug_*.py`
- 演示代码：`test/demo*.py`
- 临时测试：`test/temp_*.py`

#### 开发、调试文档分别放到指定目录
- 开发文档：`docs/dev_logs/` - 开发过程记录、设计文档、技术决策
- 调试日志：`docs/debug_logs/` - 程序运行日志、错误日志（自动生成）
- 用户文档：`docs/` 根级别 - README、使用说明、API文档

### 根目录清理规范
根目录应该保持整洁，只包含：
- 配置文件：`pyproject.toml`, `.env`, `.env.example`
- 运行脚本：`run_analyzer.py`
- 项目文档：`README.md`, `LICENSE`
- 项目管理：`.gitignore`, `.augmentrules`

#### 不应出现在根目录的文件
- 测试脚本：`test_*.py`, `benchmark_*.py`
- 调试脚本：`debug_*.py`
- 演示脚本：`demo*.py`
- 临时文件：`temp_*.py`, `*.tmp`
- 日志文件：`*.log`

### 日志和注释
- 代码注释使用英文
- 日志消息使用英文
- 文档和用户界面可以使用中文

## 开发流程

1. 使用 `uv` 管理依赖
2. 确保所有网络访问都通过代理
3. 编写测试用例
4. 遵循代码审查流程
5. 保持代码质量和文档更新

## 注意事项

- 所有外部网络请求都必须配置代理
- 所有 git 网络操作都必须使用代理
- 测试时也要考虑代理配置
- 处理网络超时和连接错误
- 实现适当的重试机制
- 可以考虑配置 git 全局代理以简化操作
