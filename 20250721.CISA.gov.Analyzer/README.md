# CISA Services Analyzer

CISA.gov 网站服务分析器 - 自动化爬取和分析 CISA 官网所有服务信息的工具

## 功能特性

✨ **全自动化分析流程**
- 自动发现并爬取 CISA.gov 所有服务页面（智能分页检测）
- 智能提取服务基础信息和详细内容
- 使用 LLM 进行内容分析和结构化提取
- 生成完整的 JSON 格式分析报告

🔍 **智能内容分析**
- 自动识别服务类型和领域
- 提取发布日期、目标受众等关键信息
- 判断是否为网络安全相关服务
- 区分 CISA 官方服务和外部提供服务

📊 **丰富的输出格式**
- 结构化 JSON 数据输出
- **新增：Markdown 表格导出功能**
- **🆕 新增：CSV 文件导出功能**
- 自动生成汇总统计报告
- 完整的日志记录和错误追踪
- 中间数据备份和恢复机制

🔧 **灵活的执行模式**
- **🆕 分步骤独立执行功能**
- **🆕 支持指定分析类型（Services/Programs/Both）**
- **🆕 支持从任意步骤开始或恢复**
- 智能状态管理和进度跟踪
- 交互式命令行界面

⚡ **高性能并发处理**
- 可配置的并发数量控制
- 智能速率限制避免被网站封禁
- 异常处理和自动重试机制
- 实时进度跟踪和性能监控

## 快速开始

### 1. 安装依赖
```bash
# 使用 uv 进行包管理（推荐）
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv sync
```

### 2. 配置环境
```bash
cp .env.example .env
# 编辑 .env 文件，设置 OpenAI API 密钥等配置
```

### 3. 运行分析器

#### 方式一：使用新的CLI工具（推荐）

**基础用法（默认分析 Services）：**
```bash
# 分步骤执行模式（推荐首次使用）
python3 cisa_cli.py step

# 完整分析模式
python3 cisa_cli.py full

# 查看分析状态
python3 cisa_cli.py status

# 导出JSON为Markdown表格
python3 cisa_cli.py export data/cisa_services.json

# 🆕 导出JSON为CSV文件
python3 cisa_cli.py export data/cisa_services.json --format csv
```

**🆕 指定分析类型：**
```bash
# 分析 Programs
python3 cisa_cli.py step --type programs
python3 cisa_cli.py full --type programs

# 分析 Groups
python3 cisa_cli.py step --type groups
python3 cisa_cli.py full --type groups

# 🆕 分析所有资源和工具（包含所有类型）
python3 cisa_cli.py step --type all_resources_tools
python3 cisa_cli.py full --type all_resources_tools

# 分析 Services 和 Programs
python3 cisa_cli.py step --type both
python3 cisa_cli.py full --type both
```

**🆕 从指定步骤开始执行：**
```bash
# 从内容抓取步骤开始
python3 cisa_cli.py step --from-step scrape_content

# 从 LLM 分析步骤开始（指定分析 Programs）
python3 cisa_cli.py step --type programs --from-step analyze_llm
```

**🆕 使用新的 analyze 命令：**
```bash
# 灵活的分析命令
python3 cisa_cli.py analyze services --mode step
python3 cisa_cli.py analyze programs --mode full
python3 cisa_cli.py analyze groups --mode full
python3 cisa_cli.py analyze all_resources_tools --mode full
python3 cisa_cli.py analyze both --mode step --from-step scrape_content
```

#### 方式二：传统完整执行模式
```bash
# 使用 uv 运行（推荐）
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python run_analyzer.py

# 或者直接运行
python run_analyzer.py
```

### 4. 查看结果
分析完成后，结果将保存在相应的 JSON 文件中：
- **Services**: `data/cisa_services.json`
- **Programs**: `data/cisa_programs.json`
- **Groups**: `data/cisa_groups.json`
- **All Resources & Tools**: `data/cisa_all_resourcess_and_tools_info.json`
- **Both**: 相应的文件都会生成

使用 `python3 cisa_cli.py export <文件路径>` 可将结果导出为易读的Markdown表格。

## 🆕 All Resources & Tools 功能

新增的 `all_resources_tools` 分析类型可以爬取 CISA 官网上所有类型的资源和工具，包括：

- **Services** (服务)
- **Programs** (项目)
- **Resources** (资源)
- **Training** (培训)
- **Groups** (组织)
- **External Resources** (外部资源)

#### 🚀 智能优化功能

- **智能跳过逻辑**: 自动检测已有正确内容的项目并跳过分析，显著提升处理效率
- **强制更新模式**: 通过 `--force-update` 参数强制重新分析所有项目
- **性能优化**: 通常可跳过 60-80% 的已有有效项目，减少 API 调用和处理时间

### 特点
- **全面覆盖**: 一次性获取所有183页的资源信息
- **智能识别**: 自动识别项目类型和是否为外部提供
- **日期提取**: 尽可能提取发布时间信息
- **去重处理**: 自动去除重复项目
- **🆕 智能跳过逻辑**: 自动跳过已有正确内容的项目，提升处理效率
- **🆕 强制更新模式**: 支持通过 `--force-update` 强制重新分析所有项目

### 使用示例
```bash
# 完整分析所有资源和工具（智能跳过已有正确内容）
python3 cisa_cli.py full --type all_resources_tools

# 分步骤分析（推荐）
python3 cisa_cli.py step --type all_resources_tools

# 🆕 强制更新模式（重新分析所有项目）
python3 cisa_cli.py full --type all_resources_tools --force-update
python3 cisa_cli.py step --type all_resources_tools --force-update

# 导出结果
python3 cisa_cli.py export data/cisa_all_resourcess_and_tools_info.json
python3 cisa_cli.py export data/cisa_all_resourcess_and_tools_info.json --format csv
```

## 项目结构

```
20250721.CISA.gov.Analyzer/
├── src/                    # 源代码目录
│   ├── main.py            # 主程序入口 (16行)
│   ├── config.py          # 配置管理 (163行) [核心基础类]
│   ├── base_scraper.py    # 爬虫基类 (148行) [核心基础类]
│   ├── data_manager.py     # 数据存储管理 (287行) [数据处理类]
│   ├── content_scraper.py  # 通用内容爬取 (199行) [数据处理类]
│   ├── llm_analyzer.py     # LLM 内容分析 (362行) [分析处理类]
│   ├── llm_providers.py    # LLM 提供者抽象层 (351行) [分析处理类]
│   ├── graph_processor.py  # 图形化和链式分析 (346行) [分析处理类]
│   ├── markdown_exporter.py # 🆕 Markdown导出功能 (200行) [工具类]
│   ├── csv_exporter.py     # 🆕 CSV导出功能 (462行) [工具类]
│   ├── concurrency_manager.py # 并发管理 (179行) [工具类]
│   ├── step_manager.py     # 🆕 步骤管理和状态持久化 (206行) [流程管理类]
│   ├── step_main.py        # 🆕 分步骤执行主程序 (441行) [流程管理类]
│   ├── unified_main.py     # 统一主程序 (243行) [流程管理类]
│   ├── cisa_programs_main.py    # Programs 主程序入口 (17行) [Programs专用]
│   ├── cisa_programs_scraper.py # Programs 专用爬虫 (62行) [Programs专用]
│   ├── cisa_groups_main.py      # 🆕 Groups 主程序入口 (95行) [Groups专用]
│   ├── cisa_groups_scraper.py   # 🆕 Groups 专用爬虫 (85行) [Groups专用]
│   ├── cisa_all_resources_tools_scraper.py # 🆕 All Resources & Tools 专用爬虫 (300行) [All Resources & Tools专用]
│   └── cisa_services_scraper.py # ✅ Services 专用爬虫 (106行) [Services专用]
├── docs/                   # 文档目录
│   ├── USAGE.md           # 详细使用说明
│   ├── NEW_FEATURES.md    # 🆕 新功能详细说明
│   ├── dev_logs/          # 开发过程文档
│   └── debug_logs/        # 调试日志目录
├── data/                  # 数据输出目录
│   ├── backups/           # 🆕 中间数据备份目录
│   └── analysis_state.json # 🆕 分析状态文件
├── test/                  # 测试文件目录
│   ├── demo.py           # 功能演示脚本
│   ├── test_analyzer.py  # 主要测试文件
│   └── ...               # 其他测试文件
├── cisa_cli.py           # 🆕 统一命令行工具
├── pyproject.toml         # 项目配置和依赖管理 (uv)
├── uv.lock               # 依赖锁定文件 (uv)
├── .env.example          # 环境配置示例
└── run_analyzer.py       # 便捷运行脚本
```

## 输出数据格式

### Services 分析结果字段：
- **cisa_official_name**: CISA 官方名称（英文）
- **url**: 服务页面 URL
- **service_description**: 服务简介（中文）
- **publication_date**: 发布日期
- **domain**: 对应领域（中文）
- **target_audience**: 目标受众（中文）
- **is_cybersecurity_related**: 是否网络安全相关
- **is_cisa_official**: 是否 CISA 官方提供

### Groups 分析结果字段：
- **cisa_official_name**: CISA 官方名称（英文）
- **url**: 组织页面 URL
- **组织简介**: 组织简介（中文）
- **发布日期**: 发布日期
- **对应领域**: 对应领域（中文）
- **目标受众**: 目标受众（中文）
- **是否网络安全相关**: 是否网络安全相关
- **是否CISA官方提供**: 是否 CISA 官方提供

## 数据转换和导出

### 数据格式转换

如果您有旧格式的JSON文件，请先运行转换脚本：

```bash
# 转换所有 cisa_*.json 文件为新格式（统一英文字段）
python3 scripts/convert_data_files.py

# 验证转换结果
python3 scripts/verify_converted_data.py

# 查看转换演示
python3 scripts/demo_conversion_results.py
```

### 数据导出

#### Markdown 表格导出

使用 CLI 工具导出 Markdown 表格（中文表头）：

```bash
# 导出服务数据为 Markdown
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_services.json

# 导出组织数据为 Markdown
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_groups.json

# 导出培训数据为 Markdown
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_trainings.json

# 导出项目数据为 Markdown
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_programs.json

# 导出资源数据为 Markdown
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_resources.json
```

#### 🆕 CSV 文件导出

使用 CLI 工具导出 CSV 文件（中文表头，Excel 兼容）：

```bash
# 导出服务数据为 CSV
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_services.json --format csv

# 导出组织数据为 CSV
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_groups.json --format csv

# 导出培训数据为 CSV
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_trainings.json --format csv

# 导出项目数据为 CSV
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_programs.json --format csv

# 导出资源数据为 CSV
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_resources.json --format csv

# 自定义输出文件和字段
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_services.json --format csv -o my_services.csv -f cisa_official_name domain description

# 不生成元数据文件
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_services.json --format csv --no-metadata
```

## 测试和演示

### 运行功能演示
```bash
python test/demo.py
```

### 运行测试套件
```bash
python test/test_analyzer.py
```

## 代码组织规范

### 文件命名规范
项目采用严格的文件命名和组织规范，确保代码结构清晰、易于维护：

#### 非通用代码命名前缀
- **Programs 相关**: `cisa_programs_*.py` - 处理 CISA Programs 的专用代码
- **Services 相关**: `cisa_services_*.py` - 处理 CISA Services 的专用代码

#### 通用代码功能分类
通用代码按功能归类，每个文件不超过 500 行：

- **核心基础类**: 配置管理、爬虫基类
- **数据处理类**: 数据存储、内容爬取
- **分析处理类**: LLM 分析、图形处理
- **工具类**: 导出工具、并发管理
- **流程管理类**: 步骤管理、主程序

### 代码质量控制
- ✅ 每个文件代码行数 ≤ 500 行
- ✅ 功能内聚，职责单一
- ✅ 清晰的模块依赖关系
- ✅ 统一的命名规范

### 代码组织特点
- **命名规范**: 所有文件已符合命名规范要求
  - Programs 专用代码: `cisa_programs_*.py`
  - Services 专用代码: `cisa_services_*.py`
  - 通用代码: 按功能分类命名
- **物理结构**: 文件按功能逻辑分类，但物理上保持平铺结构便于导入
- **模块化设计**: 清晰的依赖关系，易于维护和扩展

## 技术特点

- **智能重试机制**: 自动处理网络错误和临时故障
- **代理支持**: 支持 HTTP/HTTPS 代理配置
- **并发控制**: 可配置的并发数量，避免被网站限制
- **速率限制**: 智能延迟控制，保护目标网站
- **进度追踪**: 实时显示分析进度
- **错误恢复**: 完善的错误处理和数据备份
- **模块化设计**: 清晰的代码结构，易于维护和扩展

## 并发控制配置

通过 `.env` 文件可以精确控制并发行为：

```bash
# 内容抓取并发数（推荐 3-5）
MAX_CONCURRENT_REQUESTS=5

# LLM 分析并发数（推荐 2-3）
MAX_CONCURRENT_LLM_REQUESTS=3

# 请求间延迟时间（秒）
REQUEST_DELAY=2.0
```

**配置建议：**
- **保守**: `MAX_CONCURRENT_REQUESTS=3`, `REQUEST_DELAY=3.0`
- **平衡**: `MAX_CONCURRENT_REQUESTS=5`, `REQUEST_DELAY=2.0` (推荐)
- **激进**: `MAX_CONCURRENT_REQUESTS=10`, `REQUEST_DELAY=1.0` (有风险)

详细配置说明请参考 [并发控制文档](docs/concurrency_control.md)

## 注意事项

1. 需要有效的 LLM API 密钥（支持多种提供商）
2. 完整分析可能需要几小时时间
3. 合理设置并发数量，避免被网站封禁
4. 监控日志输出，根据错误率调整配置
5. 网络不稳定时建议使用保守配置
3. 请遵守 CISA.gov 使用条款
4. 建议在稳定网络环境下运行

## 新功能亮点 🆕

### 🎯 多类型分析支持
- **Services 分析**: 分析 CISA 服务页面
- **Programs 分析**: 分析 CISA 项目页面
- **Groups 分析**: 分析 CISA 组织页面
- **Both 分析**: 同时分析上述页面
- 独立的状态管理和数据存储

### 🚀 灵活的执行控制
- **指定分析类型**: `--type services|programs|groups|both`
- **从任意步骤开始**: `--from-step <步骤名>`
- **多种执行模式**: step（分步骤）或 full（完整）
- **新的 analyze 命令**: 更灵活的参数组合

### 📊 多格式数据导出功能
- **Markdown 表格**: 将分析结果转换为易读的Markdown表格
- **🆕 CSV 文件**: 将分析结果导出为 Excel 兼容的 CSV 文件
- 支持中文表头和字段映射
- 可自定义包含字段和格式
- CSV 导出支持元数据文件生成

### 🔄 分步骤独立执行功能
- 支持6个独立分析步骤
- 智能状态管理和恢复
- 交互式命令行界面
- 可从任意步骤开始或继续

详细说明请参考：[新功能文档](docs/NEW_FEATURES.md)

## 详细文档

更多详细信息请参考：
- [新功能说明](docs/NEW_FEATURES.md) 🆕
- [All Resources & Tools 跳过逻辑](docs/ALL_RESOURCES_TOOLS_SKIP_LOGIC.md) 🆕
- [使用说明](docs/USAGE.md)
- [开发日志](docs/dev_logs/)

---

**开发状态**: ✅ 完成
**版本**: 1.0.0
**最后更新**: 2025-01-23
