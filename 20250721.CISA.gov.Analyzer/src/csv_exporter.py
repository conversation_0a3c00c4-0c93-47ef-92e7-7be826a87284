"""
CSV Exporter Module
Converts JSON data to CSV format with Chinese headers
Based on the MarkdownExporter design pattern for consistency
"""
import json
import os
import logging
import pandas as pd
from datetime import datetime
from typing import List, Dict, Optional, Set, Tuple, Any
from config import Config
from field_standardizer import Field<PERSON><PERSON>dardizer, BackwardCompatibilityWrapper


class CSVExporter:
    """Exports JSON data to CSV format with Chinese headers"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Initialize field standardizer (reuse from MarkdownExporter)
        self.field_standardizer = FieldStandardizer()
        self.compatibility_wrapper = BackwardCompatibilityWrapper()

        # Initialize data type configurations
        self._init_data_type_configs()

        # Cache for field analysis to improve performance
        self._field_cache: Dict[str, Set[str]] = {}

    def _init_data_type_configs(self) -> None:
        """Initialize data type configurations in a structured way"""
        # Standard field mappings from English to Chinese (for CSV headers)
        self.field_mappings = {
            'cisa_official_name': 'CISA官方名称',
            'url': '链接地址',
            'description': '描述',  # Will be dynamically updated based on data type
            'publication_date': '发布日期',
            'domain': '对应领域',
            'target_audience': '目标受众',
            'is_cybersecurity_related': '是否网络安全相关',
            'is_cisa_official': '是否CISA官方提供'
        }

        # Unified data type configurations with standardized English fields
        # Note: indicators are used for fallback detection when filename/structure detection fails
        self.data_type_configs = {
            'services': {
                'default_fields': [
                    'cisa_official_name', 'domain', 'target_audience',
                    'description', 'is_cybersecurity_related',
                    'is_cisa_official', 'url'
                ],
                'title': 'CISA 服务列表',
                'indicators': ['description', 'service_description'],  # Include legacy field
                'description_fields': ['description']
            },
            'groups': {
                'default_fields': [
                    'cisa_official_name', 'domain', 'target_audience',
                    'description', 'is_cybersecurity_related', 'is_cisa_official', 'url'
                ],
                'title': 'CISA 组织列表',
                'indicators': ['description', '组织简介'],  # Include legacy field
                'description_fields': ['description']
            },
            'programs': {
                'default_fields': [
                    'cisa_official_name', 'domain', 'target_audience',
                    'description', 'is_cybersecurity_related', 'is_cisa_official', 'url'
                ],
                'title': 'CISA 项目列表',
                'indicators': ['description', 'program_description'],  # Include legacy field
                'description_fields': ['description']
            },
            'resources': {
                'default_fields': [
                    'cisa_official_name', 'domain', 'target_audience',
                    'description', 'is_cybersecurity_related', 'is_cisa_official', 'url'
                ],
                'title': 'CISA 资源列表',
                'indicators': ['description', 'resource_description'],  # Include legacy field
                'description_fields': ['description']
            },
            'trainings': {
                'default_fields': [
                    'cisa_official_name', 'domain', 'target_audience',
                    'description', 'is_cybersecurity_related', 'is_cisa_official', 'url'
                ],
                'title': 'CISA 培训列表',
                'indicators': ['description', 'training_description'],  # Include legacy field
                'description_fields': ['description']
            }
        }

    def load_json_data(self, file_path: str) -> Optional[List[Dict]]:
        """Load and extract data from JSON file with enhanced error handling"""
        try:
            self.logger.info(f"Loading JSON data from: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Handle different JSON structures
            if isinstance(data, list):
                # Direct list of items
                self.logger.info(f"Loaded {len(data)} items from direct list")
                return data
            elif isinstance(data, dict):
                # Check for common data keys
                for key in ['services', 'groups', 'programs', 'resources', 'trainings']:
                    if key in data and isinstance(data[key], list):
                        self.logger.info(f"Loaded {len(data[key])} items from '{key}' key")
                        return data[key]
                
                # If no standard key found, check for any list value
                for key, value in data.items():
                    if isinstance(value, list) and value:
                        self.logger.info(f"Loaded {len(value)} items from '{key}' key")
                        return value
                
                # If still no list found, treat the dict as a single item
                self.logger.info("Treating single dict as single item")
                return [data]
            else:
                self.logger.error(f"Unsupported data type: {type(data)}")
                return None
                
        except FileNotFoundError:
            self.logger.error(f"File not found: {file_path}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON decode error in {file_path}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error loading {file_path}: {e}")
            return None

    def get_available_fields(self, services: List[Dict]) -> Set[str]:
        """Get all available fields from the services data with caching"""
        # Create a cache key based on the first few services
        cache_key = str(hash(str(services[:3]))) if services else "empty"
        
        if cache_key in self._field_cache:
            return self._field_cache[cache_key]
        
        fields = set()
        for service in services:
            if isinstance(service, dict):
                fields.update(service.keys())
        
        self._field_cache[cache_key] = fields
        return fields

    def get_default_fields_for_type(self, data_type: str) -> List[str]:
        """Get default fields for a specific data type"""
        return self.data_type_configs.get(data_type, {}).get('default_fields', 
                                                           self.data_type_configs['services']['default_fields'])

    def get_title_for_type(self, data_type: str) -> str:
        """Get title for a specific data type"""
        return self.data_type_configs.get(data_type, {}).get('title', 'CISA 数据列表')

    def print_field_mappings(self) -> None:
        """Print available field mappings for user reference"""
        print("\n字段映射表:")
        print("英文字段名 -> 中文表头")
        print("-" * 30)
        for eng, chn in self.field_mappings.items():
            print(f"{eng:<25} -> {chn}")

    def format_cell_value(self, value: Any) -> str:
        """Format cell value for CSV with enhanced type support"""
        try:
            if value is None:
                return ""
            elif isinstance(value, bool):
                return "是" if value else "否"
            elif isinstance(value, str):
                # Clean string for CSV: remove newlines and extra whitespace
                formatted = value.replace('\n', ' ').replace('\r', ' ').strip()
                return formatted
            elif isinstance(value, (int, float)):
                return str(value)
            elif isinstance(value, list):
                # Handle list values by joining with semicolons
                if not value:
                    return ""
                # Recursively format list items and join
                formatted_items = [self.format_cell_value(item) for item in value]
                return "; ".join(formatted_items)
            elif isinstance(value, dict):
                # Handle dict values by showing key-value pairs
                if not value:
                    return ""
                # For small dicts, show key-value pairs; for large ones, show summary
                if len(value) <= 3:
                    items = [f"{k}: {self.format_cell_value(v)}" for k, v in value.items()]
                    return "; ".join(items)
                else:
                    return f"对象 ({len(value)} 个属性)"
            elif hasattr(value, 'isoformat'):  # datetime objects
                return value.isoformat()
            else:
                # Fallback for other types
                return str(value)
        except Exception as e:
            self.logger.warning(f"Error formatting value {value}: {e}")
            return str(value) if value is not None else ""

    def _detect_type_from_filename(self, file_path: str) -> Optional[str]:
        """Detect data type from filename"""
        filename = os.path.basename(file_path).lower()

        # Define type patterns for filename detection
        type_patterns = {
            'services': ['service'],
            'groups': ['group'],
            'programs': ['program'],
            'resources': ['resource'],
            'trainings': ['training']
        }

        for data_type, patterns in type_patterns.items():
            for pattern in patterns:
                if pattern in filename:
                    self.logger.debug(f"Detected type '{data_type}' from filename pattern '{pattern}'")
                    return data_type

        return None

    def _calculate_detection_score(self, sample_fields: Set[str], data_type: str, indicators: List[str]) -> int:
        """Calculate detection score for a data type based on field indicators"""
        score = 0
        for indicator in indicators:
            if indicator in sample_fields:
                score += 1
                self.logger.debug(f"Found indicator '{indicator}' for type '{data_type}'")
        return score

    def auto_detect_data_type(self, services: List[Dict], file_path: str = None) -> str:
        """Auto-detect data type based on field names, file name, and data structure"""
        if not services:
            return "services"  # Default fallback

        self.logger.debug(f"Starting data type detection with file_path: {file_path}")

        # First try: detect from file name if available
        if file_path:
            detected_from_filename = self._detect_type_from_filename(file_path)
            if detected_from_filename:
                self.logger.info(f"Data type detected from filename: {detected_from_filename}")
                return detected_from_filename

        # Second try: detect from data structure (check for metadata)
        # This is useful when the JSON has metadata indicating the type
        if isinstance(services, list) and len(services) > 0:
            # Check if we have metadata in the parent structure
            # (This would require access to the original data structure)
            pass

        # Third try: detect from field patterns
        # Sample a few services to get representative fields
        sample_size = min(5, len(services))
        sample_services = services[:sample_size]
        sample_fields = set()
        for service in sample_services:
            if isinstance(service, dict):
                sample_fields.update(service.keys())

        self.logger.debug(f"Auto-detecting data type from {len(sample_services)} sample items")
        self.logger.debug(f"Sample fields: {sorted(sample_fields)}")

        # Use improved indicators for better detection
        detection_scores = {}
        for data_type, config in self.data_type_configs.items():
            indicators = config.get('indicators', [])
            score = self._calculate_detection_score(sample_fields, data_type, indicators)
            if score > 0:
                detection_scores[data_type] = score
                self.logger.debug(f"{data_type} detection score: {score}")

        # Return the type with the highest score
        if detection_scores:
            best_type = max(detection_scores.items(), key=lambda x: x[1])[0]
            self.logger.info(f"Auto-detected data type: {best_type} (score: {detection_scores[best_type]})")
            return best_type

        # Fallback to services
        self.logger.info("Could not auto-detect data type, using default: services")
        return "services"

    def generate_csv_dataframe(self, services: List[Dict],
                              fields: Optional[List[str]] = None,
                              max_description_length: int = None) -> pd.DataFrame:
        """Generate pandas DataFrame from services data with Chinese headers"""
        if not services:
            return pd.DataFrame()

        # Standardize field names in the data
        standardized_services = self.field_standardizer.standardize_data(services)

        # Auto-detect data type and get configuration
        file_path = getattr(self, '_current_file_path', None)
        data_type = self.auto_detect_data_type(standardized_services, file_path)

        if fields is None:
            fields = self.get_default_fields_for_type(data_type)
            self.logger.info(f"Auto-detected data type: {data_type}, using fields: {fields}")

        # Get description fields for this data type
        description_fields = self.data_type_configs.get(data_type, {}).get('description_fields', ['description'])

        # Filter fields that exist in the data
        available_fields = []
        for field in fields:
            field_exists = any(field in service for service in standardized_services)
            self.logger.debug(f"Field '{field}' exists in data: {field_exists}")
            if field_exists:
                available_fields.append(field)

        if not available_fields:
            self.logger.warning("No valid fields found in data")
            return pd.DataFrame()

        # Create Chinese headers
        headers = []
        for field in available_fields:
            chinese_header = self.field_mappings.get(field, field)
            headers.append(chinese_header)
            self.logger.debug(f"Field mapping: {field} -> {chinese_header}")

        # Prepare data for DataFrame
        rows_data = []
        for service in standardized_services:
            row_data = {}
            for i, field in enumerate(available_fields):
                value = service.get(field, None)
                formatted_value = self.format_cell_value(value)

                # Truncate long descriptions if max_description_length is specified
                if (max_description_length and
                    field in description_fields and
                    len(formatted_value) > max_description_length):
                    formatted_value = formatted_value[:max_description_length] + "..."

                # Use Chinese header as column name
                row_data[headers[i]] = formatted_value

            rows_data.append(row_data)

        # Create DataFrame
        df = pd.DataFrame(rows_data)

        # Ensure column order matches the headers
        df = df.reindex(columns=headers)

        self.logger.info(f"Generated DataFrame with {len(df)} rows and {len(df.columns)} columns")
        return df

    def export_to_csv(self, input_file: str, output_file: Optional[str] = None,
                     fields: Optional[List[str]] = None,
                     max_description_length: int = None,
                     include_metadata: bool = True) -> bool:
        """Export JSON data to CSV file with enhanced error handling"""
        try:
            # Store current file path for data type detection
            self._current_file_path = input_file

            # Load data
            services = self.load_json_data(input_file)
            if services is None:
                return False

            # Auto-detect data type for appropriate title
            data_type = self.auto_detect_data_type(services, input_file)
            title = self.get_title_for_type(data_type)

            # Generate output filename if not provided
            if output_file is None:
                base_name = os.path.splitext(input_file)[0]
                output_file = f"{base_name}.csv"

            # Generate DataFrame
            df = self.generate_csv_dataframe(services, fields, max_description_length)

            if df.empty:
                self.logger.error("Generated DataFrame is empty")
                return False

            # Export to CSV with UTF-8 encoding
            df.to_csv(output_file, index=False, encoding='utf-8-sig')  # utf-8-sig for Excel compatibility

            self.logger.info(f"Successfully exported {len(df)} records to {output_file}")

            # Optionally create a metadata file
            if include_metadata:
                metadata_file = output_file.replace('.csv', '_metadata.txt')
                self._create_metadata_file(metadata_file, input_file, data_type, len(services), output_file)

            return True

        except Exception as e:
            self.logger.error(f"Error exporting to CSV: {e}")
            return False
        finally:
            # Clean up
            if hasattr(self, '_current_file_path'):
                delattr(self, '_current_file_path')

    def _create_metadata_file(self, metadata_file: str, input_file: str,
                             data_type: str, record_count: int, csv_file: str) -> None:
        """Create a metadata file with export information"""
        try:
            with open(metadata_file, 'w', encoding='utf-8') as f:
                f.write(f"CSV导出元数据\n")
                f.write(f"=" * 30 + "\n\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"数据来源: {input_file}\n")
                f.write(f"数据类型: {data_type}\n")
                f.write(f"记录总数: {record_count}\n")
                f.write(f"CSV文件: {csv_file}\n")
                f.write(f"编码格式: UTF-8 with BOM (Excel兼容)\n\n")
                f.write(f"此文件由 CISA Services Analyzer 自动生成\n")

            self.logger.info(f"Created metadata file: {metadata_file}")
        except Exception as e:
            self.logger.warning(f"Failed to create metadata file: {e}")

    def get_export_summary(self, input_file: str, fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """Get a summary of what would be exported without actually exporting"""
        try:
            # Store current file path for data type detection
            self._current_file_path = input_file

            services = self.load_json_data(input_file)
            if not services:
                return {"error": "Failed to load data"}

            data_type = self.auto_detect_data_type(services, input_file)

            if fields is None:
                fields = self.get_default_fields_for_type(data_type)

            available_fields = self.get_available_fields(services)
            valid_fields = [f for f in fields if f in available_fields]

            return {
                "data_type": data_type,
                "total_records": len(services),
                "requested_fields": fields,
                "available_fields": list(available_fields),
                "valid_fields": valid_fields,
                "chinese_headers": [self.field_mappings.get(f, f) for f in valid_fields]
            }
        except Exception as e:
            return {"error": str(e)}
        finally:
            # Clean up
            if hasattr(self, '_current_file_path'):
                delattr(self, '_current_file_path')
