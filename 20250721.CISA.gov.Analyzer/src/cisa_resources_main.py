"""
CISA Resources Analysis Main Entry Point
"""
import asyncio
import logging
import sys
import os
from datetime import datetime

# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from cisa_resources_scraper import CISAResourcesScraper
from content_scraper import ContentScraper
from llm_analyzer import LLMAnalyzer
from data_manager import DataManager


async def main(force_update: bool = False):
    """Main function for CISA resources analysis

    Args:
        force_update: Whether to force update all items, ignoring existing valid content
    """
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, Config.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    print("🔍 CISA 资源分析器启动")
    print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Add debug logging for force_update parameter
    logger.info(f"Starting CISA resources analysis with force_update={force_update}")
    if force_update:
        print("🔄 强制更新模式：将重新分析所有资源")
        logger.info("Force update mode enabled: will reanalyze all resources")

    try:
        # Step 1: Scrape resources list
        print("\n🔍 步骤 1: 从 CISA 网站抓取资源列表")
        logger.info("Starting Step 1: Scraping resources list")
        scraper = CISAResourcesScraper()
        resources_list = await scraper.scrape_all_resources()

        if not resources_list:
            logger.error("No resources found in Step 1")
            print("❌ 未找到任何资源")
            return False

        logger.info(f"Step 1 completed successfully: found {len(resources_list)} resources")
        print(f"✅ 步骤 1 完成：找到 {len(resources_list)} 个资源")

        # Filter resources based on existing data and force_update flag
        data_manager = DataManager()
        original_resources_list = resources_list  # Keep original list for merging later
        resources_to_skip = []  # Initialize for later use

        if not force_update:
            logger.info("Filtering resources for analysis (not force_update)")
            resources_to_analyze, resources_to_skip = data_manager.filter_items_for_analysis(
                resources_list, 'resources', force_update
            )

            if resources_to_skip:
                print(f"🔄 跳过 {len(resources_to_skip)} 个已有正确内容的资源")
                logger.info(f"Skipping {len(resources_to_skip)} resources with valid content")

            if resources_to_analyze:
                print(f"📝 需要分析 {len(resources_to_analyze)} 个资源")
                logger.info(f"Will analyze {len(resources_to_analyze)} resources")
                resources_list = resources_to_analyze
            else:
                print(f"✅ 所有资源都已有正确内容，无需重新分析")
                logger.info("All resources have valid content, no analysis needed")
                return True
        else:
            print(f"🔄 强制更新模式：将重新分析所有 {len(resources_list)} 个资源")
            logger.info(f"Force update mode: will analyze all {len(resources_list)} resources")

        # Step 2: Scrape content from each resource page
        print(f"\n📄 步骤 2: 抓取每个资源的详细内容（最大并发数: {Config.MAX_CONCURRENT_REQUESTS}）")
        logger.info("Starting Step 2: Scraping content from resource pages")
        content_scraper = ContentScraper()
        resources_with_content = await content_scraper.scrape_all_content(resources_list)

        if not resources_with_content:
            logger.error("No content scraped in Step 2")
            print("❌ 未能抓取任何资源内容")
            return False

        logger.info(f"Step 2 completed successfully: scraped content for {len(resources_with_content)} resources")
        print(f"✅ 步骤 2 完成：抓取了 {len(resources_with_content)} 个资源的内容")

        # Step 3: Analyze content with LLM
        print(f"\n🤖 步骤 3: 使用 LLM 分析内容（最大并发数: {Config.MAX_CONCURRENT_LLM_REQUESTS}）")
        logger.info("Starting Step 3: LLM analysis")
        llm_analyzer = LLMAnalyzer(analysis_type="resource")
        analyzed_resources = await llm_analyzer.analyze_all_items(resources_with_content)

        if not analyzed_resources:
            logger.error("No resources analyzed in Step 3")
            print("❌ LLM 分析未产生任何结果")
            return False

        logger.info(f"Step 3 completed successfully: analyzed {len(analyzed_resources)} resources")
        print(f"✅ 步骤 3 完成：分析了 {len(analyzed_resources)} 个资源")

        # Step 4: Save results
        print("\n💾 步骤 4: 保存分析结果")
        logger.info("Starting Step 4: Saving results")
        data_manager = DataManager()

        # Add debug logging
        logger.info(f"About to save {len(analyzed_resources)} resources")
        logger.info(f"analyzed_resources type: {type(analyzed_resources)}")
        if analyzed_resources:
            logger.info(f"First resource type: {type(analyzed_resources[0])}")
            logger.info(f"First resource keys: {list(analyzed_resources[0].keys()) if isinstance(analyzed_resources[0], dict) else 'Not a dict'}")

        # Merge with existing data if not force_update
        final_resources = analyzed_resources
        if not force_update and resources_to_skip:
            logger.info(f"Merging {len(analyzed_resources)} analyzed resources with {len(resources_to_skip)} skipped resources")
            print(f"🔄 合并 {len(analyzed_resources)} 个新分析的资源和 {len(resources_to_skip)} 个跳过的资源")

            # Merge analyzed resources with skipped resources
            final_resources = data_manager.merge_analyzed_data(
                analyzed_resources, resources_to_skip, 'resources'
            )
            logger.info(f"Final merged resources count: {len(final_resources)}")
            print(f"✅ 合并后共有 {len(final_resources)} 个资源")

        # Use the specialized save method for resources
        success = data_manager.save_resources_data(final_resources)

        if success:
            logger.info(f"Step 4 completed successfully: saved to {Config.RESOURCES_OUTPUT_FILE}")
            print(f"✅ 步骤 4 完成：结果已保存到 {Config.RESOURCES_OUTPUT_FILE}")
        else:
            logger.error(f"Step 4 failed: unable to save to {Config.RESOURCES_OUTPUT_FILE}")
            print(f"❌ 步骤 4 失败：无法保存结果到 {Config.RESOURCES_OUTPUT_FILE}")
            return False

        # Summary
        logger.info("All steps completed successfully")
        print(f"\n🎉 分析完成！")
        print(f"📊 总计处理: {len(final_resources)} 个资源")
        if not force_update and resources_to_skip:
            print(f"   - 新分析: {len(analyzed_resources)} 个")
            print(f"   - 跳过: {len(resources_to_skip)} 个")
        print(f"📁 输出文件: {Config.RESOURCES_OUTPUT_FILE}")
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        return True

    except Exception as e:
        logger.error(f"Analysis failed with exception: {e}")
        logger.error(f"Exception type: {type(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        print(f"❌ 分析失败: {e}")
        return False


if __name__ == "__main__":
    import sys
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
