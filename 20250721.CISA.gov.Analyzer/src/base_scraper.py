"""
Base Scraper Module
Provides common functionality for CISA website scrapers
"""
import httpx
import asyncio
import logging
import json
import os
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from typing import List, Dict, Optional
from config import Config


class BaseCISAScraper:
    """Base class for CISA website scrapers"""

    # Class-level cache for all_resources_tools data to avoid repeated file reads
    _all_resources_tools_cache = None
    _cache_loaded = False

    def __init__(self, base_url: str):
        self.base_url = base_url
        self.logger = logging.getLogger(__name__)

        # Headers to mimic a real browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    async def get_page_content(self, url: str, retries: int = Config.MAX_RETRIES) -> Optional[BeautifulSoup]:
        """Get page content with retry mechanism using httpx"""
        for attempt in range(retries):
            try:
                self.logger.info(f"Fetching URL: {url} (attempt {attempt + 1})")

                # Get proxy configuration
                proxies = Config.get_proxies()
                proxy_url = None
                if proxies and proxies.get('http://'):
                    proxy_url = proxies['http://']

                self.logger.debug(f"Using proxy: {proxy_url}")

                async with httpx.AsyncClient(
                    headers=self.headers,
                    proxy=proxy_url,
                    timeout=Config.REQUEST_TIMEOUT
                ) as client:
                    response = await client.get(url)
                    response.raise_for_status()

                    self.logger.debug(f"Response status: {response.status_code}, Content length: {len(response.content)}")

                    soup = BeautifulSoup(response.content, 'html.parser')
                    return soup

            except httpx.RequestError as e:
                self.logger.warning(f"Request failed for {url}: {e}")
                if attempt < retries - 1:
                    await asyncio.sleep(Config.REQUEST_DELAY * (attempt + 1))
                else:
                    self.logger.error(f"Failed to fetch {url} after {retries} attempts")
                    return None
            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed for {url}: {e}")
                if attempt < retries - 1:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                else:
                    self.logger.error(f"All attempts failed for {url}")
                    return None

    @classmethod
    def _load_all_resources_tools_data(cls) -> Dict[str, str]:
        """Load all_resources_tools data and create URL to publication_date mapping"""
        if cls._cache_loaded:
            return cls._all_resources_tools_cache or {}

        cls._cache_loaded = True
        cls._all_resources_tools_cache = {}

        try:
            # Get the data directory path relative to the current file
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            data_file = os.path.join(project_root, 'data', 'cisa_all_resourcess_and_tools_info.json')

            if not os.path.exists(data_file):
                logging.getLogger(__name__).debug(f"All resources tools file not found: {data_file}")
                return {}

            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Extract URL to publication_date mapping
            url_to_date = {}
            if 'all_resources_tools' in data:
                for item in data['all_resources_tools']:
                    url = item.get('url')
                    publication_date = item.get('publication_date')

                    if url and publication_date and publication_date != "null" and publication_date is not None:
                        url_to_date[url] = publication_date

            cls._all_resources_tools_cache = url_to_date
            logging.getLogger(__name__).info(f"Loaded {len(url_to_date)} publication dates from all_resources_tools")

        except Exception as e:
            logging.getLogger(__name__).warning(f"Failed to load all_resources_tools data: {e}")
            cls._all_resources_tools_cache = {}

        return cls._all_resources_tools_cache or {}

    def extract_item_info(self, item_element, item_type: str = "item") -> Optional[Dict]:
        """Extract item information from an element (to be overridden by subclasses)"""
        try:
            # Find the item title and URL
            title_element = item_element.find('h3')
            if not title_element:
                return None

            link_element = title_element.find('a')
            if not link_element:
                return None

            title = link_element.get_text(strip=True)
            relative_url = link_element.get('href', '')
            full_url = urljoin('https://www.cisa.gov', relative_url)

            # Simplified logic: set is_cisa_official to "N/A" for Programs and Trainings
            # Only Services will use the EXTERNAL PROVIDER logic

            # Try to get publication_date from all_resources_tools data
            publication_date = None
            try:
                url_to_date_mapping = self._load_all_resources_tools_data()
                if full_url in url_to_date_mapping:
                    publication_date = url_to_date_mapping[full_url]
                    self.logger.debug(f"Found publication_date from all_resources_tools for {title}: {publication_date}")
            except Exception as e:
                self.logger.debug(f"Could not get publication_date from all_resources_tools for {title}: {e}")

            item_info = {
                'cisa_official_name': title,
                'url': full_url,
                'is_cisa_official': "N/A"
            }

            # Add publication_date if found
            if publication_date:
                item_info['publication_date'] = publication_date

            self.logger.debug(f"Extracted {item_type}: {title}")
            return item_info

        except Exception as e:
            self.logger.error(f"Error extracting {item_type} info: {e}")
            return None
    
    async def scrape_page(self, page_num: int) -> List[Dict]:
        """Scrape items from a specific page (to be overridden by subclasses)"""
        raise NotImplementedError("Subclasses must implement scrape_page method")
    
    async def detect_total_pages(self) -> int:
        """Detect total number of pages (to be overridden by subclasses)"""
        raise NotImplementedError("Subclasses must implement detect_total_pages method")
    
    async def scrape_all_items(self, max_pages: int = 10) -> List[Dict]:
        """Scrape all items from all pages"""
        self.logger.info(f"Starting to scrape all items from {self.base_url}")
        self.logger.info(f"Input parameters: max_pages={max_pages}")

        # Debug: Log if using default max_pages value (only warn for very low values that might be problematic)
        if max_pages <= 5:
            self.logger.warning(f"Using low max_pages={max_pages}! This may limit scraping. Consider increasing max_pages parameter.")
            print(f"⚠️  警告：使用较低的最大页数 {max_pages}，这可能会限制抓取范围")

        # Detect total pages
        detected_pages = await self.detect_total_pages()
        self.logger.info(f"Detected total pages: {detected_pages}")
        print(f"🔍 检测到的总页数: {detected_pages}")

        if detected_pages == 0:
            self.logger.warning("Could not detect total pages, using default max_pages")
            print(f"⚠️  无法检测总页数，使用配置的最大页数: {max_pages}")
            total_pages = max_pages
        else:
            total_pages = min(detected_pages, max_pages)  # Respect max_pages limit
            self.logger.info(f"Applied max_pages limit: detected={detected_pages}, max_pages={max_pages}, final={total_pages}")
            print(f"📊 页数计算: 检测到={detected_pages}, 配置最大={max_pages}, 实际使用={total_pages}")

        self.logger.info(f"Will scrape {total_pages} pages")
        print(f"🚀 将抓取 {total_pages} 页数据")
        
        all_items = []

        for page_num in range(total_pages):
            self.logger.info(f"Scraping page {page_num + 1}/{total_pages}")
            print(f"🔍 正在抓取第 {page_num + 1}/{total_pages} 页...")

            items = await self.scrape_page(page_num)

            # If no items found, we might have reached the end
            if not items:
                self.logger.info(f"No items found on page {page_num}, stopping early")
                print(f"⚠️  第 {page_num} 页没有找到项目，提前结束")
                break

            all_items.extend(items)
            print(f"✅ 第 {page_num + 1} 页完成，找到 {len(items)} 个项目（总计: {len(all_items)}）")

            # Add delay between requests
            if page_num < total_pages - 1:
                print(f"⏳ 等待 {Config.REQUEST_DELAY} 秒后继续...")
                await asyncio.sleep(Config.REQUEST_DELAY)

        self.logger.info(f"Scraping completed. Found {len(all_items)} items total")
        print(f"🎉 抓取完成！总共找到 {len(all_items)} 个项目")
        
        return all_items
