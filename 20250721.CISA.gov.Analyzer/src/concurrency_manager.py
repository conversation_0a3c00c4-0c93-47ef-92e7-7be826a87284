"""
Concurrency Manager Mo<PERSON>le
Provides utilities for managing concurrent operations with rate limiting
"""
import asyncio
import logging
from typing import List, Callable, Any, Optional
from config import Config
from tqdm.asyncio import tqdm


class ConcurrencyManager:
    """Manager for controlling concurrent operations"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    async def run_with_concurrency_limit(
        self,
        tasks: List[Any],
        task_func: Callable,
        max_concurrent: int,
        task_name: str = "task",
        delay_between_tasks: float = 0.0
    ) -> List[Any]:
        """
        Run tasks with concurrency limit and optional delay

        Args:
            tasks: List of task inputs
            task_func: Async function to execute for each task
            max_concurrent: Maximum number of concurrent tasks
            task_name: Name for logging purposes
            delay_between_tasks: Delay in seconds between task executions

        Returns:
            List of results (including exceptions)
        """
        self.logger.info(f"Starting {len(tasks)} {task_name}s with max {max_concurrent} concurrent operations")
        print(f"🚀 开始 {task_name}，共 {len(tasks)} 个任务，最大并发数: {max_concurrent}")

        # Create semaphore to limit concurrent operations
        semaphore = asyncio.Semaphore(max_concurrent)
        completed_count = 0

        async def execute_with_semaphore_and_delay(task_input: Any) -> Any:
            nonlocal completed_count
            async with semaphore:
                try:
                    result = await task_func(task_input)
                    completed_count += 1
                    print(f"✅ {task_name} 进度: {completed_count}/{len(tasks)} ({completed_count/len(tasks)*100:.1f}%)")
                    if delay_between_tasks > 0:
                        await asyncio.sleep(delay_between_tasks)
                    return result
                except Exception as e:
                    completed_count += 1
                    print(f"❌ {task_name} 失败: {completed_count}/{len(tasks)} ({completed_count/len(tasks)*100:.1f}%)")
                    self.logger.error(f"Error executing {task_name}: {e}")
                    raise

        # Create tasks for all inputs
        async_tasks = [execute_with_semaphore_and_delay(task_input) for task_input in tasks]

        # Execute tasks concurrently and collect results
        results = await asyncio.gather(*async_tasks, return_exceptions=True)

        # Count successful and failed operations
        successful = sum(1 for result in results if not isinstance(result, Exception))
        failed = len(results) - successful

        self.logger.info(f"Completed {task_name}s: {successful} successful, {failed} failed")
        print(f"🎉 {task_name} 完成！成功: {successful}, 失败: {failed}")

        return results

    async def run_content_scraping(self, services: List[Any], scraper_func: Callable) -> List[Any]:
        """Run content scraping with configured concurrency limits"""
        return await self.run_with_concurrency_limit(
            tasks=services,
            task_func=scraper_func,
            max_concurrent=Config.MAX_CONCURRENT_REQUESTS,
            task_name="content scraping",
            delay_between_tasks=Config.REQUEST_DELAY
        )

    async def run_llm_analysis(self, services: List[Any], analyzer_func: Callable) -> List[Any]:
        """Run LLM analysis with configured concurrency limits"""
        return await self.run_with_concurrency_limit(
            tasks=services,
            task_func=analyzer_func,
            max_concurrent=Config.MAX_CONCURRENT_LLM_REQUESTS,
            task_name="LLM analysis",
            delay_between_tasks=0.1  # Small delay to avoid overwhelming LLM API
        )

    def create_fallback_handler(self, fallback_func: Callable) -> Callable:
        """
        Create a wrapper that handles exceptions and provides fallback results
        
        Args:
            fallback_func: Function to call when the main operation fails
            
        Returns:
            Wrapped function that handles exceptions
        """
        def wrapper(original_func: Callable) -> Callable:
            async def wrapped_func(task_input: Any) -> Any:
                try:
                    return await original_func(task_input)
                except Exception as e:
                    self.logger.warning(f"Operation failed, using fallback: {e}")
                    return fallback_func(task_input)
            return wrapped_func
        return wrapper


class RateLimiter:
    """Simple rate limiter for API calls"""
    
    def __init__(self, max_calls_per_minute: int = 60):
        self.max_calls_per_minute = max_calls_per_minute
        self.calls = []
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """Acquire permission to make a call"""
        async with self.lock:
            now = asyncio.get_event_loop().time()
            
            # Remove calls older than 1 minute
            self.calls = [call_time for call_time in self.calls if now - call_time < 60]
            
            # If we're at the limit, wait
            if len(self.calls) >= self.max_calls_per_minute:
                sleep_time = 60 - (now - self.calls[0])
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                    # Remove the old call after waiting
                    self.calls.pop(0)
            
            # Record this call
            self.calls.append(now)


class ProgressTracker:
    """Track progress of concurrent operations"""
    
    def __init__(self, total_tasks: int, task_name: str = "task"):
        self.total_tasks = total_tasks
        self.task_name = task_name
        self.completed = 0
        self.failed = 0
        self.lock = asyncio.Lock()
        self.logger = logging.getLogger(__name__)
    
    async def mark_completed(self, success: bool = True):
        """Mark a task as completed"""
        async with self.lock:
            if success:
                self.completed += 1
            else:
                self.failed += 1
            
            total_processed = self.completed + self.failed
            if total_processed % 10 == 0 or total_processed == self.total_tasks:
                self.logger.info(
                    f"Progress: {total_processed}/{self.total_tasks} {self.task_name}s "
                    f"({self.completed} successful, {self.failed} failed)"
                )
    
    def get_summary(self) -> dict:
        """Get summary of progress"""
        return {
            'total': self.total_tasks,
            'completed': self.completed,
            'failed': self.failed,
            'success_rate': self.completed / max(1, self.completed + self.failed)
        }
