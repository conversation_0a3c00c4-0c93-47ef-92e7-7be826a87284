"""
CISA Groups Scraper
Specialized scraper for CISA Groups pages
"""
import logging
from typing import List, Dict, Optional
from bs4 import BeautifulSoup
from base_scraper import BaseCISAScraper
from config import Config


class CISAGroupsScraper(BaseCISAScraper):
    """Scraper for CISA Groups pages"""

    def __init__(self):
        super().__init__(Config.CISA_GROUPS_BASE_URL)
        self.logger = logging.getLogger(__name__)

    def extract_group_info(self, element) -> Optional[Dict]:
        """Extract group information from HTML element"""
        try:
            # Find the link element (usually in h3 or h4)
            link_element = element.find('a', href=True)
            if not link_element:
                return None

            # Extract basic information
            cisa_official_name = link_element.get_text(strip=True)
            relative_url = link_element.get('href', '')

            # Skip filter URLs - we only want actual group pages
            if '?f%5B0%5D=' in relative_url or 'f[0]=' in relative_url:
                self.logger.debug(f"Skipping filter URL: {relative_url}")
                return None

            # Convert relative URL to absolute URL
            if relative_url.startswith('/'):
                url = f"https://www.cisa.gov{relative_url}"
            elif relative_url.startswith('http'):
                url = relative_url
            else:
                url = f"https://www.cisa.gov/{relative_url}"

            # Only process URLs that point to actual group pages
            if not url.startswith('https://www.cisa.gov/resources-tools/groups/'):
                self.logger.debug(f"Skipping non-group URL: {url}")
                return None

            # Simplified logic: set is_cisa_official to "N/A" for Groups
            is_cisa_official = "N/A"

            group_info = {
                'cisa_official_name': cisa_official_name,
                'url': url,
                'is_cisa_official': is_cisa_official
            }

            self.logger.debug(f"Extracted group info: {group_info}")
            return group_info

        except Exception as e:
            self.logger.error(f"Error extracting group info: {e}")
            return None

    async def scrape_page(self, page_num: int) -> List[Dict]:
        """Scrape groups from a specific page"""
        url = f"{self.base_url}?page={page_num}"
        soup = await self.get_page_content(url)

        if not soup:
            return []

        groups = []

        # Find all group items - they appear to be in h3 elements with links
        # Look for h3 elements that contain links to actual group pages
        group_elements = soup.find_all('h3')

        for element in group_elements:
            # Find the link within the h3 element
            link_element = element.find('a', href=True)
            if link_element:
                group_info = self.extract_group_info(element)
                if group_info:
                    groups.append(group_info)

        self.logger.info(f"Found {len(groups)} groups on page {page_num}")
        return groups

    async def detect_total_pages(self) -> int:
        """Detect total number of pages for groups by analyzing pagination on first page"""
        try:
            self.logger.info("Discovering total number of pages for groups...")
            print(f"🔍 正在检测 Groups 总页数...")
            soup = await self.get_page_content(f"{self.base_url}?page=0")

            if not soup:
                self.logger.warning("Failed to get first page, using fallback MAX_GROUPS_PAGES")
                print(f"❌ 无法获取首页，使用回退值 MAX_GROUPS_PAGES: {Config.MAX_GROUPS_PAGES}")
                return Config.MAX_GROUPS_PAGES

            # Debug: Log page structure for analysis
            self.logger.debug(f"Page title: {soup.title.string if soup.title else 'No title'}")

            # Look for pagination navigation
            pagination_nav = soup.find('nav', {'aria-label': 'Pagination'})
            if pagination_nav:
                self.logger.debug(f"Found pagination nav: {pagination_nav}")
                print(f"🔍 找到分页导航")
            else:
                self.logger.debug("No pagination nav found")
                print(f"🔍 未找到分页导航")

            # Look for "Last" page link which contains the highest page number
            last_page_link = soup.find('a', string=lambda text: text and 'last' in text.lower())
            self.logger.debug(f"Found last page link: {last_page_link}")
            if last_page_link:
                href = last_page_link.get('href', '')
                self.logger.debug(f"Last page link href: {href}")
                # Extract page number from URL like "?page=29"
                import re
                match = re.search(r'page=(\d+)', href)
                if match:
                    last_page_num = int(match.group(1))
                    total_pages = last_page_num + 1  # Convert from 0-based to count
                    self.logger.info(f"Discovered {total_pages} total pages (0-{last_page_num})")
                    print(f"✅ 通过 'Last' 链接检测到 Groups 总页数: {total_pages}")
                    return total_pages

            # Fallback: look for all page links and find the highest number
            self.logger.debug("Last page link not found, trying fallback method...")
            print(f"🔄 未找到 'Last' 链接，尝试备用方法...")
            page_links = soup.find_all('a', href=True)
            self.logger.debug(f"Found {len(page_links)} total links")
            max_page = 0
            page_links_found = 0
            for link in page_links:
                href = link.get('href', '')
                if 'page=' in href:
                    page_links_found += 1
                    import re
                    match = re.search(r'page=(\d+)', href)
                    if match:
                        page_num = int(match.group(1))
                        max_page = max(max_page, page_num)

            self.logger.debug(f"Found {page_links_found} page links, max page number: {max_page}")

            if max_page > 0:
                total_pages = max_page + 1
                self.logger.info(f"Discovered {total_pages} total pages via page links (0-{max_page})")
                print(f"✅ 通过页面链接检测到 Groups 总页数: {total_pages}")
                return total_pages

            # If no pagination found, assume single page
            self.logger.info("No pagination found, assuming single page")
            print(f"ℹ️  未找到分页导航，假设只有 1 页")
            return 1

        except Exception as e:
            self.logger.error(f"Error discovering total pages: {e}")
            self.logger.warning("Using configured MAX_GROUPS_PAGES as fallback")
            print(f"❌ 检测页数时出错: {e}，使用配置的 MAX_GROUPS_PAGES: {Config.MAX_GROUPS_PAGES}")
            return Config.MAX_GROUPS_PAGES

    # Backward compatibility methods
    async def scrape_groups_page(self, page_num: int) -> List[Dict]:
        """Scrape groups from a specific page (backward compatibility)"""
        return await self.scrape_page(page_num)

    async def scrape_all_groups(self) -> List[Dict]:
        """Scrape all groups from all pages (backward compatibility)"""
        # Use the configured MAX_GROUPS_PAGES to ensure we scrape all pages
        max_pages = Config.MAX_GROUPS_PAGES
        self.logger.info(f"Starting to scrape all groups with max_pages={max_pages}")
        return await self.scrape_all_items(max_pages=max_pages)
