"""
Field Standardizer Module
Provides field name standardization and backward compatibility for CISA data types
"""
import logging
from typing import Dict, List, Optional, Any, Set
from copy import deepcopy


class FieldStandardizer:
    """Field standardization and conversion utility"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Standard English field names for all data types
        self.STANDARD_FIELDS = {
            'cisa_official_name',
            'url', 
            'description',
            'publication_date',
            'domain',
            'target_audience',
            'is_cybersecurity_related',
            'is_cisa_official'
        }
        
        # Legacy field mappings to standard fields
        self.LEGACY_TO_STANDARD = {
            # Chinese fields to English
            '组织简介': 'description',
            '项目简介': 'description',
            '资源简介': 'description',
            '发布日期': 'publication_date',
            '对应领域': 'domain',
            '目标受众': 'target_audience',
            '是否网络安全相关': 'is_cybersecurity_related',
            '是否CISA官方提供': 'is_cisa_official',
            
            # Type-specific English fields to standard
            'service_description': 'description',
            'training_description': 'description',
            'program_description': 'description',
            'group_description': 'description',
            'resource_description': 'description'
        }
        
        # Standard to legacy field mappings by data type
        self.STANDARD_TO_LEGACY = {
            'services': {
                'description': 'service_description'
            },
            'trainings': {
                'description': 'training_description'
            },
            'groups': {
                'description': '组织简介',
                'publication_date': '发布日期',
                'domain': '对应领域',
                'target_audience': '目标受众',
                'is_cybersecurity_related': '是否网络安全相关',
                'is_cisa_official': '是否CISA官方提供'
            },
            'programs': {
                'description': '项目简介',
                'publication_date': '发布日期',
                'domain': '对应领域',
                'target_audience': '目标受众',
                'is_cybersecurity_related': '是否网络安全相关',
                'is_cisa_official': '是否CISA官方提供'
            },
            'resources': {
                'description': '资源简介',
                'publication_date': '发布日期',
                'domain': '对应领域',
                'target_audience': '目标受众',
                'is_cybersecurity_related': '是否网络安全相关',
                'is_cisa_official': '是否CISA官方提供'
            }
        }
    
    def standardize_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Convert item fields to standard English field names"""
        if not isinstance(item, dict):
            return item
            
        standardized = {}
        
        for key, value in item.items():
            # Convert legacy field names to standard
            if key in self.LEGACY_TO_STANDARD:
                standard_key = self.LEGACY_TO_STANDARD[key]
                standardized[standard_key] = value
                self.logger.debug(f"Converted field: {key} -> {standard_key}")
            else:
                # Keep field as-is if already standard or unknown
                standardized[key] = value
        
        return standardized
    
    def standardize_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert list of items to standard field names"""
        if not isinstance(data, list):
            return data
            
        return [self.standardize_item(item) for item in data]
    
    def restore_legacy_fields(self, item: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """Restore legacy field names for backward compatibility"""
        if not isinstance(item, dict) or data_type not in self.STANDARD_TO_LEGACY:
            return item
            
        restored = deepcopy(item)
        legacy_mapping = self.STANDARD_TO_LEGACY[data_type]
        
        for standard_field, legacy_field in legacy_mapping.items():
            if standard_field in restored:
                # Add legacy field while keeping standard field
                restored[legacy_field] = restored[standard_field]
                self.logger.debug(f"Added legacy field: {standard_field} -> {legacy_field}")
        
        return restored
    
    def restore_legacy_data(self, data: List[Dict[str, Any]], data_type: str) -> List[Dict[str, Any]]:
        """Restore legacy field names for list of items"""
        if not isinstance(data, list):
            return data
            
        return [self.restore_legacy_fields(item, data_type) for item in data]
    
    def get_standard_fields_for_type(self, data_type: str) -> List[str]:
        """Get standard field list for a data type"""
        return list(self.STANDARD_FIELDS)
    
    def get_legacy_fields_for_type(self, data_type: str) -> List[str]:
        """Get legacy field list for a data type"""
        if data_type not in self.STANDARD_TO_LEGACY:
            return self.get_standard_fields_for_type(data_type)
            
        legacy_mapping = self.STANDARD_TO_LEGACY[data_type]
        legacy_fields = []
        
        for standard_field in self.STANDARD_FIELDS:
            if standard_field in legacy_mapping:
                legacy_fields.append(legacy_mapping[standard_field])
            else:
                legacy_fields.append(standard_field)
        
        return legacy_fields
    
    def detect_field_format(self, data: List[Dict[str, Any]]) -> str:
        """Detect whether data uses standard or legacy field format"""
        if not data:
            return "unknown"
            
        sample_item = data[0]
        legacy_field_count = sum(1 for field in sample_item.keys() 
                               if field in self.LEGACY_TO_STANDARD)
        standard_field_count = sum(1 for field in sample_item.keys() 
                                 if field in self.STANDARD_FIELDS)
        
        if legacy_field_count > standard_field_count:
            return "legacy"
        elif standard_field_count > 0:
            return "standard"
        else:
            return "unknown"
    
    def validate_standard_fields(self, item: Dict[str, Any]) -> List[str]:
        """Validate that item has required standard fields"""
        missing_fields = []
        required_fields = {'cisa_official_name', 'url', 'description'}
        
        for field in required_fields:
            if field not in item:
                missing_fields.append(field)
        
        return missing_fields
    
    def get_field_mapping_for_display(self, data_type: str) -> Dict[str, str]:
        """Get field mapping for display purposes (English -> Chinese)"""
        # Standard field mappings for display
        display_mappings = {
            'cisa_official_name': 'CISA官方名称',
            'url': '链接地址',
            'description': self._get_description_label(data_type),
            'publication_date': '发布日期',
            'domain': '对应领域',
            'target_audience': '目标受众',
            'is_cybersecurity_related': '是否网络安全相关',
            'is_cisa_official': '是否CISA官方提供'
        }
        
        return display_mappings
    
    def _get_description_label(self, data_type: str) -> str:
        """Get appropriate description label for data type"""
        labels = {
            'services': '服务描述',
            'trainings': '培训简介',
            'groups': '组织简介',
            'programs': '项目简介',
            'resources': '资源简介'
        }
        return labels.get(data_type, '描述')


class BackwardCompatibilityWrapper:
    """Wrapper for maintaining backward compatibility during field standardization"""
    
    def __init__(self):
        self.standardizer = FieldStandardizer()
        self.logger = logging.getLogger(__name__)
    
    def wrap_for_legacy_code(self, data: List[Dict[str, Any]], data_type: str) -> List[Dict[str, Any]]:
        """Wrap standardized data for legacy code consumption"""
        self.logger.debug(f"Wrapping data for legacy code: {data_type}")
        return self.standardizer.restore_legacy_data(data, data_type)
    
    def unwrap_from_legacy_code(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Unwrap data from legacy code and standardize"""
        self.logger.debug("Unwrapping data from legacy code")
        return self.standardizer.standardize_data(data)
    
    def ensure_compatibility(self, data: List[Dict[str, Any]], data_type: str) -> List[Dict[str, Any]]:
        """Ensure data has both standard and legacy fields for maximum compatibility"""
        # First standardize the data
        standardized = self.standardizer.standardize_data(data)
        
        # Then add legacy fields while keeping standard fields
        compatible = self.standardizer.restore_legacy_data(standardized, data_type)
        
        self.logger.debug(f"Ensured compatibility for {len(data)} items of type {data_type}")
        return compatible
    
    def get_compatible_field_list(self, data_type: str) -> List[str]:
        """Get field list that includes both standard and legacy fields"""
        standard_fields = set(self.standardizer.get_standard_fields_for_type(data_type))
        legacy_fields = set(self.standardizer.get_legacy_fields_for_type(data_type))
        
        # Return union of both sets
        return sorted(list(standard_fields.union(legacy_fields)))


# Global instances for easy access
field_standardizer = FieldStandardizer()
compatibility_wrapper = BackwardCompatibilityWrapper()


def standardize_fields(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Convenience function for field standardization"""
    return field_standardizer.standardize_data(data)


def ensure_backward_compatibility(data: List[Dict[str, Any]], data_type: str) -> List[Dict[str, Any]]:
    """Convenience function for ensuring backward compatibility"""
    return compatibility_wrapper.ensure_compatibility(data, data_type)
