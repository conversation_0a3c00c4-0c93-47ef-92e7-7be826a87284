"""
Step-by-step Main Entry Point for CISA Services Analyzer
Supports independent step execution and resume functionality
"""
import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import Optional, List, Dict

# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from cisa_services_scraper import CISAServicesScraper
from content_scraper import ContentScraper
from llm_analyzer import LLMAnalyzer
from data_manager import DataManager
from graph_processor import GraphProcessor, ChainProcessor
from step_manager import StepManager, AnalysisStep, AnalysisType
from markdown_exporter import MarkdownExporter
from cisa_programs_scraper import CISAProgramsScraper
from cisa_trainings_scraper import CISATrainingsScraper
from cisa_resources_scraper import CISAResourcesScraper
from cisa_all_resources_tools_scraper import CISAAllResourcesToolsScraper

def setup_logging():
    """Setup logging configuration"""
    log_level = getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO)
    
    # Create logs directory
    log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'docs', 'debug_logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # Create log filename with timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(log_dir, f'cisa_analyzer_step_{timestamp}.log')
    
    # Configure logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"Step-by-step logging initialized. Log file: {log_file}")
    return logger

async def execute_step_scrape_items(step_manager: StepManager) -> Optional[List[Dict]]:
    """Execute step 1: Scrape items list (services or programs)"""
    logger = logging.getLogger(__name__)

    if step_manager.analysis_type == AnalysisType.SERVICES:
        logger.info("Executing Step 1: Scraping services list")
        print("\n🔍 步骤 1: 从 CISA 网站抓取服务列表")

        scraper = CISAServicesScraper()
        items_list = await scraper.scrape_all_services()
        item_type = "服务"

    elif step_manager.analysis_type == AnalysisType.PROGRAMS:
        logger.info("Executing Step 1: Scraping programs list")
        print("\n🔍 步骤 1: 从 CISA 网站抓取项目列表")

        scraper = CISAProgramsScraper()
        items_list = await scraper.scrape_all_programs()
        item_type = "项目"

    elif step_manager.analysis_type == AnalysisType.TRAININGS:
        logger.info("Executing Step 1: Scraping trainings list")
        print("\n🔍 步骤 1: 从 CISA 网站抓取培训列表")

        scraper = CISATrainingsScraper()
        items_list = await scraper.scrape_all_trainings()
        item_type = "培训"

    elif step_manager.analysis_type == AnalysisType.RESOURCES:
        logger.info("Executing Step 1: Scraping resources list")
        print("\n🔍 步骤 1: 从 CISA 网站抓取资源列表")

        scraper = CISAResourcesScraper()
        items_list = await scraper.scrape_all_resources()
        item_type = "资源"

    elif step_manager.analysis_type == AnalysisType.ALL_RESOURCES_TOOLS:
        logger.info("Executing Step 1: Scraping all resources and tools list")
        print("\n🔍 步骤 1: 从 CISA 网站抓取所有资源和工具列表")

        scraper = CISAAllResourcesToolsScraper()
        max_pages = Config.MAX_ALL_RESOURCES_TOOLS_PAGES
        items_list = await scraper.scrape_all_items(max_pages=max_pages)
        item_type = "所有资源和工具"

    elif step_manager.analysis_type == AnalysisType.BOTH:
        logger.info("Executing Step 1: Scraping both services and programs")
        print("\n🔍 步骤 1: 从 CISA 网站抓取服务和项目列表")

        # Scrape both services and programs
        services_scraper = CISAServicesScraper()
        programs_scraper = CISAProgramsScraper()

        services_list = await services_scraper.scrape_all_services()
        programs_list = await programs_scraper.scrape_all_programs()

        # Combine both lists
        items_list = []
        if services_list:
            items_list.extend(services_list)
        if programs_list:
            items_list.extend(programs_list)

        item_type = "服务和项目"
    else:
        logger.error(f"Unsupported analysis type: {step_manager.analysis_type}")
        return None

    if not items_list:
        logger.error(f"No {item_type} found")
        print(f"❌ 没有找到任何{item_type}")
        return None

    logger.info(f"Found {len(items_list)} {item_type}")
    print(f"✅ 步骤 1 完成：找到 {len(items_list)} 个{item_type}")

    # Filter items based on existing data and force_update flag
    data_manager = DataManager()
    if not step_manager.force_update and step_manager.analysis_type != AnalysisType.BOTH:
        items_to_analyze, items_to_skip = data_manager.filter_items_for_analysis(
            items_list, step_manager.analysis_type.value, step_manager.force_update
        )

        if items_to_skip:
            print(f"🔄 跳过 {len(items_to_skip)} 个已有正确内容的{item_type}")
            logger.info(f"Skipping {len(items_to_skip)} items with valid content")

        if items_to_analyze:
            print(f"📝 需要分析 {len(items_to_analyze)} 个{item_type}")
            logger.info(f"Will analyze {len(items_to_analyze)} items")
            items_list = items_to_analyze
        else:
            print(f"✅ 所有{item_type}都已有正确内容，无需重新分析")
            logger.info("All items have valid content, no analysis needed")
            # Save empty list to indicate completion
            step_manager.save_state(AnalysisStep.SCRAPE_SERVICES, [],
                                   {"item_count": 0, "item_type": item_type, "all_items_valid": True})
            return []
    elif step_manager.force_update:
        print(f"🔄 强制更新模式：将重新分析所有 {len(items_list)} 个{item_type}")
        logger.info(f"Force update mode: will analyze all {len(items_list)} items")

    # Save step state
    step_manager.save_state(AnalysisStep.SCRAPE_SERVICES, items_list,
                           {"item_count": len(items_list), "item_type": item_type, "force_update": step_manager.force_update})

    return items_list

# Backward compatibility
async def execute_step_scrape_services(step_manager: StepManager) -> Optional[List[Dict]]:
    """Execute step 1: Scrape services list (backward compatibility)"""
    return await execute_step_scrape_items(step_manager)

async def execute_step_scrape_content(step_manager: StepManager, 
                                    services_list: Optional[List[Dict]] = None) -> Optional[List[Dict]]:
    """Execute step 2: Scrape content from service pages"""
    logger = logging.getLogger(__name__)
    
    # Load services list if not provided
    if services_list is None:
        services_list = step_manager.load_step_data(AnalysisStep.SCRAPE_SERVICES)
        if services_list is None:
            logger.error("No services list available. Please run step 1 first.")
            print("❌ 没有可用的服务列表，请先执行步骤 1")
            return None
    
    logger.info("Executing Step 2: Scraping content from service pages")
    print(f"\n📄 步骤 2: 抓取服务页面内容（最大并发数: {Config.MAX_CONCURRENT_REQUESTS}）")
    
    content_scraper = ContentScraper()
    services_with_content = await content_scraper.scrape_all_service_contents(services_list)
    
    logger.info(f"Scraped content for {len(services_with_content)} services")
    print(f"✅ 步骤 2 完成：抓取了 {len(services_with_content)} 个服务的内容")
    
    # Save step state
    step_manager.save_state(AnalysisStep.SCRAPE_CONTENT, services_with_content,
                           {"service_count": len(services_with_content)})
    
    return services_with_content

async def execute_step_analyze_llm(step_manager: StepManager,
                                 items_with_content: Optional[List[Dict]] = None) -> Optional[List[Dict]]:
    """Execute step 3: Analyze content with LLM"""
    logger = logging.getLogger(__name__)

    # Load items with content if not provided
    if items_with_content is None:
        items_with_content = step_manager.load_step_data(AnalysisStep.SCRAPE_CONTENT)
        if items_with_content is None:
            logger.error("No content available. Please run step 2 first.")
            print("❌ 没有可用的内容，请先执行步骤 2")
            return None

    if step_manager.analysis_type == AnalysisType.SERVICES:
        logger.info("Executing Step 3: Analyzing services with LLM")
        print(f"\n🤖 步骤 3: 使用 LLM 分析服务内容（最大并发数: {Config.MAX_CONCURRENT_LLM_REQUESTS}）")

        llm_analyzer = LLMAnalyzer(analysis_type="service")
        analyzed_items = await llm_analyzer.analyze_all_services(items_with_content)
        item_type = "服务"

    elif step_manager.analysis_type == AnalysisType.PROGRAMS:
        logger.info("Executing Step 3: Analyzing programs with LLM")
        print(f"\n🤖 步骤 3: 使用 LLM 分析项目内容（最大并发数: {Config.MAX_CONCURRENT_LLM_REQUESTS}）")

        llm_analyzer = LLMAnalyzer(analysis_type="program")
        analyzed_items = await llm_analyzer.analyze_all_programs(items_with_content)
        item_type = "项目"

    elif step_manager.analysis_type == AnalysisType.TRAININGS:
        logger.info("Executing Step 3: Analyzing trainings with LLM")
        print(f"\n🤖 步骤 3: 使用 LLM 分析培训内容（最大并发数: {Config.MAX_CONCURRENT_LLM_REQUESTS}）")

        llm_analyzer = LLMAnalyzer(analysis_type="training")
        analyzed_items = await llm_analyzer.analyze_all_items(items_with_content)
        item_type = "培训"

    elif step_manager.analysis_type == AnalysisType.ALL_RESOURCES_TOOLS:
        logger.info("Executing Step 3: Analyzing all resources and tools with LLM")
        print(f"\n🤖 步骤 3: 使用 LLM 分析所有资源和工具内容（最大并发数: {Config.MAX_CONCURRENT_LLM_REQUESTS}）")

        llm_analyzer = LLMAnalyzer(analysis_type="all_resources_tools")
        analyzed_items = await llm_analyzer.analyze_all_items(items_with_content)
        item_type = "所有资源和工具"

    elif step_manager.analysis_type == AnalysisType.BOTH:
        logger.info("Executing Step 3: Analyzing both services and programs with LLM")
        print(f"\n🤖 步骤 3: 使用 LLM 分析服务和项目内容（最大并发数: {Config.MAX_CONCURRENT_LLM_REQUESTS}）")

        # Separate services and programs based on URL patterns
        services = []
        programs = []

        for item in items_with_content:
            if '/services/' in item.get('url', ''):
                services.append(item)
            elif '/programs/' in item.get('url', ''):
                programs.append(item)
            else:
                # Default to services if unclear
                services.append(item)

        analyzed_items = []

        # Analyze services
        if services:
            services_analyzer = LLMAnalyzer(analysis_type="service")
            analyzed_services = await services_analyzer.analyze_all_services(services)
            if analyzed_services:
                analyzed_items.extend(analyzed_services)

        # Analyze programs
        if programs:
            programs_analyzer = LLMAnalyzer(analysis_type="program")
            analyzed_programs = await programs_analyzer.analyze_all_programs(programs)
            if analyzed_programs:
                analyzed_items.extend(analyzed_programs)

        item_type = "服务和项目"
    else:
        logger.error(f"Unsupported analysis type: {step_manager.analysis_type}")
        return None

    logger.info(f"Analyzed {len(analyzed_items)} {item_type}")
    print(f"✅ 步骤 3 完成：分析了 {len(analyzed_items)} 个{item_type}")

    # Save step state
    step_manager.save_state(AnalysisStep.ANALYZE_LLM, analyzed_items,
                           {"item_count": len(analyzed_items), "item_type": item_type})

    return analyzed_items

async def execute_step_graph_analysis(step_manager: StepManager,
                                    analyzed_services: Optional[List[Dict]] = None) -> Optional[Dict]:
    """Execute step 4: Graph and Chain Analysis (optional)"""
    logger = logging.getLogger(__name__)
    
    if not (Config.ENABLE_GRAPH_ANALYSIS or Config.ENABLE_CHAIN_PROCESSING):
        logger.info("Graph analysis disabled, skipping step 4")
        print("⏭️  步骤 4: 图形分析已禁用，跳过")
        return None
    
    # Load analyzed services if not provided
    if analyzed_services is None:
        analyzed_services = step_manager.load_step_data(AnalysisStep.ANALYZE_LLM)
        if analyzed_services is None:
            logger.error("No analyzed services available. Please run step 3 first.")
            print("❌ 没有可用的分析结果，请先执行步骤 3")
            return None
    
    logger.info("Executing Step 4: Graph and Chain Analysis")
    print("\n📊 步骤 4: 图形和链式分析")
    
    graph_processor = GraphProcessor()
    chain_processor = ChainProcessor()
    
    analysis_results = {}
    
    if Config.ENABLE_GRAPH_ANALYSIS:
        logger.info("Building service graph...")
        graph_data = await graph_processor.build_service_graph(analyzed_services)
        if graph_data:
            analysis_results['graph'] = graph_data
    
    if Config.ENABLE_CHAIN_PROCESSING:
        logger.info("Creating analysis chains...")
        chain_data = await chain_processor.create_analysis_chain(analyzed_services)
        if chain_data:
            analysis_results['chains'] = chain_data
    
    if analysis_results:
        print(f"✅ 步骤 4 完成：生成了图形和链式分析结果")
        step_manager.save_state(AnalysisStep.GRAPH_ANALYSIS, analysis_results)
    else:
        print("⚠️  步骤 4: 没有生成分析结果")
    
    return analysis_results if analysis_results else None

async def execute_step_save_results(step_manager: StepManager,
                                  analyzed_items: Optional[List[Dict]] = None) -> bool:
    """Execute step 5: Save final results"""
    logger = logging.getLogger(__name__)

    # Load analyzed items if not provided
    if analyzed_items is None:
        analyzed_items = step_manager.load_step_data(AnalysisStep.ANALYZE_LLM)
        if analyzed_items is None:
            logger.error("No analyzed items available. Please run step 3 first.")
            print("❌ 没有可用的分析结果，请先执行步骤 3")
            return False

    # Determine output file and item type based on analysis type
    if step_manager.analysis_type == AnalysisType.SERVICES:
        output_file = Config.SERVICES_OUTPUT_FILE
        item_type = "服务"
    elif step_manager.analysis_type == AnalysisType.PROGRAMS:
        output_file = Config.PROGRAMS_OUTPUT_FILE
        item_type = "项目"
    elif step_manager.analysis_type == AnalysisType.TRAININGS:
        output_file = Config.TRAININGS_OUTPUT_FILE
        item_type = "培训"
    elif step_manager.analysis_type == AnalysisType.RESOURCES:
        output_file = Config.RESOURCES_OUTPUT_FILE
        item_type = "资源"
    elif step_manager.analysis_type == AnalysisType.ALL_RESOURCES_TOOLS:
        output_file = Config.ALL_RESOURCES_TOOLS_OUTPUT_FILE
        item_type = "所有资源和工具"
    elif step_manager.analysis_type == AnalysisType.BOTH:
        # Handle both types separately
        services = [item for item in analyzed_items if '/services/' in item.get('url', '')]
        programs = [item for item in analyzed_items if '/programs/' in item.get('url', '')]

        data_manager = DataManager()
        success = True

        # Save services
        if services:
            if data_manager.save_services_data(services):
                logger.info(f"Successfully saved {len(services)} services")
                print(f"✅ 成功保存 {len(services)} 个服务到 {Config.OUTPUT_FILE}")
            else:
                success = False

        # Save programs
        if programs:
            programs_file = Config.PROGRAMS_OUTPUT_FILE
            # Note: We'll need to implement save_programs_data in DataManager
            try:
                import json
                import os
                from datetime import datetime

                programs_data = {
                    "metadata": {
                        "generated_at": datetime.now().isoformat(),
                        "total_programs": len(programs),
                        "source": "CISA.gov Programs",
                        "analyzer_version": "1.0.0",
                        "analysis_type": "programs"
                    },
                    "programs": programs
                }

                os.makedirs(os.path.dirname(programs_file), exist_ok=True)
                with open(programs_file, 'w', encoding='utf-8') as f:
                    json.dump(programs_data, f, ensure_ascii=False, indent=2)

                logger.info(f"Successfully saved {len(programs)} programs")
                print(f"✅ 成功保存 {len(programs)} 个项目到 {programs_file}")
            except Exception as e:
                logger.error(f"Failed to save programs: {e}")
                success = False

        # Save step state
        step_manager.save_state(AnalysisStep.SAVE_RESULTS, None,
                               {"services_count": len(services), "programs_count": len(programs)})
        return success
    else:
        logger.error(f"Unsupported analysis type: {step_manager.analysis_type}")
        return False

    logger.info(f"Executing Step 5: Saving final results to {output_file}")
    print(f"\n💾 步骤 5: 验证和保存最终数据到 {output_file}")

    data_manager = DataManager()

    # Merge with existing data if not force_update
    final_items = analyzed_items
    if not step_manager.force_update:
        # Get all scraped items to find skipped ones
        scraped_items = step_manager.load_step_data(AnalysisStep.SCRAPE_SERVICES)
        if scraped_items:
            _, skipped_items = data_manager.filter_items_for_analysis(
                scraped_items, step_manager.analysis_type.value, step_manager.force_update
            )

            # Merge analyzed items with skipped items
            final_items = data_manager.merge_analyzed_data(
                analyzed_items, skipped_items, step_manager.analysis_type.value
            )

    # Validate and save data based on type
    if step_manager.analysis_type == AnalysisType.SERVICES:
        if data_manager.validate_services_data(final_items):
            logger.info("Data validation passed")
            print("✅ 数据验证通过")
        else:
            logger.warning("Data validation failed, but continuing with save")
            print("⚠️  数据验证失败，但继续保存")

        success = data_manager.save_services_data(final_items)

        if success:
            # Generate and save summary report for services
            data_manager.save_summary_report(final_items)
            if not step_manager.force_update and len(final_items) > len(analyzed_items):
                print(f"📊 合并保存了 {len(final_items)} 个{item_type}（新分析 {len(analyzed_items)} 个）")
            print("📊 生成并保存摘要报告")
    elif step_manager.analysis_type == AnalysisType.RESOURCES:
        # Use the specialized save method for resources
        success = data_manager.save_resources_data(final_items)
    elif step_manager.analysis_type == AnalysisType.ALL_RESOURCES_TOOLS:
        # Use the specialized save method for all_resources_tools
        success = data_manager.save_all_resources_tools_data(final_items)
    elif step_manager.analysis_type == AnalysisType.TRAININGS:
        # For trainings, use the generic save method with proper structure
        try:
            import json
            import os
            from datetime import datetime

            trainings_data = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "total_trainings": len(final_items),
                    "source": "CISA.gov Trainings",
                    "analyzer_version": "1.0.0",
                    "analysis_type": "trainings",
                    "force_update": step_manager.force_update,
                    "analyzed_count": len(analyzed_items),
                    "total_count": len(final_items)
                },
                "trainings": final_items
            }

            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(trainings_data, f, ensure_ascii=False, indent=2)

            success = True
        except Exception as e:
            logger.error(f"Failed to save trainings: {e}")
            success = False
    else:  # PROGRAMS
        # For programs, create a simple save mechanism
        try:
            import json
            import os
            from datetime import datetime

            programs_data = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "total_programs": len(final_items),
                    "source": "CISA.gov Programs",
                    "analyzer_version": "1.0.0",
                    "analysis_type": "programs",
                    "force_update": step_manager.force_update,
                    "analyzed_count": len(analyzed_items),
                    "total_count": len(final_items)
                },
                "programs": final_items
            }

            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(programs_data, f, ensure_ascii=False, indent=2)

            success = True
        except Exception as e:
            logger.error(f"Failed to save programs: {e}")
            success = False

    if success:
        logger.info(f"Successfully saved {len(analyzed_items)} {item_type}")
        print(f"✅ 成功保存 {len(analyzed_items)} 个{item_type}到 {output_file}")
    else:
        logger.error("Failed to save final results")
        print("❌ 保存最终结果失败")
        return False

    # Save step state
    step_manager.save_state(AnalysisStep.SAVE_RESULTS, None,
                           {"output_file": output_file, "item_count": len(analyzed_items), "item_type": item_type})

    return True

async def execute_step_export_markdown(step_manager: StepManager,
                                     analyzed_services: Optional[List[Dict]] = None) -> bool:
    """Execute step 6: Export to Markdown table"""
    logger = logging.getLogger(__name__)
    
    # Load analyzed services if not provided
    if analyzed_services is None:
        # Try to load from final output file first
        data_manager = DataManager()
        analyzed_services = data_manager.load_services_data()
        
        # If not available, try to load from step data
        if not analyzed_services:
            analyzed_services = step_manager.load_step_data(AnalysisStep.ANALYZE_LLM)
        
        if not analyzed_services:
            logger.error("No analyzed services available. Please run previous steps first.")
            print("❌ 没有可用的分析结果，请先执行前面的步骤")
            return False
    
    logger.info("Executing Step 6: Exporting to Markdown table")
    print("\n📝 步骤 6: 导出 Markdown 表格")
    
    markdown_exporter = MarkdownExporter()
    
    # Export to markdown
    output_file = Config.SERVICES_OUTPUT_FILE.replace('.json', '_table.md')
    success = markdown_exporter.export_to_markdown(
        Config.SERVICES_OUTPUT_FILE,
        output_file,
        max_description_length=300
    )
    
    if success:
        logger.info(f"Successfully exported to {output_file}")
        print(f"✅ 成功导出 Markdown 表格到 {output_file}")
        
        # Save step state
        step_manager.save_state(AnalysisStep.EXPORT_MARKDOWN, None,
                               {"output_file": output_file, "service_count": len(analyzed_services)})
        return True
    else:
        logger.error("Failed to export to Markdown")
        print("❌ 导出 Markdown 表格失败")
        return False

async def execute_single_step(step: AnalysisStep, step_manager: StepManager) -> bool:
    """Execute a single analysis step"""
    try:
        if step == AnalysisStep.SCRAPE_SERVICES:
            result = await execute_step_scrape_items(step_manager)
            return result is not None

        elif step == AnalysisStep.SCRAPE_CONTENT:
            result = await execute_step_scrape_content(step_manager)
            return result is not None

        elif step == AnalysisStep.ANALYZE_LLM:
            result = await execute_step_analyze_llm(step_manager)
            return result is not None

        elif step == AnalysisStep.GRAPH_ANALYSIS:
            result = await execute_step_graph_analysis(step_manager)
            return True  # This step is optional, so always return True

        elif step == AnalysisStep.SAVE_RESULTS:
            return await execute_step_save_results(step_manager)

        elif step == AnalysisStep.EXPORT_MARKDOWN:
            return await execute_step_export_markdown(step_manager)

        else:
            print(f"❌ 未知步骤: {step}")
            return False

    except Exception as e:
        logging.getLogger(__name__).error(f"Error executing step {step}: {e}", exc_info=True)
        print(f"❌ 执行步骤 {step.value} 时出错: {e}")
        return False

async def execute_from_step(start_step: AnalysisStep, step_manager: StepManager) -> bool:
    """Execute analysis starting from a specific step"""
    logger = logging.getLogger(__name__)

    steps = list(AnalysisStep)
    start_index = steps.index(start_step)

    logger.info(f"Starting execution from step: {start_step.value}")
    print(f"\n🚀 从步骤 {step_manager.step_descriptions[start_step]} 开始执行")

    for i in range(start_index, len(steps)):
        current_step = steps[i]

        print(f"\n{'='*60}")
        print(f"执行步骤 {i+1}/{len(steps)}: {step_manager.step_descriptions[current_step]}")
        print(f"{'='*60}")

        success = await execute_single_step(current_step, step_manager)

        if not success:
            logger.error(f"Step {current_step.value} failed")
            print(f"❌ 步骤 {current_step.value} 执行失败")
            return False

    logger.info("All steps completed successfully")
    print("\n🎉 所有步骤执行完成！")
    return True

def show_step_menu(step_manager: StepManager) -> Optional[AnalysisStep]:
    """Show step selection menu"""
    print("\n" + "="*60)
    print("选择要执行的步骤:")
    print("="*60)

    steps = list(AnalysisStep)
    completed_steps = step_manager.get_completed_steps()

    for i, step in enumerate(steps):
        status = "✅" if step in completed_steps else "⏸️"
        can_resume = step_manager.can_resume_from_step(step)
        resume_text = "" if can_resume else " (需要前置步骤)"

        print(f"  {i+1}. {status} {step_manager.step_descriptions[step]}{resume_text}")

    print(f"  {len(steps)+1}. 🔄 从当前状态继续")
    print(f"  {len(steps)+2}. 📊 显示状态")
    print(f"  {len(steps)+3}. 🗑️  清除状态")
    print(f"  0. 退出")
    print("="*60)

    try:
        choice = int(input("请选择 (0-{}): ".format(len(steps)+3)))

        if choice == 0:
            return None
        elif 1 <= choice <= len(steps):
            selected_step = steps[choice-1]
            if not step_manager.can_resume_from_step(selected_step):
                print("❌ 无法从此步骤开始，缺少前置步骤数据")
                return show_step_menu(step_manager)
            return selected_step
        elif choice == len(steps)+1:
            # Continue from current state
            state = step_manager.load_state()
            if state:
                current_step = AnalysisStep(state['current_step'])
                next_step = step_manager.get_next_step(current_step)
                if next_step:
                    return next_step
                else:
                    print("✅ 所有步骤已完成")
                    return None
            else:
                print("📝 没有保存的状态，从第一步开始")
                return steps[0]
        elif choice == len(steps)+2:
            # Show status
            step_manager.print_status()
            return show_step_menu(step_manager)
        elif choice == len(steps)+3:
            # Clear state
            confirm = input("确认清除所有状态? (y/N): ").strip().lower()
            if confirm == 'y':
                step_manager.clear_state()
                print("✅ 状态已清除")
            return show_step_menu(step_manager)
        else:
            print("❌ 无效选择")
            return show_step_menu(step_manager)

    except (ValueError, KeyboardInterrupt):
        print("\n👋 退出")
        return None

async def main(analysis_type: str = "services", from_step: Optional[str] = None, force_update: bool = False):
    """Main execution function for step-by-step analysis"""
    logger = setup_logging()

    try:
        # Validate configuration
        Config.validate()
        logger.info(f"Configuration validated. Using LLM provider: {Config.LLM_PROVIDER}")

        # Parse analysis type
        analysis_type_enum = StepManager.get_analysis_type_by_name(analysis_type)
        if analysis_type_enum is None:
            logger.error(f"Invalid analysis type: {analysis_type}")
            print(f"❌ 无效的分析类型: {analysis_type}")
            return False

        # Initialize step manager with analysis type and force_update flag
        step_manager = StepManager(analysis_type_enum, force_update=force_update)

        print(f"🔧 CISA Analyzer - 分步骤执行模式 ({analysis_type.upper()})")
        if force_update:
            print("🔄 强制更新模式：将重新分析所有项目")

        # If from_step is specified, start from that step
        if from_step:
            from_step_enum = StepManager.get_step_by_name(from_step)
            if from_step_enum is None:
                logger.error(f"Invalid step name: {from_step}")
                print(f"❌ 无效的步骤名称: {from_step}")
                return False

            print(f"🚀 从指定步骤开始: {step_manager.step_descriptions[from_step_enum]}")
            success = await execute_from_step(from_step_enum, step_manager)
        else:
            # Show current status
            step_manager.print_status()

            # Show menu and get user choice
            selected_step = show_step_menu(step_manager)

            if selected_step is None:
                print("👋 退出程序")
                return True

            # Execute from selected step
            success = await execute_from_step(selected_step, step_manager)

        if success:
            print("\n✅ 执行完成！")
            step_manager.print_status()
        else:
            print("\n❌ 执行失败，请检查日志")

        return success

    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        print("\n👋 用户中断程序")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        print(f"\n❌ 意外错误: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
