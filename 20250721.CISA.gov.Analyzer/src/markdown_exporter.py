"""
Markdown Exporter Module
Converts JSON data to Markdown table format with Chinese headers
"""
import json
import os
import logging
from datetime import datetime
from typing import List, Dict, Optional, Set, Tuple, Any
from config import Config
from field_standardizer import FieldStandardizer, BackwardCompatibilityWrapper

class MarkdownExporter:
    """Exports JSON data to Markdown table format"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Initialize field standardizer
        self.field_standardizer = FieldStandardizer()
        self.compatibility_wrapper = BackwardCompatibilityWrapper()

        # Initialize data type configurations
        self._init_data_type_configs()

        # Cache for field analysis to improve performance
        self._field_cache: Dict[str, Set[str]] = {}

    def _init_data_type_configs(self) -> None:
        """Initialize data type configurations in a structured way"""
        # Standard field mappings from English to Chinese (for display)
        self.field_mappings = {
            'cisa_official_name': 'CISA官方名称',
            'url': '链接地址',
            'description': '描述',  # Will be dynamically updated based on data type
            'publication_date': '发布日期',
            'domain': '对应领域',
            'target_audience': '目标受众',
            'is_cybersecurity_related': '是否网络安全相关',
            'is_cisa_official': '是否CISA官方提供'
        }

        # Unified data type configurations with standardized English fields
        # Note: indicators are used for fallback detection when filename/structure detection fails
        self.data_type_configs = {
            'services': {
                'default_fields': [
                    'cisa_official_name', 'domain', 'target_audience',
                    'description', 'is_cybersecurity_related',
                    'is_cisa_official', 'url'
                ],
                'title': 'CISA 服务列表',
                'indicators': ['description', 'service_description'],  # Include legacy field
                'description_fields': ['description']
            },
            'groups': {
                'default_fields': [
                    'cisa_official_name', 'domain', 'target_audience',
                    'description', 'is_cybersecurity_related', 'is_cisa_official', 'url'
                ],
                'title': 'CISA 组织列表',
                'indicators': ['description', '组织简介'],  # Include legacy field
                'description_fields': ['description']
            },
            'programs': {
                'default_fields': [
                    'cisa_official_name', 'domain', 'target_audience',
                    'description', 'is_cybersecurity_related', 'is_cisa_official', 'url'
                ],
                'title': 'CISA 项目列表',
                'indicators': ['description', '项目简介', 'program_type'],  # Include legacy fields
                'description_fields': ['description']
            },
            'resources': {
                'default_fields': [
                    'cisa_official_name', 'domain', 'target_audience',
                    'description', 'is_cybersecurity_related', 'is_cisa_official', 'url'
                ],
                'title': 'CISA 资源列表',
                'indicators': ['description', '资源简介', 'resource_type'],  # Include legacy fields
                'description_fields': ['description']
            },
            'trainings': {
                'default_fields': [
                    'cisa_official_name', 'domain', 'target_audience',
                    'description', 'is_cybersecurity_related',
                    'is_cisa_official', 'url'
                ],
                'title': 'CISA 培训列表',
                'indicators': ['description', 'training_description', 'training_type'],  # Include legacy fields
                'description_fields': ['description']
            }
        }

        # Backward compatibility properties
        self.default_fields = self.data_type_configs['services']['default_fields']
        self.groups_default_fields = self.data_type_configs['groups']['default_fields']
        self.programs_default_fields = self.data_type_configs['programs']['default_fields']
        self.resources_default_fields = self.data_type_configs['resources']['default_fields']
        self.trainings_default_fields = self.data_type_configs['trainings']['default_fields']
    
    def load_json_data(self, file_path: str) -> Optional[List[Dict]]:
        """Load JSON data from file and store structure for type detection"""
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"File not found: {file_path}")
                return None

            self.logger.debug(f"Loading JSON data from: {file_path}")

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Store the full JSON data and file path for structure-based detection
            self._current_json_data = data
            self._current_file_path = file_path

            self.logger.debug(f"JSON data type: {type(data)}")

            # Handle both direct list and structured format
            if isinstance(data, list):
                items = data
                self.logger.debug("Data is a direct list")
            elif isinstance(data, dict):
                self.logger.debug(f"Data is a dict with keys: {list(data.keys())}")

                # Try different keys for different data types
                if 'services' in data:
                    items = data['services']
                    self.logger.debug("Found 'services' key")
                elif 'groups' in data:
                    items = data['groups']
                    self.logger.debug("Found 'groups' key")
                elif 'programs' in data:
                    items = data['programs']
                    self.logger.debug("Found 'programs' key")
                elif 'trainings' in data:
                    items = data['trainings']
                    self.logger.debug("Found 'trainings' key")
                elif 'resources' in data:
                    items = data['resources']
                    self.logger.debug("Found 'resources' key")
                else:
                    available_keys = list(data.keys())
                    self.logger.error(f"Invalid JSON format: expected list or object with 'services', 'groups', 'programs', 'trainings', or 'resources' key. Available keys: {available_keys}")
                    return None
            else:
                self.logger.error(f"Invalid JSON format: expected list or object, got {type(data)}")
                return None

            if not isinstance(items, list):
                self.logger.error(f"Data items should be a list, got {type(items)}")
                return None

            self.logger.info(f"Successfully loaded {len(items)} items from {file_path}")
            return items

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON decode error in {file_path}: {e}")
            return None
        except UnicodeDecodeError as e:
            self.logger.error(f"Unicode decode error in {file_path}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Failed to load JSON data from {file_path}: {e}")
            return None
    
    def format_cell_value(self, value: Any) -> str:
        """Format cell value for Markdown table with enhanced type support"""
        try:
            if value is None:
                return "N/A"
            elif isinstance(value, bool):
                return "是" if value else "否"
            elif isinstance(value, str):
                # Escape pipe characters and newlines for Markdown table
                formatted = value.replace('|', '\\|').replace('\n', ' ').replace('\r', ' ').strip()
                # Limit extremely long strings to prevent table formatting issues
                if len(formatted) > 1000:
                    formatted = formatted[:997] + "..."
                return formatted
            elif isinstance(value, (int, float)):
                return str(value)
            elif isinstance(value, list):
                # Handle list values by joining with commas
                if not value:
                    return "N/A"
                # Recursively format list items and join
                formatted_items = [self.format_cell_value(item) for item in value[:5]]  # Limit to first 5 items
                result = ", ".join(formatted_items)
                if len(value) > 5:
                    result += f" (共{len(value)}项)"
                return result
            elif isinstance(value, dict):
                # Handle dict values by showing key count or specific keys
                if not value:
                    return "N/A"
                # For small dicts, show key-value pairs; for large ones, show summary
                if len(value) <= 3:
                    items = [f"{k}: {self.format_cell_value(v)}" for k, v in value.items()]
                    return "; ".join(items)
                else:
                    return f"对象 ({len(value)} 个属性)"
            elif hasattr(value, 'isoformat'):  # datetime objects
                return value.isoformat()
            else:
                # Fallback for other types
                str_value = str(value)
                # Escape and limit length
                formatted = str_value.replace('|', '\\|').replace('\n', ' ').replace('\r', ' ').strip()
                if len(formatted) > 100:
                    formatted = formatted[:97] + "..."
                return formatted
        except Exception as e:
            # Robust error handling - log the error but don't break the export
            self.logger.warning(f"Error formatting cell value {type(value)}: {e}")
            return f"格式化错误: {type(value).__name__}"
    
    def auto_detect_data_type(self, services: List[Dict], file_path: str = None) -> str:
        """Auto-detect data type based on field names, file name, and data structure"""
        if not services:
            return "services"  # Default fallback

        self.logger.debug(f"Starting data type detection with file_path: {file_path}")

        # First try: detect from file name if available
        if file_path:
            detected_from_filename = self._detect_type_from_filename(file_path)
            if detected_from_filename:
                self.logger.info(f"Data type detected from filename: {detected_from_filename}")
                return detected_from_filename

        # Second try: detect from data structure (JSON keys)
        detected_from_structure = self._detect_type_from_data_structure()
        if detected_from_structure:
            self.logger.info(f"Data type detected from data structure: {detected_from_structure}")
            return detected_from_structure

        # Third try: detect from field patterns (original logic with improvements)
        sample_services = services[:min(3, len(services))]
        sample_fields = set()
        for service in sample_services:
            sample_fields.update(service.keys())

        self.logger.debug(f"Auto-detecting data type from {len(sample_services)} sample items")
        self.logger.debug(f"Sample fields: {sorted(sample_fields)}")

        # Use improved indicators for better detection
        detection_scores = {}
        for data_type, config in self.data_type_configs.items():
            indicators = config.get('indicators', [])
            score = self._calculate_detection_score(sample_fields, data_type, indicators)
            if score > 0:
                detection_scores[data_type] = score
                self.logger.debug(f"{data_type} detection score: {score}")

        # Return the data type with the highest score
        if detection_scores:
            # Handle ties by preferring more specific types
            max_score = max(detection_scores.values())
            candidates = [dt for dt, score in detection_scores.items() if score == max_score]

            if len(candidates) == 1:
                detected_type = candidates[0]
            else:
                # Tie-breaking: prefer non-services types
                detected_type = next((dt for dt in candidates if dt != 'services'), candidates[0])
                self.logger.debug(f"Tie-breaking among {candidates}, selected: {detected_type}")

            self.logger.info(f"Auto-detected data type: {detected_type} (score: {detection_scores[detected_type]})")
            return detected_type

        # Default to services if no specific indicators found
        self.logger.warning("No specific indicators found, using default 'services' data type")
        return "services"

    def _detect_type_from_filename(self, file_path: str) -> Optional[str]:
        """Detect data type from filename"""
        if not file_path:
            return None

        filename = os.path.basename(file_path).lower()
        self.logger.debug(f"Analyzing filename: {filename}")

        # Check for specific patterns in filename
        type_patterns = {
            'services': ['service'],
            'groups': ['group'],
            'programs': ['program'],
            'resources': ['resource'],
            'trainings': ['training']
        }

        for data_type, patterns in type_patterns.items():
            if any(pattern in filename for pattern in patterns):
                self.logger.debug(f"Filename pattern match: {data_type}")
                return data_type

        return None

    def _detect_type_from_data_structure(self) -> Optional[str]:
        """Detect data type from JSON data structure keys"""
        if not hasattr(self, '_current_json_data') or not self._current_json_data:
            return None

        # Check for data type keys in JSON structure
        data_keys = ['services', 'groups', 'programs', 'resources', 'trainings']
        for key in data_keys:
            if key in self._current_json_data:
                self.logger.debug(f"Data structure key match: {key}")
                return key

        return None

    def _calculate_detection_score(self, sample_fields: Set[str], data_type: str, indicators: List[str]) -> int:
        """Calculate detection score with improved logic"""
        base_score = sum(1 for indicator in indicators if indicator in sample_fields)

        # Add bonus points for type-specific field patterns
        bonus_score = 0

        # Check for legacy field patterns that might indicate type
        legacy_patterns = {
            'services': ['service_description'],
            'groups': ['组织简介', 'group_description'],
            'programs': ['项目简介', 'program_description', 'program_type'],
            'resources': ['资源简介', 'resource_description', 'resource_type'],
            'trainings': ['training_description', 'training_type', 'course_']
        }

        if data_type in legacy_patterns:
            legacy_matches = sum(1 for pattern in legacy_patterns[data_type]
                               if any(pattern in field for field in sample_fields))
            bonus_score += legacy_matches * 2  # Higher weight for legacy patterns

        total_score = base_score + bonus_score
        self.logger.debug(f"{data_type}: base={base_score}, bonus={bonus_score}, total={total_score}")

        return total_score

    def get_default_fields_for_type(self, data_type: str) -> List[str]:
        """Get default fields based on data type using configuration"""
        config = self.data_type_configs.get(data_type, self.data_type_configs['services'])
        fields = config['default_fields']

        self.logger.debug(f"Getting default fields for data type: {data_type}")
        self.logger.debug(f"Selected default fields: {fields}")
        return fields

    def get_title_for_type(self, data_type: str) -> str:
        """Get title for data type"""
        config = self.data_type_configs.get(data_type, self.data_type_configs['services'])
        return config['title']

    def get_description_fields_for_type(self, data_type: str) -> List[str]:
        """Get description fields that should be truncated for data type"""
        config = self.data_type_configs.get(data_type, self.data_type_configs['services'])
        return config.get('description_fields', [])

    def _analyze_data_fields(self, services: List[Dict]) -> Tuple[Set[str], Dict[str, bool]]:
        """Analyze data fields once to improve performance"""
        all_fields = set()
        field_existence = {}

        # Single pass through all services to collect field information
        for service in services:
            service_fields = set(service.keys())
            all_fields.update(service_fields)

            # Track field existence across services
            for field in service_fields:
                field_existence[field] = field_existence.get(field, 0) + 1

        # Convert counts to boolean existence (field exists if present in any service)
        field_exists = {field: count > 0 for field, count in field_existence.items()}

        return all_fields, field_exists

    def _filter_available_fields(self, requested_fields: List[str],
                                field_exists: Dict[str, bool],
                                all_fields: Set[str]) -> List[str]:
        """Filter and validate requested fields against available data"""
        available_fields = []

        for field in requested_fields:
            if field_exists.get(field, False):
                available_fields.append(field)
                self.logger.debug(f"Field '{field}' is available")
            else:
                # Look for similar fields for debugging
                similar_fields = [f for f in all_fields
                                if field.lower() in f.lower() or f.lower() in field.lower()]
                if similar_fields:
                    self.logger.debug(f"Field '{field}' not found, similar fields: {similar_fields}")
                else:
                    self.logger.debug(f"Field '{field}' not found, no similar fields")

        return available_fields

    def generate_markdown_table(self, services: List[Dict],
                              fields: Optional[List[str]] = None,
                              max_description_length: int = 300) -> str:
        """Generate Markdown table from services data with improved performance"""
        if not services:
            return "No data available."

        # Standardize field names in the data
        standardized_services = self.field_standardizer.standardize_data(services)

        # Auto-detect data type and get configuration
        file_path = getattr(self, '_current_file_path', None)
        data_type = self.auto_detect_data_type(standardized_services, file_path)

        if fields is None:
            fields = self.get_default_fields_for_type(data_type)
            self.logger.info(f"Auto-detected data type: {data_type}, using fields: {fields}")

        # Analyze data fields once for better performance
        all_fields, field_exists = self._analyze_data_fields(standardized_services)
        self.logger.debug(f"All fields in data: {sorted(all_fields)}")
        self.logger.debug(f"Requested fields: {fields}")

        # Filter available fields
        available_fields = self._filter_available_fields(fields, field_exists, all_fields)
        self.logger.info(f"Available fields after filtering: {available_fields}")

        if not available_fields:
            self.logger.warning("No valid fields found in data")
            # Use all available fields as fallback
            available_fields = sorted(list(all_fields))
            self.logger.info(f"Using fallback fields: {available_fields}")

        # Get description fields for this data type
        description_fields = self.get_description_fields_for_type(data_type)

        # Generate table header with data-type-specific field mappings
        headers = []
        for field in available_fields:
            if field == 'description':
                # Use data-type-specific description label
                description_label = self.field_standardizer._get_description_label(data_type)
                headers.append(description_label)
            else:
                headers.append(self.field_mappings.get(field, field))

        header_row = "| " + " | ".join(headers) + " |"
        separator_row = "|" + "|".join([" --- " for _ in headers]) + "|"

        # Generate table rows using standardized data
        rows = []
        for service in standardized_services:
            row_values = []
            for field in available_fields:
                value = service.get(field, None)
                formatted_value = self.format_cell_value(value)

                # Truncate long descriptions based on data type configuration
                if field in description_fields and len(formatted_value) > max_description_length:
                    formatted_value = formatted_value[:max_description_length] + "..."

                row_values.append(formatted_value)

            row = "| " + " | ".join(row_values) + " |"
            rows.append(row)

        # Combine all parts
        table_lines = [header_row, separator_row] + rows
        return "\n".join(table_lines)
    
    def export_to_markdown(self, input_file: str, output_file: Optional[str] = None,
                          fields: Optional[List[str]] = None,
                          include_metadata: bool = True,
                          max_description_length: int = 300) -> bool:
        """Export JSON data to Markdown file with enhanced error handling"""
        try:
            # Load data
            services = self.load_json_data(input_file)
            if services is None:
                return False

            # Auto-detect data type for appropriate title
            data_type = self.auto_detect_data_type(services, input_file)
            title = self.get_title_for_type(data_type)

            # Generate output filename if not provided
            if output_file is None:
                base_name = os.path.splitext(input_file)[0]
                output_file = f"{base_name}_table.md"

            # Generate Markdown content
            markdown_content = []

            # Add title and metadata
            if include_metadata:
                markdown_content.append(f"# {title}")
                markdown_content.append("")
                markdown_content.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                markdown_content.append(f"**数据来源**: {input_file}")
                markdown_content.append(f"**数据类型**: {data_type}")
                markdown_content.append(f"**记录总数**: {len(services)}")
                markdown_content.append("")

            # Add table
            table = self.generate_markdown_table(services, fields, max_description_length)
            markdown_content.append(table)

            # Add footer
            if include_metadata:
                markdown_content.append("")
                markdown_content.append("---")
                markdown_content.append("*此表格由 CISA Services Analyzer 自动生成*")

            # Write to file with proper error handling
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write("\n".join(markdown_content))
            except IOError as e:
                self.logger.error(f"Failed to write to file {output_file}: {e}")
                return False

            self.logger.info(f"Successfully exported {len(services)} {data_type} to {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to export to Markdown: {e}")
            return False
    
    def get_available_fields(self, services: List[Dict]) -> List[str]:
        """Get list of available fields in the services data with caching"""
        if not services:
            return []

        # Create a cache key based on the first service's fields (simple heuristic)
        cache_key = str(sorted(services[0].keys())) if services else ""

        # Check cache first
        if cache_key in self._field_cache:
            return sorted(list(self._field_cache[cache_key]))

        # Analyze fields and cache result
        all_fields, _ = self._analyze_data_fields(services)
        self._field_cache[cache_key] = all_fields

        return sorted(list(all_fields))

    def print_field_mappings(self):
        """Print available field mappings for user interface"""
        print("可用字段映射:")
        for eng, chn in self.field_mappings.items():
            # Skip redundant Chinese to Chinese mappings in display
            if eng != chn:
                print(f"  {eng} -> {chn}")

    def clear_cache(self):
        """Clear the field analysis cache"""
        self._field_cache.clear()
        self.logger.debug("Field cache cleared")
