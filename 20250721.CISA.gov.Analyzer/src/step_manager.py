"""
Step Manager Module
Manages step-by-step execution and data persistence
"""
import json
import os
import logging
from datetime import datetime
from typing import List, Dict, Optional, Any
from enum import Enum
from config import Config

class AnalysisStep(Enum):
    """Analysis steps enumeration"""
    SCRAPE_SERVICES = "scrape_services"  # For backward compatibility, this will be renamed dynamically
    SCRAPE_CONTENT = "scrape_content"
    ANALYZE_LLM = "analyze_llm"
    GRAPH_ANALYSIS = "graph_analysis"
    SAVE_RESULTS = "save_results"
    EXPORT_MARKDOWN = "export_markdown"

class AnalysisType(Enum):
    """Analysis type enumeration"""
    SERVICES = "services"
    PROGRAMS = "programs"
    TRAININGS = "trainings"
    RESOURCES = "resources"
    ALL_RESOURCES_TOOLS = "all_resources_tools"
    BOTH = "both"

class StepManager:
    """Manages step-by-step execution and data persistence"""

    def __init__(self, analysis_type: AnalysisType = AnalysisType.SERVICES, force_update: bool = False):
        self.logger = logging.getLogger(__name__)
        self.analysis_type = analysis_type
        self.force_update = force_update

        # Set up file paths based on analysis type
        data_dir = os.path.dirname(Config.OUTPUT_FILE)
        if analysis_type == AnalysisType.SERVICES:
            self.state_file = os.path.join(data_dir, 'analysis_state_services.json')
            self.backup_dir = os.path.join(data_dir, 'backups', 'services')
        elif analysis_type == AnalysisType.PROGRAMS:
            self.state_file = os.path.join(data_dir, 'analysis_state_programs.json')
            self.backup_dir = os.path.join(data_dir, 'backups', 'programs')
        elif analysis_type == AnalysisType.TRAININGS:
            self.state_file = os.path.join(data_dir, 'analysis_state_trainings.json')
            self.backup_dir = os.path.join(data_dir, 'backups', 'trainings')
        elif analysis_type == AnalysisType.RESOURCES:
            self.state_file = os.path.join(data_dir, 'analysis_state_resources.json')
            self.backup_dir = os.path.join(data_dir, 'backups', 'resources')
        elif analysis_type == AnalysisType.ALL_RESOURCES_TOOLS:
            self.state_file = os.path.join(data_dir, 'analysis_state_all_resources_tools.json')
            self.backup_dir = os.path.join(data_dir, 'backups', 'all_resources_tools')
        else:  # BOTH - use services as default for state management
            self.state_file = os.path.join(data_dir, 'analysis_state_both.json')
            self.backup_dir = os.path.join(data_dir, 'backups', 'both')

        # Ensure backup directory exists
        os.makedirs(self.backup_dir, exist_ok=True)

        # Step descriptions (for user interface display) - dynamic based on analysis type
        self.step_descriptions = self._get_step_descriptions(analysis_type)

    def _get_step_descriptions(self, analysis_type: AnalysisType) -> Dict:
        """Get step descriptions based on analysis type"""
        if analysis_type == AnalysisType.SERVICES:
            return {
                AnalysisStep.SCRAPE_SERVICES: "抓取服务列表",
                AnalysisStep.SCRAPE_CONTENT: "抓取服务内容",
                AnalysisStep.ANALYZE_LLM: "LLM分析服务",
                AnalysisStep.GRAPH_ANALYSIS: "图形和链式分析",
                AnalysisStep.SAVE_RESULTS: "保存最终结果",
                AnalysisStep.EXPORT_MARKDOWN: "导出Markdown表格"
            }
        elif analysis_type == AnalysisType.PROGRAMS:
            return {
                AnalysisStep.SCRAPE_SERVICES: "抓取项目列表",  # Reuse enum but change description
                AnalysisStep.SCRAPE_CONTENT: "抓取项目内容",
                AnalysisStep.ANALYZE_LLM: "LLM分析项目",
                AnalysisStep.GRAPH_ANALYSIS: "图形和链式分析",
                AnalysisStep.SAVE_RESULTS: "保存最终结果",
                AnalysisStep.EXPORT_MARKDOWN: "导出Markdown表格"
            }
        elif analysis_type == AnalysisType.TRAININGS:
            return {
                AnalysisStep.SCRAPE_SERVICES: "抓取培训列表",  # Reuse enum but change description
                AnalysisStep.SCRAPE_CONTENT: "抓取培训内容",
                AnalysisStep.ANALYZE_LLM: "LLM分析培训",
                AnalysisStep.GRAPH_ANALYSIS: "图形和链式分析",
                AnalysisStep.SAVE_RESULTS: "保存最终结果",
                AnalysisStep.EXPORT_MARKDOWN: "导出Markdown表格"
            }
        elif analysis_type == AnalysisType.RESOURCES:
            return {
                AnalysisStep.SCRAPE_SERVICES: "抓取资源列表",  # Reuse enum but change description
                AnalysisStep.SCRAPE_CONTENT: "抓取资源内容",
                AnalysisStep.ANALYZE_LLM: "LLM分析资源",
                AnalysisStep.GRAPH_ANALYSIS: "图形和链式分析",
                AnalysisStep.SAVE_RESULTS: "保存最终结果",
                AnalysisStep.EXPORT_MARKDOWN: "导出Markdown表格"
            }
        elif analysis_type == AnalysisType.ALL_RESOURCES_TOOLS:
            return {
                AnalysisStep.SCRAPE_SERVICES: "抓取所有资源和工具列表",  # Reuse enum but change description
                AnalysisStep.SCRAPE_CONTENT: "抓取所有资源和工具内容",
                AnalysisStep.ANALYZE_LLM: "LLM分析所有资源和工具",
                AnalysisStep.GRAPH_ANALYSIS: "图形和链式分析",
                AnalysisStep.SAVE_RESULTS: "保存最终结果",
                AnalysisStep.EXPORT_MARKDOWN: "导出Markdown表格"
            }
        else:  # BOTH
            return {
                AnalysisStep.SCRAPE_SERVICES: "抓取服务和项目列表",
                AnalysisStep.SCRAPE_CONTENT: "抓取服务和项目内容",
                AnalysisStep.ANALYZE_LLM: "LLM分析服务和项目",
                AnalysisStep.GRAPH_ANALYSIS: "图形和链式分析",
                AnalysisStep.SAVE_RESULTS: "保存最终结果",
                AnalysisStep.EXPORT_MARKDOWN: "导出Markdown表格"
            }
    
    def save_state(self, step: AnalysisStep, data: Any, metadata: Optional[Dict] = None) -> bool:
        """Save current analysis state"""
        try:
            state = {
                "current_step": step.value,
                "analysis_type": self.analysis_type.value,
                "timestamp": datetime.now().isoformat(),
                "data_file": None,
                "metadata": metadata or {}
            }
            
            # Save data to backup file
            if data is not None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                data_filename = f"{step.value}_{timestamp}.json"
                data_file = os.path.join(self.backup_dir, data_filename)
                
                with open(data_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                state["data_file"] = data_file
                self.logger.info(f"Saved step data to {data_file}")
            
            # Save state file
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Saved analysis state for step: {step.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save state: {e}")
            return False
    
    def load_state(self) -> Optional[Dict]:
        """Load current analysis state"""
        try:
            if not os.path.exists(self.state_file):
                self.logger.info("No previous state found")
                return None
            
            with open(self.state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            self.logger.info(f"Loaded analysis state for step: {state.get('current_step')}")
            return state
            
        except Exception as e:
            self.logger.error(f"Failed to load state: {e}")
            return None
    
    def load_step_data(self, step: AnalysisStep) -> Optional[Any]:
        """Load data from a specific step"""
        try:
            state = self.load_state()
            if not state:
                return None
            
            # If current step matches, load from state
            if state.get('current_step') == step.value and state.get('data_file'):
                data_file = state['data_file']
                if os.path.exists(data_file):
                    with open(data_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    self.logger.info(f"Loaded data for step {step.value} from {data_file}")
                    return data
            
            # Otherwise, look for the most recent backup file for this step
            step_files = []
            for filename in os.listdir(self.backup_dir):
                if filename.startswith(f"{step.value}_") and filename.endswith('.json'):
                    file_path = os.path.join(self.backup_dir, filename)
                    step_files.append((file_path, os.path.getmtime(file_path)))
            
            if step_files:
                # Get the most recent file
                latest_file = max(step_files, key=lambda x: x[1])[0]
                with open(latest_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.logger.info(f"Loaded data for step {step.value} from {latest_file}")
                return data
            
            self.logger.warning(f"No data found for step: {step.value}")
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to load step data: {e}")
            return None
    
    def get_completed_steps(self) -> List[AnalysisStep]:
        """Get list of completed steps based on available backup files"""
        completed = []
        
        for step in AnalysisStep:
            # Check if backup files exist for this step
            step_files = [f for f in os.listdir(self.backup_dir) 
                         if f.startswith(f"{step.value}_") and f.endswith('.json')]
            if step_files:
                completed.append(step)
        
        return completed
    
    def get_next_step(self, current_step: Optional[AnalysisStep] = None) -> Optional[AnalysisStep]:
        """Get the next step to execute"""
        steps = list(AnalysisStep)
        
        if current_step is None:
            return steps[0]
        
        try:
            current_index = steps.index(current_step)
            if current_index < len(steps) - 1:
                return steps[current_index + 1]
            return None
        except ValueError:
            return steps[0]
    
    def can_resume_from_step(self, step: AnalysisStep) -> bool:
        """Check if we can resume from a specific step"""
        if step == AnalysisStep.SCRAPE_SERVICES:
            return True
        
        # For other steps, check if previous step data exists
        steps = list(AnalysisStep)
        try:
            step_index = steps.index(step)
            if step_index > 0:
                previous_step = steps[step_index - 1]
                return self.load_step_data(previous_step) is not None
            return True
        except ValueError:
            return False
    
    def print_status(self):
        """Print current analysis status"""
        print("\n" + "="*60)
        print(f"分析状态 ({self.analysis_type.value.upper()})")
        print("="*60)

        completed_steps = self.get_completed_steps()
        state = self.load_state()

        if state:
            print(f"分析类型: {state.get('analysis_type', self.analysis_type.value)}")
            print(f"当前步骤: {self.step_descriptions.get(AnalysisStep(state['current_step']), state['current_step'])}")
            print(f"最后更新: {state['timestamp']}")
        else:
            print(f"分析类型: {self.analysis_type.value}")
            print("当前步骤: 未开始")

        print(f"\n已完成步骤 ({len(completed_steps)}/{len(AnalysisStep)}):")
        for step in AnalysisStep:
            status = "✅" if step in completed_steps else "⏸️"
            print(f"  {status} {self.step_descriptions[step]}")

        print("="*60)

    @staticmethod
    def get_step_by_name(step_name: str) -> Optional[AnalysisStep]:
        """Get AnalysisStep enum by string name"""
        try:
            return AnalysisStep(step_name)
        except ValueError:
            return None

    @staticmethod
    def get_analysis_type_by_name(type_name: str) -> Optional[AnalysisType]:
        """Get AnalysisType enum by string name"""
        try:
            return AnalysisType(type_name)
        except ValueError:
            return None

    def clear_state(self) -> bool:
        """Clear analysis state"""
        try:
            if os.path.exists(self.state_file):
                os.remove(self.state_file)
                self.logger.info("Cleared analysis state")
            return True
        except Exception as e:
            self.logger.error(f"Failed to clear state: {e}")
            return False
