
"""
Configuration management for CISA Services Analyzer
"""
import os
from enum import Enum
from typing import Dict, Optional, Any
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class LLMProvider(Enum):
    """Supported LLM providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    QWEN = "qwen"
    DEEPSEEK = "deepseek"
    OLLAMA = "ollama"
    GEMINI = "gemini"

class Config:
    """Configuration class for CISA Services Analyzer"""

    # LLM Provider Configuration
    LLM_PROVIDER = os.getenv('LLM_PROVIDER', 'openai').lower()

    # OpenAI Configuration
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-4')
    OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL')  # For custom endpoints

    # Anthropic Configuration
    ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
    ANTHROPIC_MODEL = os.getenv('ANTHROPIC_MODEL', 'claude-3-sonnet-20240229')

    # QWEN Configuration
    QWEN_API_KEY = os.getenv('QWEN_API_KEY')
    QWEN_MODEL = os.getenv('QWEN_MODEL', 'qwen-turbo')
    QWEN_BASE_URL = os.getenv('QWEN_BASE_URL', 'https://dashscope.aliyuncs.com/api/v1')

    # DeepSeek Configuration
    DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')
    DEEPSEEK_MODEL = os.getenv('DEEPSEEK_MODEL', 'deepseek-chat')
    DEEPSEEK_BASE_URL = os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com/v1')

    # Ollama Configuration
    OLLAMA_BASE_URL = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
    OLLAMA_MODEL = os.getenv('OLLAMA_MODEL', 'llama2')

    # Gemini Configuration
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
    GEMINI_MODEL = os.getenv('GEMINI_MODEL', 'gemini-pro')

    # HTTP Proxy Configuration
    HTTP_PROXY = os.getenv('HTTP_PROXY')
    HTTPS_PROXY = os.getenv('HTTPS_PROXY')

    # CISA Website Configuration
    CISA_SERVICES_BASE_URL = os.getenv('CISA_SERVICES_BASE_URL', 'https://www.cisa.gov/resources-tools/services')
    CISA_PROGRAMS_BASE_URL = os.getenv('CISA_PROGRAMS_BASE_URL', 'https://www.cisa.gov/resources-tools/programs')
    CISA_TRAININGS_BASE_URL = os.getenv('CISA_TRAININGS_BASE_URL', 'https://www.cisa.gov/resources-tools/training')
    CISA_GROUPS_BASE_URL = os.getenv('CISA_GROUPS_BASE_URL', 'https://www.cisa.gov/resources-tools/groups')
    CISA_RESOURCES_BASE_URL = os.getenv('CISA_RESOURCES_BASE_URL', 'https://www.cisa.gov/resources-tools/resources')
    CISA_ALL_RESOURCES_TOOLS_BASE_URL = os.getenv('CISA_ALL_RESOURCES_TOOLS_BASE_URL', 'https://www.cisa.gov/resources-tools/all-resources-tools')
    # MAX_PAGES: Safety limit for maximum pages to scrape (actual page count is auto-discovered)
    MAX_PAGES = int(os.getenv('MAX_PAGES', '100'))
    MAX_SERVICES_PAGES = int(os.getenv('MAX_SERVICES_PAGES', str(MAX_PAGES)))
    MAX_TRAININGS_PAGES = int(os.getenv('MAX_TRAININGS_PAGES', str(MAX_PAGES)))
    # Backward compatibility - keep old name as alias
    MAX_TRAINING_PAGES = MAX_TRAININGS_PAGES
    MAX_PROGRAMS_PAGES = int(os.getenv('MAX_PROGRAMS_PAGES', str(MAX_PAGES)))
    MAX_GROUPS_PAGES = int(os.getenv('MAX_GROUPS_PAGES', str(MAX_PAGES)))  # Groups typically have fewer pages, use conservative default
    MAX_RESOURCES_PAGES = int(os.getenv('MAX_RESOURCES_PAGES', str(MAX_PAGES)))
    MAX_ALL_RESOURCES_TOOLS_PAGES = int(os.getenv('MAX_ALL_RESOURCES_TOOLS_PAGES', str(MAX_PAGES)))  # Default to 200 based on observed 182 pages
    REQUEST_DELAY = float(os.getenv('REQUEST_DELAY', '1.0'))

    # Output Configuration
    SERVICES_OUTPUT_FILE = os.getenv('SERVICES_OUTPUT_FILE', 'data/cisa_services.json')
    PROGRAMS_OUTPUT_FILE = os.getenv('PROGRAMS_OUTPUT_FILE', 'data/cisa_programs.json')
    TRAININGS_OUTPUT_FILE = os.getenv('TRAININGS_OUTPUT_FILE', 'data/cisa_trainings.json')
    GROUPS_OUTPUT_FILE = os.getenv('GROUPS_OUTPUT_FILE', 'data/cisa_groups.json')
    RESOURCES_OUTPUT_FILE = os.getenv('RESOURCES_OUTPUT_FILE', 'data/cisa_resources.json')
    ALL_RESOURCES_TOOLS_OUTPUT_FILE = os.getenv('ALL_RESOURCES_TOOLS_OUTPUT_FILE', 'data/cisa_all_resourcess_and_tools_info.json')

    # Backward compatibility - keep OUTPUT_FILE as alias for SERVICES_OUTPUT_FILE
    OUTPUT_FILE = os.getenv('OUTPUT_FILE', SERVICES_OUTPUT_FILE)
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')

    # Request Configuration
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', '30'))
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))

    # Concurrency Configuration
    MAX_CONCURRENT_REQUESTS = int(os.getenv('MAX_CONCURRENT_REQUESTS', '5'))
    MAX_CONCURRENT_LLM_REQUESTS = int(os.getenv('MAX_CONCURRENT_LLM_REQUESTS', '3'))

    # Graph Processing Configuration
    ENABLE_GRAPH_ANALYSIS = os.getenv('ENABLE_GRAPH_ANALYSIS', 'false').lower() == 'true'
    ENABLE_CHAIN_PROCESSING = os.getenv('ENABLE_CHAIN_PROCESSING', 'false').lower() == 'true'

    @classmethod
    def get_proxies(cls) -> Optional[Dict[str, str]]:
        """Get proxy configuration for HTTP requests"""
        if cls.HTTP_PROXY or cls.HTTPS_PROXY:
            return {
                'http://': cls.HTTP_PROXY,
                'https://': cls.HTTPS_PROXY
            }
        return None

    @classmethod
    def get_llm_config(cls) -> Dict[str, Any]:
        """Get LLM configuration based on selected provider"""
        provider = cls.LLM_PROVIDER

        if provider == LLMProvider.OPENAI.value:
            return {
                'provider': 'openai',
                'api_key': cls.OPENAI_API_KEY,
                'model': cls.OPENAI_MODEL,
                'base_url': cls.OPENAI_BASE_URL
            }
        elif provider == LLMProvider.ANTHROPIC.value:
            return {
                'provider': 'anthropic',
                'api_key': cls.ANTHROPIC_API_KEY,
                'model': cls.ANTHROPIC_MODEL
            }
        elif provider == LLMProvider.QWEN.value:
            return {
                'provider': 'qwen',
                'api_key': cls.QWEN_API_KEY,
                'model': cls.QWEN_MODEL,
                'base_url': cls.QWEN_BASE_URL
            }
        elif provider == LLMProvider.DEEPSEEK.value:
            return {
                'provider': 'deepseek',
                'api_key': cls.DEEPSEEK_API_KEY,
                'model': cls.DEEPSEEK_MODEL,
                'base_url': cls.DEEPSEEK_BASE_URL
            }
        elif provider == LLMProvider.OLLAMA.value:
            return {
                'provider': 'ollama',
                'base_url': cls.OLLAMA_BASE_URL,
                'model': cls.OLLAMA_MODEL
            }
        elif provider == LLMProvider.GEMINI.value:
            return {
                'provider': 'gemini',
                'api_key': cls.GEMINI_API_KEY,
                'model': cls.GEMINI_MODEL
            }
        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")

    @classmethod
    def validate(cls) -> bool:
        """Validate required configuration based on selected LLM provider"""
        provider = cls.LLM_PROVIDER

        if provider == LLMProvider.OPENAI.value:
            if not cls.OPENAI_API_KEY:
                raise ValueError("OPENAI_API_KEY is required when using OpenAI provider.")
        elif provider == LLMProvider.ANTHROPIC.value:
            if not cls.ANTHROPIC_API_KEY:
                raise ValueError("ANTHROPIC_API_KEY is required when using Anthropic provider.")
        elif provider == LLMProvider.QWEN.value:
            if not cls.QWEN_API_KEY:
                raise ValueError("QWEN_API_KEY is required when using QWEN provider.")
        elif provider == LLMProvider.DEEPSEEK.value:
            if not cls.DEEPSEEK_API_KEY:
                raise ValueError("DEEPSEEK_API_KEY is required when using DeepSeek provider.")
        elif provider == LLMProvider.OLLAMA.value:
            # Ollama doesn't require API key, just check if base URL is accessible
            pass
        elif provider == LLMProvider.GEMINI.value:
            if not cls.GEMINI_API_KEY:
                raise ValueError("GEMINI_API_KEY is required when using Gemini provider.")
        else:
            raise ValueError(f"Unsupported LLM provider: {provider}. Supported providers: {[p.value for p in LLMProvider]}")

        return True
