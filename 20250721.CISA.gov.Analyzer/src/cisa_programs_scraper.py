"""
CISA Programs Scraper Module
Scrapes program information from CISA website
"""
import asyncio
import logging
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
from config import Config
from base_scraper import BaseCISAScraper


class CISAProgramsScraper(BaseCISAScraper):
    """Scraper for CISA programs pages"""

    def __init__(self):
        super().__init__(Config.CISA_PROGRAMS_BASE_URL)
    
    def extract_program_info(self, program_element) -> Optional[Dict]:
        """Extract program information from a program element"""
        return self.extract_item_info(program_element, "program")

    async def scrape_page(self, page_num: int) -> List[Dict]:
        """Scrape programs from a specific page"""
        url = f"{self.base_url}?page={page_num}"
        soup = await self.get_page_content(url)

        if not soup:
            return []

        programs = []

        # Find all program items - they appear to be in h3 elements with links
        program_elements = soup.find_all('h3')

        for element in program_elements:
            # Look for the parent container that might contain additional info
            parent = element.parent
            while parent and parent.name not in ['article', 'div', 'section']:
                parent = parent.parent

            if parent:
                program_info = self.extract_program_info(parent)
                if program_info:
                    programs.append(program_info)

        self.logger.info(f"Found {len(programs)} programs on page {page_num}")
        return programs

    async def detect_total_pages(self) -> int:
        """Detect total number of pages for programs by analyzing pagination on first page"""
        try:
            self.logger.info("Discovering total number of pages for programs...")
            print(f"🔍 正在检测 Programs 总页数...")
            soup = await self.get_page_content(f"{self.base_url}?page=0")

            if not soup:
                self.logger.warning("Failed to get first page, using fallback of 5 pages")
                print(f"❌ 无法获取首页，使用回退值: 5 页")
                return 5

            # Look for "Last" page link which contains the highest page number
            last_page_link = soup.find('a', string=lambda text: text and 'last' in text.lower())
            self.logger.debug(f"Found last page link: {last_page_link}")
            if last_page_link:
                href = last_page_link.get('href', '')
                self.logger.debug(f"Last page link href: {href}")
                # Extract page number from URL like "?page=29"
                import re
                match = re.search(r'page=(\d+)', href)
                if match:
                    last_page_num = int(match.group(1))
                    total_pages = last_page_num + 1  # Convert from 0-based to count
                    self.logger.info(f"Discovered {total_pages} total pages (0-{last_page_num})")
                    print(f"✅ 通过 'Last' 链接检测到 Programs 总页数: {total_pages}")
                    return total_pages

            # Fallback: look for all page links and find the highest number
            self.logger.debug("Last page link not found, trying fallback method...")
            print(f"🔄 未找到 'Last' 链接，尝试备用方法...")
            page_links = soup.find_all('a', href=True)
            max_page = 0
            page_links_found = 0
            for link in page_links:
                href = link.get('href', '')
                if 'page=' in href:
                    page_links_found += 1
                    import re
                    match = re.search(r'page=(\d+)', href)
                    if match:
                        page_num = int(match.group(1))
                        max_page = max(max_page, page_num)

            self.logger.debug(f"Found {page_links_found} page links, max page number: {max_page}")

            if max_page > 0:
                total_pages = max_page + 1
                self.logger.info(f"Discovered {total_pages} total pages via page links (0-{max_page})")
                print(f"✅ 通过页面链接检测到 Programs 总页数: {total_pages}")
                return total_pages

            # If no pagination found, assume single page
            self.logger.info("No pagination found, assuming single page")
            print(f"ℹ️  未找到分页导航，假设只有 1 页")
            return 1

        except Exception as e:
            self.logger.error(f"Error discovering total pages: {e}")
            self.logger.warning("Using conservative estimate of 5 pages as fallback")
            print(f"❌ 检测页数时出错: {e}，使用保守估计: 5 页")
            return 5

    # Backward compatibility methods
    async def scrape_programs_page(self, page_num: int) -> List[Dict]:
        """Scrape programs from a specific page (backward compatibility)"""
        return await self.scrape_page(page_num)

    async def scrape_all_programs(self) -> List[Dict]:
        """Scrape all programs from all pages (backward compatibility)"""
        # Use the configured MAX_PROGRAMS_PAGES to ensure we scrape all pages
        max_pages = Config.MAX_PROGRAMS_PAGES
        self.logger.info(f"Starting to scrape all programs with max_pages={max_pages}")
        return await self.scrape_all_items(max_pages=max_pages)
