"""
Main Entry Point for CISA Programs Analyzer
Analyzes CISA Programs from https://www.cisa.gov/resources-tools/programs
"""
import asyncio
import sys
import os

# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from unified_main import main_programs


if __name__ == "__main__":
    success = asyncio.run(main_programs())
    sys.exit(0 if success else 1)
