"""
Content Scraper Module
Scrapes detailed content from individual service pages
"""
import httpx
import asyncio
import logging
from bs4 import BeautifulSoup
from typing import Optional, Dict
from config import Config
from concurrency_manager import ConcurrencyManager

class ContentScraper:
    """Scraper for individual service page content"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.concurrency_manager = ConcurrencyManager()

        # Headers to mimic a real browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    async def get_page_content(self, url: str, retries: int = Config.MAX_RETRIES) -> Optional[BeautifulSoup]:
        """Get page content with retry mechanism using httpx"""
        for attempt in range(retries):
            try:
                self.logger.info(f"Fetching content from: {url} (attempt {attempt + 1})")

                # Get proxy configuration
                proxies = Config.get_proxies()
                proxy_url = None
                if proxies and proxies.get('http://'):
                    proxy_url = proxies['http://']
                    self.logger.debug(f"Using proxy: {proxy_url}")
                else:
                    self.logger.debug("No proxy configured")

                async with httpx.AsyncClient(
                    headers=self.headers,
                    proxy=proxy_url,
                    timeout=Config.REQUEST_TIMEOUT
                ) as client:
                    response = await client.get(url)
                    self.logger.debug(f"Response status: {response.status_code} for {url}")
                    response.raise_for_status()

                    content_length = len(response.content)
                    self.logger.debug(f"Response content length: {content_length} bytes for {url}")

                    soup = BeautifulSoup(response.content, 'html.parser')
                    self.logger.debug(f"Successfully parsed HTML for {url}")
                    return soup

            except httpx.HTTPStatusError as e:
                self.logger.warning(f"HTTP error {e.response.status_code} for {url}: {e}")
                if attempt < retries - 1:
                    await asyncio.sleep(Config.REQUEST_DELAY * (attempt + 1))
                else:
                    self.logger.error(f"Failed to fetch {url} after {retries} attempts - HTTP {e.response.status_code}")
                    return None
            except httpx.RequestError as e:
                self.logger.warning(f"Request failed for {url}: {e}")
                if attempt < retries - 1:
                    await asyncio.sleep(Config.REQUEST_DELAY * (attempt + 1))
                else:
                    self.logger.error(f"Failed to fetch {url} after {retries} attempts - Request error: {e}")
                    return None
            except Exception as e:
                self.logger.warning(f"Unexpected error for {url}: {e}")
                if attempt < retries - 1:
                    await asyncio.sleep(Config.REQUEST_DELAY * (attempt + 1))
                else:
                    self.logger.error(f"Failed to fetch {url} after {retries} attempts - Unexpected error: {e}")
                    return None
    
    def extract_main_content(self, soup: BeautifulSoup) -> str:
        """Extract main content from the page"""
        # Remove script and style elements
        for script in soup(["script", "style", "nav", "header", "footer"]):
            script.decompose()
        
        # Try to find main content area
        main_content = None
        
        # Common selectors for main content
        content_selectors = [
            'main',
            '[role="main"]',
            '.main-content',
            '.content',
            '#main',
            '#content',
            '.page-content',
            'article'
        ]
        
        for selector in content_selectors:
            main_content = soup.select_one(selector)
            if main_content:
                break
        
        # If no main content found, use body
        if not main_content:
            main_content = soup.find('body')
        
        if not main_content:
            return soup.get_text(strip=True, separator=' ')
        
        # Extract text content
        text_content = main_content.get_text(strip=True, separator=' ')
        
        # Clean up extra whitespace
        text_content = ' '.join(text_content.split())
        
        return text_content
    
    def extract_metadata(self, soup: BeautifulSoup) -> Dict:
        """Extract metadata from the page"""
        metadata = {}
        
        # Extract title
        title_tag = soup.find('title')
        if title_tag:
            metadata['page_title'] = title_tag.get_text(strip=True)
        
        # Extract meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            metadata['meta_description'] = meta_desc.get('content', '')
        
        # Extract meta keywords
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        if meta_keywords:
            metadata['meta_keywords'] = meta_keywords.get('content', '')
        
        # Look for publication date
        date_selectors = [
            'time[datetime]',
            '.date',
            '.published',
            '.publication-date',
            '[class*="date"]'
        ]
        
        for selector in date_selectors:
            date_element = soup.select_one(selector)
            if date_element:
                if date_element.get('datetime'):
                    metadata['publication_date'] = date_element.get('datetime')
                else:
                    metadata['publication_date'] = date_element.get_text(strip=True)
                break
        
        return metadata
    
    async def scrape_service_content(self, service_info: Dict) -> Dict:
        """Scrape content from a service page"""
        url = service_info['url']
        item_name = service_info.get('cisa_official_name', 'Unknown')

        self.logger.info(f"Starting content scraping for: {item_name}")
        self.logger.debug(f"URL: {url}")

        soup = await self.get_page_content(url)
        if not soup:
            self.logger.error(f"Failed to get content for {url} - {item_name}")
            return {
                **service_info,
                'content': '',
                'metadata': {},
                'scrape_success': False
            }

        # Extract main content
        content = self.extract_main_content(soup)
        content_length = len(content) if content else 0
        self.logger.debug(f"Extracted content length: {content_length} characters for {item_name}")

        # Extract metadata
        metadata = self.extract_metadata(soup)
        self.logger.debug(f"Extracted metadata: {metadata} for {item_name}")

        # Check for publication date in content if not found in metadata
        if not metadata.get('publication_date') and content:
            import re
            date_patterns = [
                r'Published[:\s]*([A-Za-z]+ \d{1,2}, \d{4})',  # "Published: July 10, 2025"
                r'Updated[:\s]*([A-Za-z]+ \d{1,2}, \d{4})',   # "Updated: July 10, 2025"
                r'Date[:\s]*([A-Za-z]+ \d{1,2}, \d{4})',     # "Date: July 10, 2025"
                r'(\w{3}\s+\d{1,2},\s+\d{4})',              # "Jul 10, 2025"
                r'(\d{1,2}/\d{1,2}/\d{4})',                 # "07/10/2025"
                r'(\d{4}-\d{2}-\d{2})'                      # "2025-07-10"
            ]

            for pattern in date_patterns:
                match = re.search(pattern, content)
                if match:
                    metadata['publication_date'] = match.group(1)
                    self.logger.debug(f"Found publication date in content: {match.group(1)} for {item_name}")
                    break

        result = {
            **service_info,
            'content': content,
            'metadata': metadata,
            'scrape_success': True
        }

        self.logger.info(f"Successfully scraped content for: {item_name} (content: {content_length} chars)")
        return result
    
    def create_scraping_fallback(self, service_info: Dict) -> Dict:
        """Create fallback result for failed scraping"""
        return {
            **service_info,
            'content': '',
            'metadata': {},
            'scrape_success': False
        }

    async def scrape_all_service_contents(self, services: list) -> list:
        """Scrape content for all services with concurrency control"""
        # Use concurrency manager for controlled scraping
        results = await self.concurrency_manager.run_content_scraping(
            services=services,
            scraper_func=self.scrape_service_content
        )

        # Process results and handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"Error scraping service {services[i]['cisa_official_name']}: {result}")
                # Create fallback result for failed scraping
                fallback_result = self.create_scraping_fallback(services[i])
                processed_results.append(fallback_result)
            else:
                processed_results.append(result)

        return processed_results

    async def scrape_all_content(self, items: list) -> list:
        """Generic method to scrape content for all items (services or programs) with concurrency control"""
        self.logger.info(f"Starting to scrape content for {len(items)} items")
        self.logger.debug(f"ContentScraper.scrape_all_content called with {len(items)} items")

        # Use concurrency manager for controlled scraping
        results = await self.concurrency_manager.run_content_scraping(
            services=items,  # Note: parameter name is 'services' but can handle any items
            scraper_func=self.scrape_service_content  # This method works for both services and programs
        )

        # Process results and handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"Error scraping item {items[i].get('cisa_official_name', 'Unknown')}: {result}")
                # Create fallback result for failed scraping
                fallback_result = self.create_scraping_fallback(items[i])
                processed_results.append(fallback_result)
            else:
                processed_results.append(result)

        self.logger.info(f"Successfully processed {len(processed_results)} items")
        return processed_results
