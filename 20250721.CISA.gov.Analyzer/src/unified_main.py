"""
Unified Main Entry Point for CISA Analyzer
Supports both Services and Programs analysis
"""
import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import Optional, List, Dict, Literal
from enum import Enum

# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from cisa_services_scraper import CISAServicesScraper
from cisa_programs_scraper import CISAProgramsScraper
from cisa_trainings_scraper import CISATrainingsScraper
from cisa_resources_scraper import CISAResourcesScraper
from cisa_groups_scraper import CISAGroupsScraper
from cisa_all_resources_tools_scraper import CISAAllResourcesToolsScraper
from content_scraper import ContentScraper
from llm_analyzer import LLMAnalyzer
from data_manager import DataManager
from graph_processor import GraphProcessor, ChainProcessor


class AnalysisType(Enum):
    """Analysis type enumeration"""
    SERVICES = "services"
    PROGRAMS = "programs"
    TRAININGS = "trainings"
    RESOURCES = "resources"
    GROUPS = "groups"
    ALL_RESOURCES_TOOLS = "all_resources_tools"


class UnifiedCISAAnalyzer:
    """Unified analyzer for CISA services and programs"""
    
    def __init__(self, analysis_type: AnalysisType):
        self.analysis_type = analysis_type
        self.logger = logging.getLogger(__name__)
        
        # Initialize components based on analysis type
        if analysis_type == AnalysisType.SERVICES:
            self.scraper = CISAServicesScraper()
            self.llm_analyzer = LLMAnalyzer(analysis_type="service")
            self.output_file = Config.SERVICES_OUTPUT_FILE
            self.item_name = "service"
            self.item_name_plural = "services"
            self.item_name_cn = "服务"
        elif analysis_type == AnalysisType.PROGRAMS:
            self.scraper = CISAProgramsScraper()
            self.llm_analyzer = LLMAnalyzer(analysis_type="program")
            self.output_file = Config.PROGRAMS_OUTPUT_FILE
            self.item_name = "program"
            self.item_name_plural = "programs"
            self.item_name_cn = "项目"
        elif analysis_type == AnalysisType.TRAININGS:
            self.scraper = CISATrainingsScraper()
            self.llm_analyzer = LLMAnalyzer(analysis_type="training")
            self.output_file = Config.TRAININGS_OUTPUT_FILE
            self.item_name = "training"
            self.item_name_plural = "trainings"
            self.item_name_cn = "培训"
        elif analysis_type == AnalysisType.RESOURCES:
            self.scraper = CISAResourcesScraper()
            self.llm_analyzer = LLMAnalyzer(analysis_type="resource")
            self.output_file = Config.RESOURCES_OUTPUT_FILE
            self.item_name = "resource"
            self.item_name_plural = "resources"
            self.item_name_cn = "资源"
        elif analysis_type == AnalysisType.GROUPS:
            self.scraper = CISAGroupsScraper()
            self.llm_analyzer = LLMAnalyzer(analysis_type="group")
            self.output_file = Config.GROUPS_OUTPUT_FILE
            self.item_name = "group"
            self.item_name_plural = "groups"
            self.item_name_cn = "组织"
        elif analysis_type == AnalysisType.ALL_RESOURCES_TOOLS:
            self.scraper = CISAAllResourcesToolsScraper()
            self.llm_analyzer = LLMAnalyzer(analysis_type="all_resources_tools")
            self.output_file = Config.ALL_RESOURCES_TOOLS_OUTPUT_FILE
            self.item_name = "all_resources_tools"
            self.item_name_plural = "all_resources_tools"
            self.item_name_cn = "所有资源和工具"
        else:
            raise ValueError(f"Unsupported analysis type: {analysis_type}")

        self.content_scraper = ContentScraper()
        self.data_manager = DataManager()
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_level = getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO)
        
        # Create logs directory
        log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'docs', 'debug_logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # Create log filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = os.path.join(log_dir, f'cisa_{self.analysis_type.value}_{timestamp}.log')
        
        # Configure logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger.info(f"Logging initialized. Log file: {log_file}")
        return self.logger
    
    async def execute_step_scrape_items(self, force_update: bool = False) -> Optional[List[Dict]]:
        """Execute step 1: Scrape items list"""
        self.logger.info(f"Executing Step 1: Scraping {self.item_name_plural} list")
        print(f"\n🔍 步骤 1: 从 CISA 网站抓取{self.item_name_cn}列表")

        # Add debug logging for analysis type and force_update
        self.logger.info(f"Analysis type: {self.analysis_type.value}, Force update: {force_update}")

        # Get the appropriate max_pages configuration based on analysis type
        if self.analysis_type == AnalysisType.SERVICES:
            max_pages = Config.MAX_SERVICES_PAGES
        elif self.analysis_type == AnalysisType.PROGRAMS:
            max_pages = Config.MAX_PROGRAMS_PAGES
        elif self.analysis_type == AnalysisType.TRAININGS:
            max_pages = Config.MAX_TRAININGS_PAGES
        elif self.analysis_type == AnalysisType.GROUPS:
            max_pages = Config.MAX_GROUPS_PAGES
        elif self.analysis_type == AnalysisType.RESOURCES:
            max_pages = Config.MAX_RESOURCES_PAGES
        elif self.analysis_type == AnalysisType.ALL_RESOURCES_TOOLS:
            max_pages = Config.MAX_ALL_RESOURCES_TOOLS_PAGES
        else:
            max_pages = Config.MAX_PAGES

        # Debug logging for max_pages configuration
        self.logger.info(f"Using max_pages configuration: {max_pages} for analysis type: {self.analysis_type.value}")
        print(f"🔧 配置的最大页数: {max_pages}")

        items_list = await self.scraper.scrape_all_items(max_pages=max_pages)

        if not items_list:
            self.logger.error(f"No {self.item_name_plural} found")
            print(f"❌ 没有找到任何{self.item_name_cn}")
            return None

        self.logger.info(f"Found {len(items_list)} {self.item_name_plural}")
        print(f"✅ 步骤 1 完成：找到 {len(items_list)} 个{self.item_name_cn}")

        # Filter items based on existing data and force_update flag
        if not force_update:
            self.logger.info(f"Filtering items for analysis (not force_update)")
            items_to_analyze, items_to_skip = self.data_manager.filter_items_for_analysis(
                items_list, self.analysis_type.value, force_update
            )

            if items_to_skip:
                print(f"🔄 跳过 {len(items_to_skip)} 个已有正确内容的{self.item_name_cn}")
                self.logger.info(f"Skipping {len(items_to_skip)} items with valid content")

            if items_to_analyze:
                print(f"📝 需要分析 {len(items_to_analyze)} 个{self.item_name_cn}")
                self.logger.info(f"Will analyze {len(items_to_analyze)} items")
                return items_to_analyze
            else:
                print(f"✅ 所有{self.item_name_cn}都已有正确内容，无需重新分析")
                self.logger.info("All items have valid content, no analysis needed")
                return []
        else:
            print(f"🔄 强制更新模式：将重新分析所有 {len(items_list)} 个{self.item_name_cn}")
            self.logger.info(f"Force update mode: will analyze all {len(items_list)} items")
            return items_list
    
    async def execute_step_scrape_content(self, items_list: List[Dict]) -> Optional[List[Dict]]:
        """Execute step 2: Scrape content from each item page"""
        self.logger.info(f"Executing Step 2: Scraping content from {self.item_name} pages")
        print(f"\n📄 步骤 2: 抓取{self.item_name_cn}页面内容（最大并发数: {Config.MAX_CONCURRENT_REQUESTS}）")

        # Debug logging
        self.logger.debug(f"ContentScraper type: {type(self.content_scraper)}")
        self.logger.debug(f"ContentScraper methods: {[method for method in dir(self.content_scraper) if not method.startswith('_')]}")
        self.logger.debug(f"About to call scrape_all_content with {len(items_list)} items")

        items_with_content = await self.content_scraper.scrape_all_content(items_list)

        self.logger.info(f"Scraped content for {len(items_with_content)} {self.item_name_plural}")
        print(f"✅ 步骤 2 完成：抓取了 {len(items_with_content)} 个{self.item_name_cn}的内容")

        return items_with_content
    
    async def execute_step_analyze_llm(self, items_with_content: List[Dict]) -> Optional[List[Dict]]:
        """Execute step 3: Analyze content with LLM"""
        self.logger.info(f"Executing Step 3: Analyzing content with LLM")
        print(f"\n🤖 步骤 3: 使用 LLM 分析内容（最大并发数: {Config.MAX_CONCURRENT_LLM_REQUESTS}）")
        
        analyzed_items = await self.llm_analyzer.analyze_all_items(items_with_content)
        
        self.logger.info(f"Analyzed {len(analyzed_items)} {self.item_name_plural}")
        print(f"✅ 步骤 3 完成：分析了 {len(analyzed_items)} 个{self.item_name_cn}")
        
        return analyzed_items
    
    async def execute_step_save_data(self, analyzed_items: List[Dict], force_update: bool = False) -> bool:
        """Execute step 4: Save data to JSON file"""
        self.logger.info(f"Executing Step 4: Saving data to JSON file")
        print(f"\n💾 步骤 4: 保存数据到 JSON 文件")

        # If not force_update, merge with existing data
        final_items = analyzed_items
        if not force_update:
            # Get skipped items (existing valid content) without re-scraping
            # Use existing data from the data manager instead of re-scraping
            self.logger.info("Merging analyzed items with existing valid content")
            existing_items = []
            if self.analysis_type.value == 'services':
                existing_items = self.data_manager.load_services_data()
            elif self.analysis_type.value == 'programs':
                existing_items = self.data_manager.load_programs_data()
            elif self.analysis_type.value == 'groups':
                existing_items = self.data_manager.load_groups_data()
            elif self.analysis_type.value == 'resources':
                existing_items = self.data_manager.load_resources_data()
            elif self.analysis_type.value == 'trainings':
                existing_items = self.data_manager.load_trainings_data()
            elif self.analysis_type.value == 'all_resources_tools':
                existing_items = self.data_manager.load_all_resources_tools_data()

            # Find items that should be skipped (existing valid content)
            skipped_items = []
            for existing_item in existing_items:
                if self.data_manager.should_skip_item(existing_item, force_update):
                    # Check if this item is not in analyzed_items (to avoid duplicates)
                    existing_url = existing_item.get('url', '')
                    existing_name = existing_item.get('cisa_official_name', '')

                    is_duplicate = False
                    for analyzed_item in analyzed_items:
                        analyzed_url = analyzed_item.get('url', '')
                        analyzed_name = analyzed_item.get('cisa_official_name', '')
                        if (existing_url == analyzed_url) or (existing_name == analyzed_name and existing_name):
                            is_duplicate = True
                            break

                    if not is_duplicate:
                        skipped_items.append(existing_item)

            # Merge analyzed items with skipped items
            final_items = self.data_manager.merge_analyzed_data(
                analyzed_items, skipped_items, self.analysis_type.value
            )

        # Use the new generic save method with additional metadata
        additional_metadata = {
            "analysis_type": self.analysis_type.value,
            "force_update": force_update,
            "analyzed_count": len(analyzed_items),
            "total_count": len(final_items)
        }

        success = self.data_manager.save_data_generic(final_items, self.analysis_type.value, additional_metadata)

        if success:
            self.logger.info(f"Successfully saved {self.item_name_plural} data")
            if not force_update and len(final_items) > len(analyzed_items):
                print(f"✅ 步骤 4 完成：合并保存了 {len(final_items)} 个{self.item_name_cn}（新分析 {len(analyzed_items)} 个）")
            else:
                print(f"✅ 步骤 4 完成：数据已保存到 {self.output_file}")
            return True
        else:
            self.logger.error(f"Failed to save {self.item_name_plural} data")
            print(f"❌ 步骤 4 失败：无法保存数据")
            return False
    
    async def execute_step_graph_analysis(self, analyzed_items: List[Dict]) -> bool:
        """Execute step 5: Optional graph analysis"""
        if not Config.ENABLE_GRAPH_ANALYSIS:
            self.logger.info("Graph analysis disabled, skipping step 5")
            print("⏭️  步骤 5: 图形分析已禁用，跳过")
            return True
        
        self.logger.info("Executing Step 5: Graph analysis")
        print("\n📊 步骤 5: 图形分析和关系发现")
        
        try:
            graph_processor = GraphProcessor()
            chain_processor = ChainProcessor()
            
            # Perform graph analysis
            await graph_processor.analyze_relationships(analyzed_items)
            await chain_processor.analyze_chains(analyzed_items)
            
            self.logger.info("Graph analysis completed")
            print("✅ 步骤 5 完成：图形分析完成")
            return True
            
        except Exception as e:
            self.logger.error(f"Graph analysis failed: {e}")
            print(f"⚠️  步骤 5 警告：图形分析失败，但不影响主要功能: {e}")
            return True  # Don't fail the entire process
    
    async def run_full_analysis(self, force_update: bool = False) -> bool:
        """Run the complete analysis pipeline"""
        try:
            # Validate configuration
            Config.validate()
            self.logger.info(f"Configuration validated successfully. Using LLM provider: {Config.LLM_PROVIDER}")

            # Step 1: Scrape items list and filter for analysis
            items_list = await self.execute_step_scrape_items(force_update)
            if items_list is None:
                return False

            # If no items need analysis, we still need to handle skipped items
            if not items_list:
                print(f"✅ 所有{self.item_name_cn}都已有正确内容，分析完成")
                return True

            # Step 2: Scrape content from each item page
            items_with_content = await self.execute_step_scrape_content(items_list)
            if not items_with_content:
                self.logger.error(f"Failed to scrape content from {self.item_name} pages")
                print(f"❌ 无法抓取{self.item_name_cn}页面内容")
                return False

            # Step 3: Analyze content with LLM
            analyzed_items = await self.execute_step_analyze_llm(items_with_content)
            if not analyzed_items:
                self.logger.error(f"Failed to analyze {self.item_name_plural} with LLM")
                print(f"❌ 无法使用 LLM 分析{self.item_name_cn}")
                return False

            # Step 4: Save data (merge with existing data if not force_update)
            success = await self.execute_step_save_data(analyzed_items, force_update)
            if not success:
                return False

            # Step 5: Optional graph analysis
            await self.execute_step_graph_analysis(analyzed_items)

            print(f"\n🎉 分析完成！")
            print(f"📊 总共分析了 {len(analyzed_items)} 个 CISA {self.item_name_cn}")
            print(f"📁 数据已保存到: {self.output_file}")

            return True

        except Exception as e:
            self.logger.error(f"Error in main execution: {e}")
            print(f"❌ 执行过程中出现错误: {e}")
            return False


async def main_services(force_update: bool = False):
    """Main function for services analysis"""
    analyzer = UnifiedCISAAnalyzer(AnalysisType.SERVICES)
    analyzer.setup_logging()
    return await analyzer.run_full_analysis(force_update=force_update)


async def main_programs(force_update: bool = False):
    """Main function for programs analysis"""
    analyzer = UnifiedCISAAnalyzer(AnalysisType.PROGRAMS)
    analyzer.setup_logging()
    return await analyzer.run_full_analysis(force_update=force_update)


async def main_trainings(force_update: bool = False):
    """Main function for trainings analysis"""
    analyzer = UnifiedCISAAnalyzer(AnalysisType.TRAININGS)
    analyzer.setup_logging()
    return await analyzer.run_full_analysis(force_update=force_update)


async def main_groups(force_update: bool = False):
    """Main function for groups analysis"""
    analyzer = UnifiedCISAAnalyzer(AnalysisType.GROUPS)
    analyzer.setup_logging()
    return await analyzer.run_full_analysis(force_update=force_update)


async def main_all_resources_tools(force_update: bool = False):
    """Main function for all resources and tools analysis"""
    analyzer = UnifiedCISAAnalyzer(AnalysisType.ALL_RESOURCES_TOOLS)
    analyzer.setup_logging()
    return await analyzer.run_full_analysis(force_update=force_update)


if __name__ == "__main__":
    # Default to services analysis for backward compatibility
    success = asyncio.run(main_services())
    sys.exit(0 if success else 1)
