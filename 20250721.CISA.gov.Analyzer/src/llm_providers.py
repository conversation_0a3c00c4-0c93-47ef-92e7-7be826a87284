"""
LLM Providers Module
Provides unified interface for different LLM providers
"""
import logging
import httpx
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from config import Config

class LLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    async def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Generate response from LLM"""
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """Validate provider configuration"""
        pass

class OpenAIProvider(LLMProvider):
    """OpenAI LLM provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        try:
            from openai import AsyncOpenAI
            # Create httpx client with proxy configuration
            proxies = Config.get_proxies()
            proxy_url = None
            if proxies and proxies.get('http://'):
                proxy_url = proxies['http://']

            http_client = httpx.AsyncClient(proxy=proxy_url)

            self.client = AsyncOpenAI(
                api_key=config['api_key'],
                base_url=config.get('base_url'),
                http_client=http_client
            )
        except ImportError:
            raise ImportError("OpenAI package not installed. Run: pip install openai")
    
    async def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Generate response using OpenAI API"""
        try:
            response = await self.client.chat.completions.create(
                model=self.config['model'],
                messages=messages,
                temperature=kwargs.get('temperature', 0.1),
                max_tokens=kwargs.get('max_tokens', 1000)
            )
            return response.choices[0].message.content
        except Exception as e:
            self.logger.error(f"OpenAI API error: {e}")
            raise
    
    def validate_config(self) -> bool:
        """Validate OpenAI configuration"""
        return bool(self.config.get('api_key'))

class AnthropicProvider(LLMProvider):
    """Anthropic Claude LLM provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        try:
            import anthropic
            # Create httpx client with proxy configuration
            proxies = Config.get_proxies()
            proxy_url = None
            if proxies and proxies.get('http://'):
                proxy_url = proxies['http://']

            http_client = httpx.AsyncClient(proxy=proxy_url)

            self.client = anthropic.AsyncAnthropic(
                api_key=config['api_key'],
                http_client=http_client
            )
        except ImportError:
            raise ImportError("Anthropic package not installed. Run: pip install anthropic")
    
    async def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Generate response using Anthropic API"""
        try:
            # Convert messages format for Anthropic
            system_message = ""
            user_messages = []
            
            for msg in messages:
                if msg['role'] == 'system':
                    system_message = msg['content']
                else:
                    user_messages.append(msg)
            
            response = await self.client.messages.create(
                model=self.config['model'],
                system=system_message,
                messages=user_messages,
                max_tokens=kwargs.get('max_tokens', 1000),
                temperature=kwargs.get('temperature', 0.1)
            )
            return response.content[0].text
        except Exception as e:
            self.logger.error(f"Anthropic API error: {e}")
            raise
    
    def validate_config(self) -> bool:
        """Validate Anthropic configuration"""
        return bool(self.config.get('api_key'))

class QWENProvider(LLMProvider):
    """QWEN LLM provider (Alibaba Cloud)"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_url = config['base_url']
        self.api_key = config['api_key']
        self.model = config['model']
    
    async def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Generate response using QWEN API"""
        try:
            # Get proxy configuration
            proxies = Config.get_proxies()
            proxy_url = None
            if proxies and proxies.get('http://'):
                proxy_url = proxies['http://']

            async with httpx.AsyncClient(proxy=proxy_url) as client:
                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json'
                }
                
                data = {
                    'model': self.model,
                    'messages': messages,
                    'temperature': kwargs.get('temperature', 0.1),
                    'max_tokens': kwargs.get('max_tokens', 1000)
                }
                
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=Config.REQUEST_TIMEOUT
                )
                response.raise_for_status()
                
                result = response.json()
                return result['choices'][0]['message']['content']
        except Exception as e:
            self.logger.error(f"QWEN API error: {e}")
            raise
    
    def validate_config(self) -> bool:
        """Validate QWEN configuration"""
        return bool(self.config.get('api_key') and self.config.get('base_url'))

class DeepSeekProvider(LLMProvider):
    """DeepSeek LLM provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_url = config['base_url']
        self.api_key = config['api_key']
        self.model = config['model']
    
    async def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Generate response using DeepSeek API"""
        try:
            # Get proxy configuration
            proxies = Config.get_proxies()
            proxy_url = None
            if proxies and proxies.get('http://'):
                proxy_url = proxies['http://']

            async with httpx.AsyncClient(proxy=proxy_url) as client:
                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json'
                }
                
                data = {
                    'model': self.model,
                    'messages': messages,
                    'temperature': kwargs.get('temperature', 0.1),
                    'max_tokens': kwargs.get('max_tokens', 1000)
                }
                
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=Config.REQUEST_TIMEOUT
                )
                response.raise_for_status()
                
                result = response.json()
                return result['choices'][0]['message']['content']
        except Exception as e:
            self.logger.error(f"DeepSeek API error: {e}")
            raise
    
    def validate_config(self) -> bool:
        """Validate DeepSeek configuration"""
        return bool(self.config.get('api_key') and self.config.get('base_url'))

class OllamaProvider(LLMProvider):
    """Ollama local LLM provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_url = config['base_url']
        self.model = config['model']
    
    async def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Generate response using Ollama API"""
        try:
            # Create httpx client with proxy configuration
            proxies = Config.get_proxies()
            proxy_url = None
            if proxies and proxies.get('http://'):
                proxy_url = proxies['http://']

            async with httpx.AsyncClient(proxy=proxy_url) as client:
                # Convert messages to Ollama format
                prompt = self._convert_messages_to_prompt(messages)
                
                data = {
                    'model': self.model,
                    'prompt': prompt,
                    'stream': False,
                    'options': {
                        'temperature': kwargs.get('temperature', 0.1),
                        'num_predict': kwargs.get('max_tokens', 1000)
                    }
                }
                
                response = await client.post(
                    f"{self.base_url}/api/generate",
                    json=data,
                    timeout=Config.REQUEST_TIMEOUT
                )
                response.raise_for_status()
                
                result = response.json()
                return result['response']
        except Exception as e:
            self.logger.error(f"Ollama API error: {e}")
            raise
    
    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Convert chat messages to a single prompt for Ollama"""
        prompt_parts = []
        for msg in messages:
            role = msg['role']
            content = msg['content']
            if role == 'system':
                prompt_parts.append(f"System: {content}")
            elif role == 'user':
                prompt_parts.append(f"User: {content}")
            elif role == 'assistant':
                prompt_parts.append(f"Assistant: {content}")
        
        return "\n\n".join(prompt_parts) + "\n\nAssistant:"
    
    def validate_config(self) -> bool:
        """Validate Ollama configuration"""
        return bool(self.config.get('base_url') and self.config.get('model'))

class GeminiProvider(LLMProvider):
    """Google Gemini LLM provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        try:
            import google.generativeai as genai
            genai.configure(api_key=config['api_key'])
            self.model = genai.GenerativeModel(config['model'])
        except ImportError:
            raise ImportError("Google Generative AI package not installed. Run: pip install google-generativeai")
    
    async def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Generate response using Gemini API"""
        try:
            # Convert messages to Gemini format
            prompt = self._convert_messages_to_prompt(messages)
            
            response = await self.model.generate_content_async(
                prompt,
                generation_config={
                    'temperature': kwargs.get('temperature', 0.1),
                    'max_output_tokens': kwargs.get('max_tokens', 1000)
                }
            )
            return response.text
        except Exception as e:
            self.logger.error(f"Gemini API error: {e}")
            raise
    
    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Convert chat messages to a single prompt for Gemini"""
        prompt_parts = []
        for msg in messages:
            role = msg['role']
            content = msg['content']
            if role == 'system':
                prompt_parts.append(f"Instructions: {content}")
            elif role == 'user':
                prompt_parts.append(f"User: {content}")
            elif role == 'assistant':
                prompt_parts.append(f"Assistant: {content}")
        
        return "\n\n".join(prompt_parts)
    
    def validate_config(self) -> bool:
        """Validate Gemini configuration"""
        return bool(self.config.get('api_key'))

class LLMProviderFactory:
    """Factory class for creating LLM providers"""
    
    @staticmethod
    def create_provider(config: Dict[str, Any]) -> LLMProvider:
        """Create LLM provider based on configuration"""
        provider_type = config['provider']
        
        if provider_type == 'openai':
            return OpenAIProvider(config)
        elif provider_type == 'anthropic':
            return AnthropicProvider(config)
        elif provider_type == 'qwen':
            return QWENProvider(config)
        elif provider_type == 'deepseek':
            return DeepSeekProvider(config)
        elif provider_type == 'ollama':
            return OllamaProvider(config)
        elif provider_type == 'gemini':
            return GeminiProvider(config)
        else:
            raise ValueError(f"Unsupported LLM provider: {provider_type}")
