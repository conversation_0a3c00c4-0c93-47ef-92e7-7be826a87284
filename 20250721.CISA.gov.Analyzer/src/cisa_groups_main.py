"""
CISA Groups Main Module
Main entry point for CISA Groups analysis
"""
import asyncio
import sys
import os

# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from unified_main import main_groups


async def main(force_update: bool = False) -> bool:
    """
    Main function for CISA Groups analysis (backward compatibility wrapper)

    Args:
        force_update: Whether to force update all items, ignoring existing valid content

    Returns:
        bool: True if analysis completed successfully, False otherwise
    """
    return await main_groups(force_update=force_update)


if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n🎉 CISA Groups 分析完成！")
    else:
        print("\n💥 CISA Groups 分析失败")
        exit(1)
