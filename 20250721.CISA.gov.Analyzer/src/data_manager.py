"""
Data Manager Module
Handles data storage and output operations
"""
import json
import os
import logging
from datetime import datetime
from typing import List, Dict, Optional, Any
from config import Config
from field_standardizer import Field<PERSON><PERSON>dardizer, BackwardCompatibilityWrapper

class DataManager:
    """Manages data storage and output operations"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.field_standardizer = FieldStandardizer()
        self.compatibility_wrapper = BackwardCompatibilityWrapper()
        self.output_file = Config.SERVICES_OUTPUT_FILE

        # Data type configuration mapping for unified operations
        self.data_type_configs = {
            'services': {
                'output_file': Config.SERVICES_OUTPUT_FILE,
                'metadata_source': 'CISA.gov Services',
                'data_key': 'services',
                'count_key': 'total_services'
            },
            'programs': {
                'output_file': Config.PROGRAMS_OUTPUT_FILE,
                'metadata_source': 'CISA.gov Programs',
                'data_key': 'programs',
                'count_key': 'total_programs'
            },
            'trainings': {
                'output_file': Config.TRAININGS_OUTPUT_FILE,
                'metadata_source': 'CISA.gov Trainings',
                'data_key': 'trainings',
                'count_key': 'total_trainings'
            },
            'resources': {
                'output_file': Config.RESOURCES_OUTPUT_FILE,
                'metadata_source': None,  # Resources don't use source field
                'data_key': 'resources',
                'count_key': 'total_resources'
            },
            'groups': {
                'output_file': Config.GROUPS_OUTPUT_FILE,
                'metadata_source': 'CISA.gov Groups',
                'data_key': 'groups',
                'count_key': 'total_groups'
            },
            'all_resources_tools': {
                'output_file': Config.ALL_RESOURCES_TOOLS_OUTPUT_FILE,
                'metadata_source': 'CISA.gov All Resources & Tools',
                'data_key': 'all_resources_tools',
                'count_key': 'total_all_resources_tools'
            }
        }

        # Ensure output directory exists
        output_dir = os.path.dirname(self.output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            self.logger.info(f"Created output directory: {output_dir}")

    def _get_data_type_config(self, data_type: str) -> Dict[str, Any]:
        """Get configuration for a specific data type"""
        if data_type not in self.data_type_configs:
            raise ValueError(f"Unsupported data type: {data_type}")
        return self.data_type_configs[data_type]

    def _ensure_output_directory(self, output_file: str) -> None:
        """Ensure output directory exists for the given file"""
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            self.logger.info(f"Created output directory: {output_dir}")

    def save_data_generic(self, data: List[Dict], data_type: str,
                         additional_metadata: Dict = None) -> bool:
        """
        Generic method to save data for any supported type

        Args:
            data: List of data items to save
            data_type: Type of data ('services', 'programs', 'trainings', 'resources', 'groups')
            additional_metadata: Additional metadata to include

        Returns:
            bool: True if save successful, False otherwise
        """
        try:
            config = self._get_data_type_config(data_type)
            output_file = config['output_file']

            # Ensure output directory exists
            self._ensure_output_directory(output_file)

            # Create base metadata
            metadata = {
                "generated_at": datetime.now().isoformat(),
                config['count_key']: len(data),
                "analyzer_version": "1.0.0"
            }

            # Add source field if configured
            if config['metadata_source']:
                metadata["source"] = config['metadata_source']
            else:
                # For resources, use analysis_type instead
                metadata["analysis_type"] = data_type

            # Add additional metadata if provided
            if additional_metadata:
                metadata.update(additional_metadata)

            # Create output structure
            output_data = {
                "metadata": metadata,
                config['data_key']: data
            }

            # Save to file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"Successfully saved {len(data)} {data_type} to {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save {data_type} data: {e}")
            return False

    def load_data_generic(self, data_type: str) -> List[Dict]:
        """
        Generic method to load data for any supported type

        Args:
            data_type: Type of data to load

        Returns:
            List[Dict]: Loaded data items, empty list if file doesn't exist or error
        """
        try:
            config = self._get_data_type_config(data_type)
            output_file = config['output_file']

            if not os.path.exists(output_file):
                self.logger.warning(f"Output file {output_file} does not exist")
                return []

            with open(output_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            items = data.get(config['data_key'], [])
            self.logger.info(f"Loaded {len(items)} {data_type} from {output_file}")
            return items

        except Exception as e:
            self.logger.error(f"Failed to load {data_type} data: {e}")
            return []

    def save_services_data(self, services_data: List[Dict]) -> bool:
        """Save services data to JSON file (backward compatibility wrapper)"""
        return self.save_data_generic(services_data, 'services')
    
    def load_services_data(self) -> List[Dict]:
        """Load services data from JSON file (backward compatibility wrapper)"""
        return self.load_data_generic('services')
    
    def save_intermediate_data(self, data: List[Dict], filename: str) -> bool:
        """Save intermediate data for debugging/backup"""
        try:
            # Create backup directory
            backup_dir = os.path.join(os.path.dirname(self.output_file), 'backups')
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir, exist_ok=True)
            
            backup_file = os.path.join(backup_dir, filename)
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Saved intermediate data to {backup_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save intermediate data: {e}")
            return False
    
    def validate_services_data(self, services_data: List[Dict]) -> bool:
        """Validate services data structure"""
        required_fields = [
            'cisa_official_name',
            'url',
            'service_description',
            'publication_date',
            'domain',
            'target_audience',
            'is_cybersecurity_related',
            'is_cisa_official'
        ]
        
        valid_count = 0
        
        for i, service in enumerate(services_data):
            missing_fields = []
            for field in required_fields:
                if field not in service:
                    missing_fields.append(field)
            
            if missing_fields:
                self.logger.warning(f"Service {i+1} missing fields: {missing_fields}")
            else:
                valid_count += 1
        
        self.logger.info(f"Validation complete: {valid_count}/{len(services_data)} services are valid")
        return valid_count == len(services_data)
    
    def generate_summary_report(self, services_data: List[Dict]) -> Dict:
        """Generate summary report of the services data"""
        if not services_data:
            return {"error": "No services data available"}
        
        # Count by domain
        domain_counts = {}
        audience_counts = {}
        cybersecurity_count = 0
        cisa_official_count = 0
        
        for service in services_data:
            # Domain counts
            domain = service.get('domain', 'Unknown')
            domain_counts[domain] = domain_counts.get(domain, 0) + 1
            
            # Audience counts
            audience = service.get('target_audience', 'Unknown')
            audience_counts[audience] = audience_counts.get(audience, 0) + 1
            
            # Cybersecurity related count
            if service.get('is_cybersecurity_related', False):
                cybersecurity_count += 1
            
            # CISA official count
            if service.get('is_cisa_official', False):
                cisa_official_count += 1
        
        summary = {
            "total_services": len(services_data),
            "cybersecurity_related": cybersecurity_count,
            "cisa_official_services": cisa_official_count,
            "external_services": len(services_data) - cisa_official_count,
            "domains": domain_counts,
            "target_audiences": audience_counts,
            "generated_at": datetime.now().isoformat()
        }
        
        return summary
    
    def save_summary_report(self, services_data: List[Dict]) -> bool:
        """Save summary report to file"""
        try:
            summary = self.generate_summary_report(services_data)

            summary_file = self.output_file.replace('.json', '_summary.json')

            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            self.logger.info(f"Summary report saved to {summary_file}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save summary report: {e}")
            return False

    def save_data(self, data, output_file: str) -> bool:
        """Save generic data structure to specified file

        Args:
            data: Data structure to save (can be Dict with metadata or List of items)
            output_file: Path to output file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Add debug logging to understand data structure
            self.logger.info(f"save_data called with data type: {type(data)}")
            if isinstance(data, list):
                self.logger.info(f"Data is a list with {len(data)} items")
                if data:
                    self.logger.info(f"First item type: {type(data[0])}")
                    self.logger.info(f"First item keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'Not a dict'}")
            elif isinstance(data, dict):
                self.logger.info(f"Data is a dict with keys: {list(data.keys())}")

            # Ensure output directory exists
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                self.logger.info(f"Created output directory: {output_dir}")

            # Handle different data structures
            if isinstance(data, list):
                # If data is a list, wrap it in proper structure
                output_data = {
                    "metadata": {
                        "generated_at": datetime.now().isoformat(),
                        "total_items": len(data),
                        "analysis_type": "resources"  # Default for backward compatibility
                    },
                    "items": data
                }
                self.logger.info(f"Wrapped list data into structured format with {len(data)} items")
            elif isinstance(data, dict):
                # If data is already a dict, use it as-is
                output_data = data
                self.logger.info("Using provided dict structure as-is")
            else:
                raise ValueError(f"Unsupported data type: {type(data)}")

            # Save to file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            # Log success with item count if available
            metadata = output_data.get('metadata', {})
            total_items = metadata.get('total_items')
            if total_items is not None:
                self.logger.info(f"Successfully saved {total_items} items to {output_file}")
            else:
                self.logger.info(f"Successfully saved data to {output_file}")

            return True

        except Exception as e:
            self.logger.error(f"Failed to save data to {output_file}: {e}")
            self.logger.error(f"Data type was: {type(data)}")
            if hasattr(e, '__traceback__'):
                import traceback
                self.logger.error(f"Traceback: {traceback.format_exc()}")
            return False

    def save_groups_data(self, groups_data: List[Dict]) -> bool:
        """Save groups data to JSON file (backward compatibility wrapper)"""
        return self.save_data_generic(groups_data, 'groups')

    def save_programs_data(self, programs_data: List[Dict]) -> bool:
        """Save programs data to JSON file (backward compatibility wrapper)"""
        return self.save_data_generic(programs_data, 'programs')

    def save_resources_data(self, resources_data: List[Dict]) -> bool:
        """Save resources data to JSON file (backward compatibility wrapper)"""
        return self.save_data_generic(resources_data, 'resources')

    def save_trainings_data(self, trainings_data: List[Dict]) -> bool:
        """Save trainings data to JSON file (backward compatibility wrapper)"""
        # Include analysis_type for trainings to match existing format
        additional_metadata = {"analysis_type": "trainings"}
        return self.save_data_generic(trainings_data, 'trainings', additional_metadata)

    def save_all_resources_tools_data(self, all_resources_tools_data: List[Dict]) -> bool:
        """Save all resources tools data to JSON file (backward compatibility wrapper)"""
        # Include analysis_type for all_resources_tools to match existing format
        additional_metadata = {"analysis_type": "all_resources_tools"}
        return self.save_data_generic(all_resources_tools_data, 'all_resources_tools', additional_metadata)

    def load_groups_data(self) -> List[Dict]:
        """Load groups data from JSON file (backward compatibility wrapper)"""
        return self.load_data_generic('groups')

    def load_programs_data(self) -> List[Dict]:
        """Load programs data from JSON file (backward compatibility wrapper)"""
        return self.load_data_generic('programs')

    def load_resources_data(self) -> List[Dict]:
        """Load resources data from JSON file (backward compatibility wrapper)"""
        return self.load_data_generic('resources')

    def load_trainings_data(self) -> List[Dict]:
        """Load trainings data from JSON file (backward compatibility wrapper)"""
        return self.load_data_generic('trainings')

    def load_all_resources_tools_data(self) -> List[Dict]:
        """Load all resources tools data from JSON file (backward compatibility wrapper)"""
        return self.load_data_generic('all_resources_tools')

    def validate_groups_data(self, groups_data: List[Dict]) -> bool:
        """Validate groups data structure"""
        required_fields = [
            'cisa_official_name',
            'url',
            '组织简介',
            '发布日期',
            '对应领域',
            '目标受众',
            '是否网络安全相关',
            '是否CISA官方提供'
        ]

        valid_count = 0

        for i, group in enumerate(groups_data):
            missing_fields = []
            for field in required_fields:
                if field not in group:
                    missing_fields.append(field)

            if missing_fields:
                self.logger.warning(f"Group {i+1} missing fields: {missing_fields}")
            else:
                valid_count += 1

        self.logger.info(f"Validation complete: {valid_count}/{len(groups_data)} groups are valid")
        return valid_count == len(groups_data)

    def is_valid_description(self, description: str) -> bool:
        """Check if description contains valid content (not error messages)"""
        if not description or len(description.strip()) < 10:
            return False

        # Error patterns that indicate failed analysis
        error_patterns = [
            "分析失败，无法获取",
            "分析失败",
            "无法获取",
            "分析失败，无法获取描述",
            "分析失败，无法获取组织描述",
            "分析失败，无法获取项目描述",
            "分析失败，无法获取服务描述",
            "分析失败，无法获取资源描述",
            "分析失败，无法获取培训描述",
            "分析失败，无法获取工具描述",
            "分析失败，无法获取资源和工具描述",
            "无法分析内容",
            "内容分析失败",
            "获取描述失败"
        ]

        description_lower = description.lower().strip()

        # Check for exact error patterns
        for pattern in error_patterns:
            if pattern in description:
                return False

        # Check for very short or meaningless content
        meaningless_patterns = ["未知", "无", "unknown", "none", "n/a"]
        if description_lower in meaningless_patterns:
            return False

        return True

    def should_skip_item(self, existing_item: Dict, force_update: bool = False) -> bool:
        """
        Determine if an item should be skipped during analysis

        Args:
            existing_item: Existing item data from JSON
            force_update: Whether to force update all items

        Returns:
            True if item should be skipped, False if it needs analysis
        """
        if force_update:
            self.logger.debug(f"Force update mode: not skipping item")
            return False  # Never skip in force update mode

        # Check if description field exists and is valid
        description = existing_item.get('description', '')
        if not description:
            # Try alternative description field names for backward compatibility
            description = existing_item.get('service_description', '')
            if not description:
                description = existing_item.get('组织简介', '')
            if not description:
                description = existing_item.get('项目简介', '')
            if not description:
                description = existing_item.get('资源简介', '')
            if not description:
                description = existing_item.get('training_description', '')
            if not description:
                description = existing_item.get('工具描述', '')
            if not description:
                description = existing_item.get('资源和工具描述', '')

        self.logger.debug(f"Found description for item: {description[:100] if description else 'None'}...")
        is_valid = self.is_valid_description(description)
        self.logger.debug(f"Description is valid: {is_valid}")

        return is_valid

    def find_existing_item(self, items_list: List[Dict], url: str, cisa_official_name: str) -> Optional[Dict]:
        """
        Find existing item in list based on URL and cisa_official_name

        Args:
            items_list: List of existing items
            url: URL to match
            cisa_official_name: CISA official name to match

        Returns:
            Existing item if found, None otherwise
        """
        for item in items_list:
            item_url = item.get('url', '').strip()
            item_name = item.get('cisa_official_name', '').strip()

            # Match by URL (primary identifier)
            if item_url and url and item_url == url:
                return item

            # Match by name as fallback (in case URL changed)
            if item_name and cisa_official_name and item_name == cisa_official_name:
                return item

        return None

    def filter_items_for_analysis(self, new_items: List[Dict], data_type: str, force_update: bool = False) -> tuple[List[Dict], List[Dict]]:
        """
        Filter items to determine which need analysis and which can be skipped

        Args:
            new_items: List of newly scraped items
            data_type: Type of data ('services', 'programs', 'groups', 'resources', 'trainings')
            force_update: Whether to force update all items

        Returns:
            Tuple of (items_to_analyze, items_to_skip)
        """
        # Add debug logging for filter operation
        self.logger.info(f"Starting filter_items_for_analysis for data_type: {data_type}, force_update: {force_update}")
        self.logger.info(f"New items count: {len(new_items)}")

        # Load existing data
        existing_items = []
        if data_type == 'services':
            existing_items = self.load_services_data()
        elif data_type == 'programs':
            existing_items = self.load_programs_data()
        elif data_type == 'groups':
            existing_items = self.load_groups_data()
        elif data_type == 'resources':
            existing_items = self.load_resources_data()
        elif data_type == 'trainings':
            existing_items = self.load_trainings_data()
        elif data_type == 'all_resources_tools':
            existing_items = self.load_all_resources_tools_data()
        else:
            self.logger.warning(f"Unknown data_type: {data_type}, no existing data will be loaded")

        self.logger.info(f"Loaded {len(existing_items)} existing items for data_type: {data_type}")

        items_to_analyze = []
        items_to_skip = []

        for new_item in new_items:
            url = new_item.get('url', '')
            name = new_item.get('cisa_official_name', '')

            # Find existing item
            existing_item = self.find_existing_item(existing_items, url, name)

            if existing_item:
                self.logger.debug(f"Found existing item for {name}: {existing_item.keys()}")
                should_skip = self.should_skip_item(existing_item, force_update)
                self.logger.debug(f"Should skip item {name}: {should_skip}")

                if should_skip:
                    # Item exists and has valid content, skip analysis
                    items_to_skip.append(existing_item)
                    self.logger.info(f"Skipping item with valid content: {name}")
                else:
                    # Item needs analysis (invalid content)
                    items_to_analyze.append(new_item)
                    self.logger.info(f"Re-analyzing item with invalid content: {name}")
            else:
                # Item needs analysis (new item)
                items_to_analyze.append(new_item)
                self.logger.info(f"Analyzing new item: {name}")

        self.logger.info(f"Filter results: {len(items_to_analyze)} to analyze, {len(items_to_skip)} to skip")
        return items_to_analyze, items_to_skip

    def merge_analyzed_data(self, analyzed_items: List[Dict], skipped_items: List[Dict], data_type: str) -> List[Dict]:
        """
        Merge newly analyzed items with skipped items to create complete dataset

        Args:
            analyzed_items: Items that were analyzed (new or updated)
            skipped_items: Items that were skipped (existing valid content)
            data_type: Type of data for logging

        Returns:
            Complete merged dataset
        """
        # Create a dictionary for quick lookup by URL and name
        merged_dict = {}

        # Add skipped items first (existing valid content)
        for item in skipped_items:
            url = item.get('url', '')
            name = item.get('cisa_official_name', '')
            key = f"{url}|{name}"
            merged_dict[key] = item

        # Add/update with analyzed items (new or updated content)
        for item in analyzed_items:
            url = item.get('url', '')
            name = item.get('cisa_official_name', '')
            key = f"{url}|{name}"
            merged_dict[key] = item  # This will overwrite if exists

        merged_list = list(merged_dict.values())

        self.logger.info(f"Merged data: {len(analyzed_items)} analyzed + {len(skipped_items)} skipped = {len(merged_list)} total {data_type}")

        return merged_list
