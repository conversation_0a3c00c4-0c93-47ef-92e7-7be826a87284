"""
CISA All Resources & Tools Scraper Module
Scrapes all resources and tools information from CISA website
"""
import asyncio
import logging
import re
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
from urllib.parse import urljoin
from config import Config
from base_scraper import BaseCISAScraper


class CISAAllResourcesToolsScraper(BaseCISAScraper):
    """Scraper for CISA all resources & tools pages"""

    def __init__(self):
        super().__init__(Config.CISA_ALL_RESOURCES_TOOLS_BASE_URL)
    
    def extract_item_info(self, item_element) -> Optional[Dict]:
        """Extract item information from an item element"""
        try:
            # Find the main title link
            title_link = None

            # Look for h3 with a link (most common pattern)
            h3_element = item_element.find('h3')
            if h3_element:
                title_link = h3_element.find('a', href=True)

            # If not found, look for any link in the item
            if not title_link:
                title_link = item_element.find('a', href=True)

            if not title_link:
                return None

            title = title_link.get_text(strip=True)
            if not title:
                return None

            relative_url = title_link.get('href', '')
            full_url = urljoin('https://www.cisa.gov', relative_url)

            # Filter URLs: only keep resources-tools URLs
            if not full_url.startswith('https://www.cisa.gov/resources-tools/'):
                self.logger.debug(f"Skipping non-resources-tools URL: {full_url}")
                return None
            
            # Extract publication date if available
            publication_date = None
            date_patterns = [
                r'Published[:\s]*([A-Za-z]+ \d{1,2}, \d{4})',  # "Published: July 10, 2025"
                r'Updated[:\s]*([A-Za-z]+ \d{1,2}, \d{4})',   # "Updated: July 10, 2025"
                r'Date[:\s]*([A-Za-z]+ \d{1,2}, \d{4})',     # "Date: July 10, 2025"
                r'(\w{3,9}\s+\d{1,2},\s+\d{4})',            # "July 10, 2025" or "Jul 10, 2025"
                r'(\d{1,2}/\d{1,2}/\d{4})',                 # "07/10/2025"
                r'(\d{4}-\d{2}-\d{2})',                     # "2025-07-10"
                r'(\d{2}-\d{2}-\d{4})',                     # "07-10-2025"
                r'(\d{1,2}\.\d{1,2}\.\d{4})'                # "10.7.2025"
            ]

            item_text = item_element.get_text()
            self.logger.debug(f"Searching for date in text: {item_text[:200]}...")

            for i, pattern in enumerate(date_patterns):
                match = re.search(pattern, item_text, re.IGNORECASE)
                if match:
                    publication_date = match.group(1)
                    self.logger.debug(f"Found date with pattern {i}: {publication_date} for {title}")
                    break

            if not publication_date:
                self.logger.debug(f"No publication date found for {title}")
            
            # Determine item type based on URL pattern or content
            item_type = "Unknown"
            if '/services/' in relative_url:
                item_type = "Service"
            elif '/programs/' in relative_url:
                item_type = "Program"
            elif '/resources/' in relative_url:
                item_type = "Resource"
            elif '/training/' in relative_url:
                item_type = "Training"
            elif '/groups/' in relative_url:
                item_type = "Group"
            
            # Check if it's external by looking for "External" indicators
            is_external = False
            if 'External' in item_text or 'EXTERNAL' in item_text:
                is_external = True
            
            # Also check for external indicators in parent elements
            parent = item_element.parent
            while parent and parent.name not in ['body', 'html']:
                parent_text = parent.get_text()
                if 'External' in parent_text or 'EXTERNAL' in parent_text:
                    is_external = True
                    break
                parent = parent.parent
            
            item_info = {
                'cisa_official_name': title,
                'url': full_url,
                'publication_date': publication_date,
                'item_type': item_type,
                'is_cisa_official': not is_external
            }
            
            self.logger.debug(f"Extracted item: {title} ({item_type})")
            return item_info
            
        except Exception as e:
            self.logger.error(f"Error extracting item info: {e}")
            return None
    
    async def scrape_page(self, page_num: int) -> List[Dict]:
        """Scrape items from a specific page"""
        url = f"{self.base_url}?page={page_num}"
        soup = await self.get_page_content(url)

        if not soup:
            return []

        items = []
        processed_urls = set()  # To avoid duplicates

        # Find all item containers - look for common patterns
        # Pattern 1: Items with h3 titles
        item_containers = soup.find_all(['div', 'article'], class_=lambda x: x and any(
            keyword in x.lower() for keyword in ['item', 'resource', 'card', 'content']
        ))
        
        # Pattern 2: If no specific containers found, look for h3 elements directly
        if not item_containers:
            h3_elements = soup.find_all('h3')
            for h3 in h3_elements:
                # Find the parent container
                parent = h3.parent
                while parent and parent.name in ['div', 'article', 'section']:
                    if parent not in item_containers:
                        item_containers.append(parent)
                    break
        
        # Pattern 3: Fallback - look for any links that seem to be resources
        if not item_containers:
            all_links = soup.find_all('a', href=True)
            # Filter for resources-tools URLs, but apply the same strict filtering as extract_item_info
            resource_links = []
            for link in all_links:
                href = link.get('href', '')
                # Convert relative URL to absolute URL for consistent filtering
                full_url = urljoin('https://www.cisa.gov', href)
                # Apply the same strict filtering as in extract_item_info
                if full_url.startswith('https://www.cisa.gov/resources-tools/'):
                    resource_links.append(link)

            for link in resource_links:
                href = link.get('href', '')
                if href in processed_urls:
                    continue

                # Skip navigation and filter links
                if any(skip in href for skip in ['?page=', '?f%5B', '#', 'mailto:', 'tel:']):
                    continue

                title = link.get_text(strip=True)
                if not title:
                    continue

                # Create a mock container for this link
                mock_container = link.parent if link.parent else link
                item_info = self.extract_item_info(mock_container)

                if item_info:
                    items.append(item_info)
                    processed_urls.add(href)

        # Process found containers
        for container in item_containers:
            item_info = self.extract_item_info(container)
            if item_info:
                url_key = item_info['url']
                if url_key not in processed_urls:
                    items.append(item_info)
                    processed_urls.add(url_key)

        self.logger.info(f"Found {len(items)} items on page {page_num}")
        return items

    async def detect_total_pages(self) -> int:
        """Discover total number of pages by analyzing pagination on first page"""
        try:
            self.logger.info("Discovering total number of pages...")
            soup = await self.get_page_content(f"{self.base_url}?page=0")

            if not soup:
                self.logger.warning("Failed to get first page, using fallback MAX_PAGES")
                return Config.MAX_ALL_RESOURCES_TOOLS_PAGES

            # Look for pagination - specifically the "Last" link
            last_link = soup.find('a', string=lambda text: text and 'Last' in text)
            if last_link:
                href = last_link.get('href', '')
                if 'page=' in href:
                    match = re.search(r'page=(\d+)', href)
                    if match:
                        last_page = int(match.group(1))
                        total_pages = last_page + 1  # Pages are 0-indexed
                        self.logger.info(f"Discovered {total_pages} total pages (0-{last_page})")
                        return total_pages

            # Fallback: look for all page links and find the highest number
            page_links = soup.find_all('a', href=True)
            max_page = 0
            for link in page_links:
                href = link.get('href', '')
                if 'page=' in href:
                    match = re.search(r'page=(\d+)', href)
                    if match:
                        page_num = int(match.group(1))
                        max_page = max(max_page, page_num)

            if max_page > 0:
                total_pages = max_page + 1
                self.logger.info(f"Discovered {total_pages} total pages via page links (0-{max_page})")
                return total_pages

            # Final fallback - use the configured maximum
            self.logger.warning(f"Could not discover page count, using configured maximum of {Config.MAX_ALL_RESOURCES_TOOLS_PAGES} pages")
            return Config.MAX_ALL_RESOURCES_TOOLS_PAGES

        except Exception as e:
            self.logger.error(f"Error discovering total pages: {e}")
            self.logger.warning(f"Using configured maximum of {Config.MAX_ALL_RESOURCES_TOOLS_PAGES} pages as fallback")
            return Config.MAX_ALL_RESOURCES_TOOLS_PAGES

    async def scrape_all_items(self, max_pages: int = None) -> List[Dict]:
        """Scrape all items from all pages with automatic page discovery"""
        # Use the provided max_pages or fall back to configured value
        if max_pages is None:
            max_pages = Config.MAX_ALL_RESOURCES_TOOLS_PAGES
        self.logger.info(f"Starting to scrape all items with max_pages={max_pages}")
        return await super().scrape_all_items(max_pages=max_pages)
