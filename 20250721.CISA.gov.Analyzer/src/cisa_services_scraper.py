"""
CISA Services Scraper Module
Scrapes service information from CISA website
"""
import asyncio
import logging
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
from config import Config
from base_scraper import BaseCISAScraper


class CISAServicesScraper(BaseCISAScraper):
    """Scraper for CISA services pages"""

    def __init__(self):
        super().__init__(Config.CISA_SERVICES_BASE_URL)
    
    def extract_service_info(self, service_element) -> Optional[Dict]:
        """Extract service information from a service element"""
        try:
            # Find the service title and URL
            title_element = service_element.find('h3')
            if not title_element:
                return None

            link_element = title_element.find('a')
            if not link_element:
                return None

            title = link_element.get_text(strip=True)
            relative_url = link_element.get('href', '')
            from urllib.parse import urljoin
            full_url = urljoin('https://www.cisa.gov', relative_url)

            # Services-specific logic: Check if it's an external provider
            is_external = 'EXTERNAL PROVIDER' in service_element.get_text()

            # Try to get publication_date from all_resources_tools data
            publication_date = None
            try:
                url_to_date_mapping = self._load_all_resources_tools_data()
                if full_url in url_to_date_mapping:
                    publication_date = url_to_date_mapping[full_url]
                    self.logger.debug(f"Found publication_date from all_resources_tools for {title}: {publication_date}")
            except Exception as e:
                self.logger.debug(f"Could not get publication_date from all_resources_tools for {title}: {e}")

            service_info = {
                'cisa_official_name': title,
                'url': full_url,
                'is_cisa_official': not is_external
            }

            # Add publication_date if found
            if publication_date:
                service_info['publication_date'] = publication_date

            self.logger.debug(f"Extracted service: {title}")
            return service_info

        except Exception as e:
            self.logger.error(f"Error extracting service info: {e}")
            return None
    
    async def scrape_page(self, page_num: int) -> List[Dict]:
        """Scrape services from a specific page"""
        url = f"{self.base_url}?page={page_num}"
        soup = await self.get_page_content(url)

        if not soup:
            return []

        services = []

        # Find all service items - they appear to be in h3 elements with links
        service_elements = soup.find_all('h3')

        for element in service_elements:
            # Look for the parent container that might contain additional info
            parent = element.parent
            while parent and parent.name not in ['article', 'div', 'section']:
                parent = parent.parent

            if parent:
                service_info = self.extract_service_info(parent)
                if service_info:
                    services.append(service_info)

        self.logger.info(f"Found {len(services)} services on page {page_num}")
        return services

    # Backward compatibility
    async def scrape_services_page(self, page_num: int) -> List[Dict]:
        """Scrape services from a specific page (backward compatibility)"""
        return await self.scrape_page(page_num)
    
    async def detect_total_pages(self) -> int:
        """Discover total number of pages by analyzing pagination on first page"""
        try:
            self.logger.info("Discovering total number of pages...")
            print(f"🔍 正在检测总页数...")
            soup = await self.get_page_content(f"{self.base_url}?page=0")

            if not soup:
                self.logger.warning("Failed to get first page, using fallback MAX_PAGES")
                print(f"❌ 无法获取首页，使用回退值 MAX_PAGES: {Config.MAX_PAGES}")
                return Config.MAX_PAGES

            # Look for "Last" page link which contains the highest page number
            last_page_link = soup.find('a', string=lambda text: text and 'last' in text.lower())
            self.logger.debug(f"Found last page link: {last_page_link}")
            if last_page_link:
                href = last_page_link.get('href', '')
                self.logger.debug(f"Last page link href: {href}")
                # Extract page number from URL like "?page=29"
                import re
                match = re.search(r'page=(\d+)', href)
                if match:
                    last_page_num = int(match.group(1))
                    total_pages = last_page_num + 1  # Convert from 0-based to count
                    self.logger.info(f"Discovered {total_pages} total pages (0-{last_page_num})")
                    print(f"✅ 通过 'Last' 链接检测到总页数: {total_pages}")
                    return total_pages

            # Fallback: look for all page links and find the highest number
            self.logger.debug("Last page link not found, trying fallback method...")
            print(f"🔄 未找到 'Last' 链接，尝试备用方法...")
            page_links = soup.find_all('a', href=True)
            self.logger.debug(f"Found {len(page_links)} total links")
            max_page = 0
            page_links_found = 0
            for link in page_links:
                href = link.get('href', '')
                if 'page=' in href:
                    page_links_found += 1
                    import re
                    match = re.search(r'page=(\d+)', href)
                    if match:
                        page_num = int(match.group(1))
                        max_page = max(max_page, page_num)

            self.logger.debug(f"Found {page_links_found} page links, max page number: {max_page}")

            if max_page > 0:
                total_pages = max_page + 1
                self.logger.info(f"Discovered {total_pages} total pages via page links (0-{max_page})")
                print(f"✅ 通过页面链接检测到总页数: {total_pages}")
                return total_pages

            # Final fallback
            self.logger.warning("Could not discover page count, using configured MAX_PAGES")
            print(f"❌ 无法检测页数，使用配置的 MAX_PAGES: {Config.MAX_PAGES}")
            return Config.MAX_PAGES

        except Exception as e:
            self.logger.error(f"Error discovering total pages: {e}")
            self.logger.warning("Using configured MAX_PAGES as fallback")
            print(f"❌ 检测页数时出错: {e}，使用配置的 MAX_PAGES: {Config.MAX_PAGES}")
            return Config.MAX_PAGES

    async def scrape_all_services(self) -> List[Dict]:
        """Scrape all services from all pages with automatic page discovery (backward compatibility)"""
        # Use the configured MAX_SERVICES_PAGES to ensure we scrape all pages
        max_pages = Config.MAX_SERVICES_PAGES
        self.logger.info(f"Starting to scrape all services with max_pages={max_pages}")
        return await self.scrape_all_items(max_pages=max_pages)
