"""
Main entry point for CISA Services Analyzer
"""
import asyncio
import sys
import os

# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from unified_main import main_services


async def main(force_update: bool = False):
    """Main entry point with force_update support"""
    return await main_services(force_update=force_update)


if __name__ == "__main__":
    success = asyncio.run(main_services())
    sys.exit(0 if success else 1)
