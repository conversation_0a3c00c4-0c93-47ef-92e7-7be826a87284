"""
LLM Analyzer Module
Uses LLM to analyze service content and extract structured information
"""
import json
import logging
from typing import Dict, Optional, List
from config import Config
from llm_providers import LLMProviderFactory
from concurrency_manager import ConcurrencyManager

class LLMAnalyzer:
    """LLM-based content analyzer for CISA services and programs"""

    def __init__(self, analysis_type: str = "service"):
        """
        Initialize LLM analyzer

        Args:
            analysis_type: Type of analysis - "service", "program", "training", or "group"
        """
        self.analysis_type = analysis_type
        self.logger = logging.getLogger(__name__)
        self.concurrency_manager = ConcurrencyManager()

        # Initialize LLM provider based on configuration
        llm_config = Config.get_llm_config()
        self.llm_provider = LLMProviderFactory.create_provider(llm_config)

        # Validate provider configuration
        if not self.llm_provider.validate_config():
            raise ValueError(f"Invalid configuration for LLM provider: {llm_config['provider']}")

    def create_analysis_messages(self, data: Dict) -> List[Dict[str, str]]:
        """Create messages for LLM analysis based on analysis type"""
        if self.analysis_type == "service":
            return self._create_service_analysis_messages(data)
        elif self.analysis_type == "program":
            return self._create_program_analysis_messages(data)
        elif self.analysis_type == "training":
            return self._create_training_analysis_messages(data)
        elif self.analysis_type == "group":
            return self._create_group_analysis_messages(data)
        elif self.analysis_type == "resource":
            return self._create_resource_analysis_messages(data)
        elif self.analysis_type == "all_resources_tools":
            return self._create_all_resources_tools_analysis_messages(data)
        else:
            raise ValueError(f"Unsupported analysis type: {self.analysis_type}")

    def _create_service_analysis_messages(self, service_data: Dict) -> List[Dict[str, str]]:
        """Create messages for service analysis"""
        system_message = """你是一个专业的网络安全服务分析师，擅长分析和总结各种网络安全相关的服务和工具。请严格按照要求的JSON格式输出分析结果。"""

        user_prompt = f"""
请分析以下 CISA 服务的网页内容，并提取所需的信息。请严格按照 JSON 格式输出结果。

服务基础信息：
- CISA 官方名称: {service_data['cisa_official_name']}
- URL: {service_data['url']}
- 是否 CISA 官方提供: {service_data['is_cisa_official']}

网页内容：
{service_data.get('content', '')[:8000]}  # 限制内容长度避免超出token限制

请分析上述内容，并以 JSON 格式输出以下信息：

{{
    "cisa_official_name": "保持原英文名称",
    "url": "保持原URL",
    "description": "服务简介（中文，简洁明了地描述这个服务是什么，有什么功能）",
    "publication_date": "发布日期（如果能找到的话，格式：YYYY-MM-DD，如果找不到则为 null ）",
    "domain": "对应领域（中文，如：网络安全、应急响应、风险评估、培训教育等）",
    "target_audience": "目标受众（中文，如：政府机构、企业、个人用户、教育机构等）",
    "is_cybersecurity_related": true/false,
    "is_cisa_official": {service_data['is_cisa_official'] if isinstance(service_data['is_cisa_official'], bool) else '"N/A"'}
}}

分析要求：
1. 服务简介要准确反映服务的核心功能和价值
2. 发布日期要从内容中寻找，如果没有明确日期信息则设为 null
3. 对应领域要根据服务内容判断主要属于哪个领域
4. 目标受众要根据服务描述判断主要面向哪类用户
5. 是否网络安全相关要根据服务内容判断是否与网络安全直接相关
6. 除了 cisa_official_name 和 url 保持英文外，其他字段都用中文
7. 必须返回有效的 JSON 格式，不要包含任何其他文本

请开始分析：
"""

        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_prompt}
        ]

    def _create_program_analysis_messages(self, program_data: Dict) -> List[Dict[str, str]]:
        """Create messages for program analysis"""
        system_message = """你是一个专业的网络安全项目分析师，擅长分析和总结各种网络安全相关的项目和计划。请严格按照要求的JSON格式输出分析结果。"""

        user_prompt = f"""
请分析以下 CISA 项目（Program）的网页内容，并提取所需的信息。请严格按照 JSON 格式输出结果。

项目基础信息：
- CISA 官方名称: {program_data['cisa_official_name']}
- URL: {program_data['url']}
- 是否 CISA 官方提供: {program_data['is_cisa_official']}

网页内容：
{program_data.get('content', '')[:8000]}  # 限制内容长度避免超出token限制

请分析上述内容，并以 JSON 格式输出以下信息：

{{
    "cisa_official_name": "保持原英文名称",
    "url": "保持原URL",
    "description": "项目简介（中文，简洁明了地描述这个项目是什么，有什么目标和功能）",
    "publication_date": "发布日期（如果能找到的话，格式：YYYY-MM-DD，如果找不到则为 null ）",
    "domain": "对应领域（中文，如：网络安全、应急响应、风险评估、培训教育、基础设施保护等）",
    "target_audience": "目标受众（中文，如：政府机构、企业、个人用户、教育机构、关键基础设施运营商等）",
    "is_cybersecurity_related": true/false,
    "is_cisa_official": {program_data['is_cisa_official'] if isinstance(program_data['is_cisa_official'], bool) else '"N/A"'}
}}

分析要求：
1. 项目简介要准确反映项目的核心目标、功能和价值
2. 发布日期要从内容中寻找，如果没有明确日期信息则设为 null
3. 对应领域要根据项目内容判断主要属于哪个领域
4. 目标受众要根据项目描述判断主要面向哪类用户或组织
5. 是否网络安全相关要根据项目内容判断是否与网络安全直接相关
6. 除了 cisa_official_name 和 url 保持英文外，其他字段都用中文
7. 必须返回有效的 JSON 格式，不要包含任何其他文本
8. 注意这是分析"项目"（Program）而不是"服务"（Service），项目通常是更大范围的计划或倡议

请开始分析：
"""

        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_prompt}
        ]

    def _create_training_analysis_messages(self, training_data: Dict) -> List[Dict[str, str]]:
        """Create messages for training analysis"""
        system_message = """你是一个专业的网络安全培训分析师，擅长分析和总结各种网络安全相关的培训和教育资源。请严格按照要求的JSON格式输出分析结果。"""

        user_prompt = f"""
请分析以下 CISA 培训（Training）的网页内容，并提取所需的信息。请严格按照 JSON 格式输出结果。

培训基础信息：
- CISA 官方名称: {training_data['cisa_official_name']}
- URL: {training_data['url']}
- 是否 CISA 官方提供: {training_data['is_cisa_official']}

网页内容：
{training_data.get('content', '无法获取内容')}

请按照以下 JSON 格式输出分析结果：
{{
    "cisa_official_name": "{training_data['cisa_official_name']}",
    "url": "{training_data['url']}",
    "description": "培训简介（中文）",
    "publication_date": "发布日期（YYYY-MM-DD格式，如果没有明确日期则为null）",
    "domain": "对应领域（中文）",
    "target_audience": "目标受众（中文）",
    "is_cybersecurity_related": true/false,
    "is_cisa_official": {training_data['is_cisa_official'] if isinstance(training_data['is_cisa_official'], bool) else '"N/A"'}
}}

分析要求：
1. 培训简介要准确反映培训的核心内容和学习目标
2. 发布日期要从内容中寻找，如果没有明确日期信息则设为 null
3. 对应领域要根据培训内容判断主要属于哪个领域（如：网络安全基础、事件响应、风险管理、合规性等）
4. 目标受众要根据培训描述判断主要面向哪类用户（如：IT专业人员、管理人员、普通用户等）
5. 是否网络安全相关要根据培训内容判断是否与网络安全直接相关
6. 除了 cisa_official_name 和 url 保持英文外，其他字段都用中文
7. 必须返回有效的 JSON 格式，不要包含任何其他文本

请开始分析：
"""

        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_prompt}
        ]

    def _create_group_analysis_messages(self, group_data: Dict) -> List[Dict[str, str]]:
        """Create messages for group analysis"""
        system_message = """你是一个专业的网络安全组织分析师，擅长分析和总结各种网络安全相关的组织和团体。请严格按照要求的JSON格式输出分析结果。"""

        user_prompt = f"""
请分析以下 CISA 组织（Group）的网页内容，并提取所需的信息。请严格按照 JSON 格式输出结果。

组织基础信息：
- CISA 官方名称: {group_data['cisa_official_name']}
- URL: {group_data['url']}
- 是否 CISA 官方提供: {group_data['is_cisa_official']}

网页内容：
{group_data.get('content', '')[:8000]}  # 限制内容长度避免超出token限制

请分析上述内容，并以 JSON 格式输出以下信息：

{{
    "cisa_official_name": "保持原英文名称",
    "url": "保持原URL",
    "description": "组织简介（中文，简洁明了地描述这个组织是什么，有什么使命和功能）",
    "publication_date": "发布日期（如果能找到的话，格式：YYYY-MM-DD，如果找不到则为 null ）",
    "domain": "对应领域（中文，如：网络安全、应急响应、风险评估、培训教育、政策制定等）",
    "target_audience": "目标受众（中文，如：政府机构、企业、个人用户、教育机构、国际组织等）",
    "is_cybersecurity_related": true/false,
    "is_cisa_official": {group_data['is_cisa_official'] if isinstance(group_data['is_cisa_official'], bool) else '"N/A"'}
}}

分析要求：
1. 组织简介要准确反映组织的核心使命、功能和价值
2. 发布日期要从内容中寻找，如果没有明确日期信息则设为 null
3. 对应领域要根据组织内容判断主要属于哪个领域
4. 目标受众要根据组织描述判断主要面向哪类用户或机构
5. 是否网络安全相关要根据组织内容判断是否与网络安全直接相关
6. 除了 cisa_official_name 和 url 保持英文外，其他字段都用中文
7. 必须返回有效的 JSON 格式，不要包含任何其他文本
8. 注意这是分析"组织"（Group）而不是"服务"或"项目"，组织通常是机构、团体或联盟

请开始分析：
"""

        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_prompt}
        ]

    def _create_resource_analysis_messages(self, resource_data: Dict) -> List[Dict[str, str]]:
        """Create messages for resource analysis"""
        system_message = """你是一个专业的网络安全资源分析师，擅长分析和总结各种网络安全相关的资源和文档。请严格按照要求的JSON格式输出分析结果。"""

        user_prompt = f"""
请分析以下 CISA 资源（Resource）的网页内容，并提取所需的信息。请严格按照 JSON 格式输出结果。

资源基础信息：
- CISA 官方名称: {resource_data['cisa_official_name']}
- URL: {resource_data['url']}
- 是否 CISA 官方提供: {resource_data['is_cisa_official']}

网页内容：
{resource_data.get('content', '')[:8000]}  # 限制内容长度避免超出token限制

请分析上述内容，并以 JSON 格式输出以下信息：

{{
    "cisa_official_name": "保持原英文名称",
    "url": "保持原URL",
    "description": "资源简介（中文，简洁明了地描述这个资源是什么，有什么内容和用途）",
    "publication_date": "发布日期（如果能找到的话，格式：YYYY-MM-DD，如果找不到则为 null ）",
    "domain": "对应领域（中文，如：网络安全、应急响应、风险评估、培训教育、政策制定、技术指南等）",
    "target_audience": "目标受众（中文，如：政府机构、企业、个人用户、教育机构、技术人员、管理人员等）",
    "is_cybersecurity_related": true/false,
    "is_cisa_official": {resource_data['is_cisa_official'] if isinstance(resource_data['is_cisa_official'], bool) else '"N/A"'}
}}

分析要求：
1. 资源简介要准确反映资源的核心内容、用途和价值
2. 发布日期要从内容中寻找，如果没有明确日期信息则设为 null
3. 对应领域要根据资源内容判断主要属于哪个领域
4. 目标受众要根据资源描述判断主要面向哪类用户或机构
5. 是否网络安全相关要根据资源内容判断是否与网络安全直接相关
6. 除了 cisa_official_name 和 url 保持英文外，其他字段都用中文
7. 必须返回有效的 JSON 格式，不要包含任何其他文本
8. 注意这是分析"资源"（Resource），通常包括指南、文档、工具、报告、最佳实践等

请开始分析：
"""

        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_prompt}
        ]

    def _create_all_resources_tools_analysis_messages(self, item_data: Dict) -> List[Dict[str, str]]:
        """Create messages for all resources and tools analysis"""
        system_message = """你是一个专业的网络安全资源分析师，擅长分析和总结各种网络安全相关的资源、工具、服务和项目。请严格按照要求的JSON格式输出分析结果。"""

        user_prompt = f"""
请分析以下 CISA 资源/工具/服务/项目的网页内容，并提取所需的信息。请严格按照 JSON 格式输出结果。

项目基础信息：
- CISA 官方名称: {item_data['cisa_official_name']}
- URL: {item_data['url']}
- 项目类型: {item_data.get('item_type', 'Unknown')}
- 是否 CISA 官方提供: {item_data['is_cisa_official']}

网页内容：
{item_data.get('content', '无法获取内容')[:8000]}  # 限制内容长度避免超出token限制

请分析上述内容，并以 JSON 格式输出以下信息：

{{
    "cisa_official_name": "保持原英文名称",
    "url": "保持原URL",
    "description": "项目简介（中文，简洁明了地描述这个项目/资源/工具/服务是什么，有什么功能和用途）",
    "publication_date": "发布日期（如果能找到的话，格式：YYYY-MM-DD，如果找不到则为 null ）",
    "domain": "对应领域（中文，如：网络安全、应急响应、风险评估、培训教育、基础设施保护、政策制定等）",
    "target_audience": "目标受众（中文，如：政府机构、企业、个人用户、教育机构、关键基础设施运营商、技术人员等）",
    "is_cybersecurity_related": true/false,
    "is_cisa_official": {item_data['is_cisa_official'] if isinstance(item_data['is_cisa_official'], bool) else '"N/A"'}
}}

分析要求：
1. 项目简介要准确反映项目的核心功能和价值，根据项目类型调整描述重点
2. 发布日期要从内容中寻找，常见格式包括：Published、Updated、Date、发布时间等，如果没有明确日期信息则设为 null
3. 对应领域要根据项目内容判断主要属于哪个领域
4. 目标受众要根据项目描述判断主要面向哪类用户或机构
5. 是否网络安全相关要根据项目内容判断是否与网络安全直接相关
6. 除了 cisa_official_name 和 url 保持英文外，其他字段都用中文
7. 必须返回有效的 JSON 格式，不要包含任何其他文本
8. 注意这可能是服务、项目、资源、培训、组织等不同类型，请根据实际内容进行分析

请开始分析：
"""

        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_prompt}
        ]

    def parse_llm_response(self, response_text: str) -> Optional[Dict]:
        """Parse LLM response and extract JSON"""
        try:
            # Try to find JSON in the response
            response_text = response_text.strip()
            
            # Look for JSON block
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}')
            
            if start_idx != -1 and end_idx != -1:
                json_text = response_text[start_idx:end_idx+1]
                return json.loads(json_text)
            else:
                self.logger.error("No JSON found in LLM response")
                return None
                
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON from LLM response: {e}")
            self.logger.debug(f"Response text: {response_text}")
            return None
    
    async def analyze_item(self, item_data: Dict) -> Optional[Dict]:
        """Analyze a single item (service or program) using LLM"""
        try:
            messages = self.create_analysis_messages(item_data)
            item_type = self.analysis_type
            item_name = item_data['cisa_official_name']

            self.logger.info(f"Analyzing {item_type}: {item_name}")

            response_text = await self.llm_provider.generate_response(
                messages=messages,
                temperature=0.1,
                max_tokens=1000
            )

            result = self.parse_llm_response(response_text)

            if result:
                self.logger.info(f"Successfully analyzed {item_type}: {item_name}")
                return result
            else:
                self.logger.error(f"Failed to parse LLM response for {item_type}: {item_name}")
                return self.create_fallback_result(item_data)

        except Exception as e:
            self.logger.error(f"Error analyzing {self.analysis_type} {item_data['cisa_official_name']}: {e}")
            return self.create_fallback_result(item_data)

    # Backward compatibility methods
    async def analyze_service(self, service_data: Dict) -> Optional[Dict]:
        """Analyze a single service using LLM (backward compatibility)"""
        return await self.analyze_item(service_data)

    async def analyze_program(self, program_data: Dict) -> Optional[Dict]:
        """Analyze a single program using LLM"""
        return await self.analyze_item(program_data)
    
    def create_fallback_result(self, item_data: Dict) -> Dict:
        """Create fallback result when LLM analysis fails"""
        # Use standardized English field names for all types
        base_result = {
            "cisa_official_name": item_data['cisa_official_name'],
            "url": item_data['url'],
            "description": "分析失败，无法获取描述",
            "publication_date": None,
            "domain": "未知",
            "target_audience": "未知",
            "is_cybersecurity_related": True,  # Default to True for CISA items
            "is_cisa_official": item_data['is_cisa_official']
        }

        return base_result
    
    async def analyze_item_with_fallback(self, item_data: Dict) -> Dict:
        """Analyze an item with fallback handling"""
        if not item_data.get('scrape_success', False):
            self.logger.warning(f"Skipping analysis for {item_data['cisa_official_name']} due to scrape failure")
            return self.create_fallback_result(item_data)
        else:
            result = await self.analyze_item(item_data)
            return result if result else self.create_fallback_result(item_data)

    # Backward compatibility methods
    async def analyze_service_with_fallback(self, service_data: Dict) -> Dict:
        """Analyze a service with fallback handling (backward compatibility)"""
        return await self.analyze_item_with_fallback(service_data)

    async def analyze_program_with_fallback(self, program_data: Dict) -> Dict:
        """Analyze a program with fallback handling"""
        return await self.analyze_item_with_fallback(program_data)

    async def analyze_group_with_fallback(self, group_data: Dict) -> Dict:
        """Analyze a group with fallback handling"""
        return await self.analyze_item_with_fallback(group_data)

    async def analyze_all_items(self, items_data: list) -> list:
        """Analyze all items using LLM with concurrency control"""
        # Use concurrency manager for controlled LLM analysis
        results = await self.concurrency_manager.run_llm_analysis(
            services=items_data,  # Note: concurrency_manager uses 'services' parameter name
            analyzer_func=self.analyze_item_with_fallback
        )

        # Process results and handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"Error analyzing {self.analysis_type} {items_data[i]['cisa_official_name']}: {result}")
                # Create fallback result for failed analysis
                fallback_result = self.create_fallback_result(items_data[i])
                processed_results.append(fallback_result)
            else:
                processed_results.append(result)

        return processed_results

    async def analyze_all_services(self, services_data: list) -> list:
        """Analyze all services using LLM with concurrency control (backward compatibility)"""
        return await self.analyze_all_items(services_data)

    async def analyze_all_programs(self, programs_data: list) -> list:
        """Analyze all programs using LLM with concurrency control"""
        return await self.analyze_all_items(programs_data)
