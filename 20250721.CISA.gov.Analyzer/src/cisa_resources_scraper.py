"""
CISA Resources Scraper Module
Scrapes resource information from CISA website
"""
import asyncio
import logging
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
from urllib.parse import urljoin
from config import Config
from base_scraper import BaseCISAScraper


class CISAResourcesScraper(BaseCISAScraper):
    """Scraper for CISA resources pages"""

    def __init__(self):
        super().__init__(Config.CISA_RESOURCES_BASE_URL)
    
    def extract_resource_info(self, resource_element) -> Optional[Dict]:
        """Extract resource information from a resource element"""
        try:
            # Find the resource title and URL
            title_element = resource_element.find('h3')
            if not title_element:
                return None
                
            link_element = title_element.find('a')
            if not link_element:
                return None
                
            title = link_element.get_text(strip=True)
            relative_url = link_element.get('href', '')
            full_url = urljoin('https://www.cisa.gov', relative_url)
            
            # Check if it's an external provider by looking for "External" text
            is_external = False
            # Look for external indicators in the resource element
            resource_text = resource_element.get_text()
            if 'External' in resource_text or 'EXTERNAL' in resource_text:
                is_external = True
            
            # Also check for external resource type indicators
            resource_type_elements = resource_element.find_all(text=True)
            for text in resource_type_elements:
                if 'External' in text:
                    is_external = True
                    break
            
            resource_info = {
                'cisa_official_name': title,
                'url': full_url,
                'is_cisa_official': not is_external
            }
            
            self.logger.debug(f"Extracted resource: {title}")
            return resource_info
            
        except Exception as e:
            self.logger.error(f"Error extracting resource info: {e}")
            return None
    
    async def scrape_page(self, page_num: int) -> List[Dict]:
        """Scrape resources from a specific page"""
        url = f"{self.base_url}?page={page_num}"
        soup = await self.get_page_content(url)

        if not soup:
            return []

        resources = []
        processed_urls = set()  # To avoid duplicates

        # Find all links that point to resources
        all_links = soup.find_all('a', href=True)
        resource_links = [link for link in all_links if '/resources-tools/resources/' in link.get('href', '')]

        for link in resource_links:
            href = link.get('href', '')
            # Skip if we've already processed this URL
            if href in processed_urls:
                continue

            # Skip navigation links and other non-resource links
            if any(skip in href for skip in ['?page=', '?f%5B', '#', 'mailto:', 'tel:']):
                continue

            title = link.get_text(strip=True)
            if not title:  # Skip links without text
                continue

            full_url = urljoin('https://www.cisa.gov', href)

            # Simplified logic: set is_cisa_official to "N/A" for Resources
            resource_info = {
                'cisa_official_name': title,
                'url': full_url,
                'is_cisa_official': "N/A"
            }

            resources.append(resource_info)
            processed_urls.add(href)
            self.logger.debug(f"Extracted resource: {title}")

        self.logger.info(f"Found {len(resources)} resources on page {page_num}")
        return resources

    async def detect_total_pages(self) -> int:
        """Discover total number of pages by analyzing pagination on first page"""
        try:
            self.logger.info("Discovering total number of pages...")
            soup = await self.get_page_content(f"{self.base_url}?page=0")

            if not soup:
                self.logger.warning("Failed to get first page, using fallback MAX_PAGES")
                return Config.MAX_RESOURCES_PAGES

            # Look for pagination - specifically the "Last" link
            last_link = soup.find('a', string=lambda text: text and 'Last' in text)
            if last_link:
                href = last_link.get('href', '')
                if 'page=' in href:
                    import re
                    match = re.search(r'page=(\d+)', href)
                    if match:
                        last_page = int(match.group(1))
                        total_pages = last_page + 1  # Pages are 0-indexed
                        self.logger.info(f"Discovered {total_pages} total pages (0-{last_page})")
                        return total_pages

            # Fallback: look for all page links and find the highest number
            page_links = soup.find_all('a', href=True)
            max_page = 0
            for link in page_links:
                href = link.get('href', '')
                if 'page=' in href:
                    import re
                    match = re.search(r'page=(\d+)', href)
                    if match:
                        page_num = int(match.group(1))
                        max_page = max(max_page, page_num)

            if max_page > 0:
                total_pages = max_page + 1
                self.logger.info(f"Discovered {total_pages} total pages via page links (0-{max_page})")
                return total_pages

            # Final fallback - use the known maximum from requirements (147 pages)
            self.logger.warning("Could not discover page count, using known maximum of 148 pages (0-147)")
            return 148

        except Exception as e:
            self.logger.error(f"Error discovering total pages: {e}")
            self.logger.warning("Using known maximum of 148 pages as fallback")
            return 148

    async def scrape_all_resources(self) -> List[Dict]:
        """Scrape all resources from all pages with automatic page discovery"""
        # Use the configured MAX_RESOURCES_PAGES to ensure we scrape all pages
        max_pages = Config.MAX_RESOURCES_PAGES
        self.logger.info(f"Starting to scrape all resources with max_pages={max_pages}")
        return await self.scrape_all_items(max_pages=max_pages)
