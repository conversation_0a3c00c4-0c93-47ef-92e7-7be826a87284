"""
Graph Processing Module
Provides graph-based analysis and chain processing capabilities
"""
import logging
import asyncio
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from config import Config

# Note: These imports might need to be adjusted based on actual package APIs
try:
    import graphlang
    GRAPHLANG_AVAILABLE = True
except ImportError:
    GRAPHLANG_AVAILABLE = False
    logging.warning("graphlang package not available. Graph analysis features will be disabled.")

try:
    import graphchain
    GRAPHCHAIN_AVAILABLE = True
except ImportError:
    GRAPHCHAIN_AVAILABLE = False
    logging.warning("graphchain package not available. Chain processing features will be disabled.")

@dataclass
class ServiceNode:
    """Represents a service as a node in the graph"""
    id: str
    name: str
    url: str
    domain: str
    target_audience: str
    is_cisa_official: bool
    is_cybersecurity_related: bool
    description: str
    metadata: Dict[str, Any]

@dataclass
class ServiceRelation:
    """Represents a relationship between services"""
    source_id: str
    target_id: str
    relation_type: str
    strength: float
    metadata: Dict[str, Any]

class GraphProcessor:
    """Processes services data using graph-based analysis"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.graph_enabled = Config.ENABLE_GRAPH_ANALYSIS and GRAPHLANG_AVAILABLE
        self.chain_enabled = Config.ENABLE_CHAIN_PROCESSING and GRAPHCHAIN_AVAILABLE
        
        if not self.graph_enabled:
            self.logger.warning("Graph analysis is disabled or graphlang not available")
        if not self.chain_enabled:
            self.logger.warning("Chain processing is disabled or graphchain not available")
    
    def create_service_nodes(self, services_data: List[Dict]) -> List[ServiceNode]:
        """Convert services data to graph nodes"""
        nodes = []
        
        for service in services_data:
            node = ServiceNode(
                id=service['url'],  # Use URL as unique identifier
                name=service['cisa_official_name'],
                url=service['url'],
                domain=service.get('domain', 'Unknown'),
                target_audience=service.get('target_audience', 'Unknown'),
                is_cisa_official=service.get('is_cisa_official', True),
                is_cybersecurity_related=service.get('is_cybersecurity_related', True),
                description=service.get('service_description', ''),
                metadata=service.get('metadata', {})
            )
            nodes.append(node)
        
        self.logger.info(f"Created {len(nodes)} service nodes")
        return nodes
    
    def analyze_service_relationships(self, nodes: List[ServiceNode]) -> List[ServiceRelation]:
        """Analyze relationships between services"""
        relations = []
        
        for i, node1 in enumerate(nodes):
            for j, node2 in enumerate(nodes[i+1:], i+1):
                # Analyze domain similarity
                domain_similarity = self._calculate_domain_similarity(node1, node2)
                
                # Analyze audience similarity
                audience_similarity = self._calculate_audience_similarity(node1, node2)
                
                # Analyze description similarity
                desc_similarity = self._calculate_description_similarity(node1, node2)
                
                # Calculate overall relationship strength
                overall_strength = (domain_similarity + audience_similarity + desc_similarity) / 3
                
                if overall_strength > 0.3:  # Threshold for meaningful relationships
                    relation = ServiceRelation(
                        source_id=node1.id,
                        target_id=node2.id,
                        relation_type=self._determine_relation_type(node1, node2, overall_strength),
                        strength=overall_strength,
                        metadata={
                            'domain_similarity': domain_similarity,
                            'audience_similarity': audience_similarity,
                            'description_similarity': desc_similarity
                        }
                    )
                    relations.append(relation)
        
        self.logger.info(f"Found {len(relations)} service relationships")
        return relations
    
    def _calculate_domain_similarity(self, node1: ServiceNode, node2: ServiceNode) -> float:
        """Calculate similarity based on domain"""
        if node1.domain == node2.domain:
            return 1.0
        
        # Simple keyword-based similarity
        domain1_words = set(node1.domain.lower().split())
        domain2_words = set(node2.domain.lower().split())
        
        if not domain1_words or not domain2_words:
            return 0.0
        
        intersection = domain1_words.intersection(domain2_words)
        union = domain1_words.union(domain2_words)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _calculate_audience_similarity(self, node1: ServiceNode, node2: ServiceNode) -> float:
        """Calculate similarity based on target audience"""
        if node1.target_audience == node2.target_audience:
            return 1.0
        
        # Simple keyword-based similarity
        audience1_words = set(node1.target_audience.lower().split())
        audience2_words = set(node2.target_audience.lower().split())
        
        if not audience1_words or not audience2_words:
            return 0.0
        
        intersection = audience1_words.intersection(audience2_words)
        union = audience1_words.union(audience2_words)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _calculate_description_similarity(self, node1: ServiceNode, node2: ServiceNode) -> float:
        """Calculate similarity based on service descriptions"""
        # Simple keyword-based similarity (could be enhanced with NLP)
        desc1_words = set(node1.description.lower().split())
        desc2_words = set(node2.description.lower().split())
        
        if not desc1_words or not desc2_words:
            return 0.0
        
        intersection = desc1_words.intersection(desc2_words)
        union = desc1_words.union(desc2_words)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _determine_relation_type(self, node1: ServiceNode, node2: ServiceNode, strength: float) -> str:
        """Determine the type of relationship between two services"""
        if strength > 0.8:
            return "highly_related"
        elif strength > 0.6:
            return "related"
        elif strength > 0.4:
            return "somewhat_related"
        else:
            return "weakly_related"
    
    async def build_service_graph(self, services_data: List[Dict]) -> Optional[Dict]:
        """Build a graph representation of services"""
        if not self.graph_enabled:
            self.logger.warning("Graph analysis is disabled")
            return None
        
        try:
            # Create nodes and relationships
            nodes = self.create_service_nodes(services_data)
            relations = self.analyze_service_relationships(nodes)
            
            # Build graph structure
            graph_data = {
                'nodes': [
                    {
                        'id': node.id,
                        'name': node.name,
                        'domain': node.domain,
                        'target_audience': node.target_audience,
                        'is_cisa_official': node.is_cisa_official,
                        'is_cybersecurity_related': node.is_cybersecurity_related,
                        'description': node.description[:200] + '...' if len(node.description) > 200 else node.description
                    }
                    for node in nodes
                ],
                'edges': [
                    {
                        'source': rel.source_id,
                        'target': rel.target_id,
                        'type': rel.relation_type,
                        'strength': rel.strength
                    }
                    for rel in relations
                ],
                'statistics': {
                    'total_nodes': len(nodes),
                    'total_edges': len(relations),
                    'cisa_official_count': sum(1 for node in nodes if node.is_cisa_official),
                    'cybersecurity_related_count': sum(1 for node in nodes if node.is_cybersecurity_related),
                    'domains': list(set(node.domain for node in nodes)),
                    'audiences': list(set(node.target_audience for node in nodes))
                }
            }
            
            self.logger.info("Successfully built service graph")
            return graph_data
            
        except Exception as e:
            self.logger.error(f"Error building service graph: {e}")
            return None

class ChainProcessor:
    """Processes services data using chain-based analysis"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.enabled = Config.ENABLE_CHAIN_PROCESSING and GRAPHCHAIN_AVAILABLE
        
        if not self.enabled:
            self.logger.warning("Chain processing is disabled or graphchain not available")
    
    async def create_analysis_chain(self, services_data: List[Dict]) -> Optional[Dict]:
        """Create a chain-based analysis of services"""
        if not self.enabled:
            self.logger.warning("Chain processing is disabled")
            return None
        
        try:
            # Group services by domain
            domain_chains = self._group_by_domain(services_data)
            
            # Analyze service dependencies and workflows
            workflow_chains = self._analyze_workflows(services_data)
            
            # Create audience-based chains
            audience_chains = self._group_by_audience(services_data)
            
            chain_analysis = {
                'domain_chains': domain_chains,
                'workflow_chains': workflow_chains,
                'audience_chains': audience_chains,
                'chain_statistics': {
                    'total_domains': len(domain_chains),
                    'total_workflows': len(workflow_chains),
                    'total_audiences': len(audience_chains),
                    'average_chain_length': self._calculate_average_chain_length(domain_chains, workflow_chains, audience_chains)
                }
            }
            
            self.logger.info("Successfully created analysis chains")
            return chain_analysis
            
        except Exception as e:
            self.logger.error(f"Error creating analysis chains: {e}")
            return None
    
    def _group_by_domain(self, services_data: List[Dict]) -> Dict[str, List[Dict]]:
        """Group services by domain"""
        domain_groups = {}
        
        for service in services_data:
            domain = service.get('domain', 'Unknown')
            if domain not in domain_groups:
                domain_groups[domain] = []
            domain_groups[domain].append(service)
        
        return domain_groups
    
    def _group_by_audience(self, services_data: List[Dict]) -> Dict[str, List[Dict]]:
        """Group services by target audience"""
        audience_groups = {}
        
        for service in services_data:
            audience = service.get('target_audience', 'Unknown')
            if audience not in audience_groups:
                audience_groups[audience] = []
            audience_groups[audience].append(service)
        
        return audience_groups
    
    def _analyze_workflows(self, services_data: List[Dict]) -> List[Dict]:
        """Analyze potential workflows between services"""
        workflows = []
        
        # Simple workflow detection based on service types and dependencies
        # This is a placeholder implementation that could be enhanced
        
        for service in services_data:
            if 'assessment' in service.get('service_description', '').lower():
                workflow = {
                    'type': 'assessment_workflow',
                    'entry_point': service,
                    'related_services': self._find_related_services(service, services_data)
                }
                workflows.append(workflow)
        
        return workflows
    
    def _find_related_services(self, target_service: Dict, all_services: List[Dict]) -> List[Dict]:
        """Find services related to a target service"""
        related = []
        target_domain = target_service.get('domain', '')
        target_audience = target_service.get('target_audience', '')
        
        for service in all_services:
            if service['url'] == target_service['url']:
                continue
            
            if (service.get('domain') == target_domain or 
                service.get('target_audience') == target_audience):
                related.append(service)
        
        return related[:5]  # Limit to top 5 related services
    
    def _calculate_average_chain_length(self, *chain_groups) -> float:
        """Calculate average chain length across all chain types"""
        total_length = 0
        total_chains = 0
        
        for group in chain_groups:
            if isinstance(group, dict):
                for chain in group.values():
                    total_length += len(chain)
                    total_chains += 1
            elif isinstance(group, list):
                for chain in group:
                    if isinstance(chain, dict) and 'related_services' in chain:
                        total_length += len(chain['related_services']) + 1
                        total_chains += 1
        
        return total_length / total_chains if total_chains > 0 else 0.0
