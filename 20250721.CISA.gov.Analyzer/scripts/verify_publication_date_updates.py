#!/usr/bin/env python3
"""
验证脚本：检查 publication_date 更新的结果
"""

import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def load_json_file(file_path: Path) -> Optional[Dict[str, Any]]:
    """加载 JSON 文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def count_publication_dates(data: Dict[str, Any], data_key: str) -> Dict[str, int]:
    """统计 publication_date 的情况"""
    if data_key not in data:
        return {"total": 0, "with_date": 0, "null_date": 0}
    
    total = len(data[data_key])
    with_date = 0
    null_date = 0
    
    for item in data[data_key]:
        pub_date = item.get('publication_date')
        if pub_date and pub_date != "null":
            with_date += 1
        else:
            null_date += 1
    
    return {
        "total": total,
        "with_date": with_date,
        "null_date": null_date,
        "percentage": round((with_date / total * 100), 2) if total > 0 else 0
    }

def main():
    """主函数"""
    print("Verifying publication_date updates...")
    print("=" * 60)
    
    # 定义文件路径
    data_dir = project_root / 'data'
    
    # 要检查的文件列表
    target_files = [
        ('cisa_groups.json', 'groups'),
        ('cisa_programs.json', 'programs'),
        ('cisa_resources.json', 'resources'),
        ('cisa_services.json', 'services'),
        ('cisa_trainings.json', 'trainings')
    ]
    
    total_items = 0
    total_with_dates = 0
    
    for filename, data_key in target_files:
        file_path = data_dir / filename
        print(f"\n📁 {filename}")
        print("-" * 40)
        
        # 加载文件
        data = load_json_file(file_path)
        if not data:
            print("❌ Failed to load file")
            continue
        
        # 统计 publication_date
        stats = count_publication_dates(data, data_key)
        total_items += stats["total"]
        total_with_dates += stats["with_date"]
        
        print(f"📊 Total items: {stats['total']}")
        print(f"✅ With publication_date: {stats['with_date']} ({stats['percentage']}%)")
        print(f"❌ Without publication_date: {stats['null_date']}")
        
        # 检查元数据
        metadata = data.get('metadata', {})
        if 'last_publication_date_update' in metadata:
            print(f"🕒 Last update: {metadata['last_publication_date_update']}")
            print(f"🔄 Updated count: {metadata.get('publication_dates_updated_count', 0)}")
        else:
            print("ℹ️  No update metadata found")
    
    print("\n" + "=" * 60)
    print("📈 SUMMARY")
    print("=" * 60)
    print(f"📊 Total items across all files: {total_items}")
    print(f"✅ Total items with publication_date: {total_with_dates}")
    print(f"📊 Overall percentage: {round((total_with_dates / total_items * 100), 2) if total_items > 0 else 0}%")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
