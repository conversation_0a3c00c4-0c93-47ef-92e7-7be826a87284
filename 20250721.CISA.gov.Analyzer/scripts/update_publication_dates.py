#!/usr/bin/env python3
"""
一次性脚本：从 all_resources_tools JSON 文件中读取 publication_date，
并更新 groups、programs、resources、services、trainings 对应 JSON 文件的 publication_date。

该脚本通过 URL 匹配来找到对应的项目并更新其 publication_date。
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def load_json_file(file_path: Path) -> Optional[Dict[str, Any]]:
    """加载 JSON 文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Warning: File not found: {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"Error: Failed to parse JSON file {file_path}: {e}")
        return None
    except Exception as e:
        print(f"Error: Failed to load file {file_path}: {e}")
        return None

def save_json_file(file_path: Path, data: Dict[str, Any]) -> bool:
    """保存 JSON 文件"""
    try:
        # 创建备份
        backup_path = file_path.with_suffix('.json.backup')
        if file_path.exists():
            import shutil
            shutil.copy2(file_path, backup_path)
            print(f"Created backup: {backup_path}")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"Successfully updated: {file_path}")
        return True
    except Exception as e:
        print(f"Error: Failed to save file {file_path}: {e}")
        return False

def create_url_to_date_mapping(all_resources_data: Dict[str, Any]) -> Dict[str, str]:
    """从 all_resources_tools 数据中创建 URL 到 publication_date 的映射"""
    url_to_date = {}
    
    if 'all_resources_tools' not in all_resources_data:
        print("Error: 'all_resources_tools' key not found in data")
        return url_to_date
    
    for item in all_resources_data['all_resources_tools']:
        url = item.get('url')
        publication_date = item.get('publication_date')
        
        if url and publication_date and publication_date != "null":
            url_to_date[url] = publication_date
    
    print(f"Found {len(url_to_date)} items with valid publication_date in all_resources_tools")
    return url_to_date

def update_publication_dates(data: Dict[str, Any], data_key: str, url_to_date: Dict[str, str]) -> int:
    """更新指定数据中的 publication_date"""
    if data_key not in data:
        print(f"Warning: '{data_key}' key not found in data")
        return 0
    
    updated_count = 0
    
    for item in data[data_key]:
        url = item.get('url')
        if url and url in url_to_date:
            old_date = item.get('publication_date')
            new_date = url_to_date[url]
            
            # 只有当新日期不同时才更新
            if old_date != new_date:
                item['publication_date'] = new_date
                updated_count += 1
                print(f"Updated {item.get('cisa_official_name', 'Unknown')}: {old_date} -> {new_date}")
    
    return updated_count

def update_metadata(data: Dict[str, Any], updated_count: int):
    """更新元数据信息"""
    if 'metadata' not in data:
        data['metadata'] = {}
    
    data['metadata']['last_publication_date_update'] = datetime.now().isoformat()
    data['metadata']['publication_dates_updated_count'] = updated_count

def main():
    """主函数"""
    print("Starting publication_date update script...")
    
    # 定义文件路径
    data_dir = project_root / 'data'
    all_resources_file = data_dir / 'cisa_all_resourcess_and_tools_info.json'
    
    # 要更新的文件列表
    target_files = [
        ('cisa_groups.json', 'groups'),
        ('cisa_programs.json', 'programs'),
        ('cisa_resources.json', 'resources'),
        ('cisa_services.json', 'services'),
        ('cisa_trainings.json', 'trainings')
    ]
    
    # 加载 all_resources_tools 数据
    print(f"Loading all_resources_tools data from: {all_resources_file}")
    all_resources_data = load_json_file(all_resources_file)
    if not all_resources_data:
        print("Error: Failed to load all_resources_tools data")
        return 1
    
    # 创建 URL 到日期的映射
    url_to_date = create_url_to_date_mapping(all_resources_data)
    if not url_to_date:
        print("Error: No valid publication_date found in all_resources_tools")
        return 1
    
    # 更新每个目标文件
    total_updated = 0
    for filename, data_key in target_files:
        file_path = data_dir / filename
        print(f"\nProcessing: {filename}")
        
        # 加载目标文件
        target_data = load_json_file(file_path)
        if not target_data:
            print(f"Skipping {filename} due to load error")
            continue
        
        # 更新 publication_date
        updated_count = update_publication_dates(target_data, data_key, url_to_date)
        
        if updated_count > 0:
            # 更新元数据
            update_metadata(target_data, updated_count)
            
            # 保存文件
            if save_json_file(file_path, target_data):
                total_updated += updated_count
                print(f"Updated {updated_count} items in {filename}")
            else:
                print(f"Failed to save {filename}")
        else:
            print(f"No updates needed for {filename}")
    
    print(f"\nScript completed. Total items updated: {total_updated}")
    return 0

if __name__ == "__main__":
    sys.exit(main())
