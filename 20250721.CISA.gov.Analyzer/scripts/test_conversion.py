#!/usr/bin/env python3
"""
Test conversion script to verify field standardization
"""
import json
import sys
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent / 'src'
sys.path.insert(0, str(src_dir))

from field_standardizer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_single_file_conversion():
    """Test conversion of a single file"""
    standardizer = FieldStandardizer()
    
    # Load groups file
    groups_file = Path("data/cisa_groups.json")
    
    with open(groups_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("Original first item:")
    print(json.dumps(data['groups'][0], indent=2, ensure_ascii=False))
    
    # Convert items
    converted_items = []
    for item in data['groups']:
        converted = standardizer.standardize_item(item)
        converted_items.append(converted)
    
    print("\nConverted first item:")
    print(json.dumps(converted_items[0], indent=2, ensure_ascii=False))
    
    # Update metadata
    data['metadata']['converted_at'] = "2025-07-24T13:30:00"
    data['metadata']['field_format'] = "standardized_english"
    data['groups'] = converted_items
    
    # Save to test file
    test_file = Path("data/test_converted_groups.json")
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"\nSaved converted data to {test_file}")
    
    # Verify the saved file
    with open(test_file, 'r', encoding='utf-8') as f:
        saved_data = json.load(f)
    
    print("\nVerification - first item from saved file:")
    print(json.dumps(saved_data['groups'][0], indent=2, ensure_ascii=False))

if __name__ == "__main__":
    test_single_file_conversion()
