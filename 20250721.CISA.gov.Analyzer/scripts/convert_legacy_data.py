#!/usr/bin/env python3
"""
Legacy Data Conversion Script
Converts existing JSON data files to standardized field format
"""
import json
import os
import sys
import logging
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent / 'src'
sys.path.insert(0, str(src_dir))

from field_standardizer import FieldStandardizer


class LegacyDataConverter:
    """Converts legacy JSON data files to standardized format"""
    
    def __init__(self, data_dir: str = "data"):
        self.logger = logging.getLogger(__name__)
        self.data_dir = Path(data_dir)
        self.standardizer = FieldStandardizer()
        
        # Backup directory for original files
        self.backup_dir = self.data_dir / "backups" / f"legacy_conversion_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Files to convert (cisa_*.json pattern)
        self.target_files = [
            "cisa_services.json",
            "cisa_groups.json", 
            "cisa_programs.json",
            "cisa_resources.json",
            "cisa_trainings.json"
        ]
    
    def create_backup(self, file_path: Path) -> bool:
        """Create backup of original file"""
        try:
            # Ensure backup directory exists
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy original file to backup
            backup_path = self.backup_dir / file_path.name
            shutil.copy2(file_path, backup_path)
            
            self.logger.info(f"Created backup: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create backup for {file_path}: {e}")
            return False
    
    def load_json_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Load JSON file safely"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load {file_path}: {e}")
            return None
    
    def save_json_file(self, file_path: Path, data: Dict[str, Any]) -> bool:
        """Save JSON file safely"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"Successfully saved {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to save {file_path}: {e}")
            return False
    
    def convert_data_items(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert data items to standardized format"""
        converted_items = []
        
        for item in items:
            # Standardize fields using the field standardizer
            standardized_item = self.standardizer.standardize_item(item)
            converted_items.append(standardized_item)
        
        return converted_items
    
    def update_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Update metadata to reflect conversion"""
        updated_metadata = metadata.copy()
        
        # Add conversion information
        updated_metadata["converted_at"] = datetime.now().isoformat()
        updated_metadata["conversion_version"] = "2.0.0"
        updated_metadata["field_format"] = "standardized_english"
        
        # Update analyzer version if needed
        if "analyzer_version" in updated_metadata:
            updated_metadata["analyzer_version"] = "2.0.0"
        
        return updated_metadata
    
    def convert_file(self, file_path: Path) -> bool:
        """Convert a single JSON file"""
        self.logger.info(f"Converting file: {file_path}")
        
        # Load original data
        original_data = self.load_json_file(file_path)
        if not original_data:
            return False

        # Check if already converted (can be overridden with force flag)
        metadata = original_data.get("metadata", {})
        if metadata.get("field_format") == "standardized_english":
            self.logger.info(f"File {file_path} already converted, but proceeding with re-conversion")
            # Continue with conversion to ensure latest format

        # Create backup
        if not self.create_backup(file_path):
            self.logger.warning(f"Proceeding without backup for {file_path}")

        # Determine data type and key
        data_type = self.detect_data_type(file_path.name)
        data_key = self.get_data_key(data_type)

        if data_key not in original_data:
            self.logger.error(f"Data key '{data_key}' not found in {file_path}")
            return False

        # Convert data items
        original_items = original_data[data_key]
        converted_items = self.convert_data_items(original_items)

        # Update metadata
        updated_metadata = self.update_metadata(original_data.get("metadata", {}))

        # Create new data structure
        converted_data = {
            "metadata": updated_metadata,
            data_key: converted_items
        }
        
        # Save converted data
        if self.save_json_file(file_path, converted_data):
            self.logger.info(f"Successfully converted {file_path}")
            self.logger.info(f"Converted {len(converted_items)} items")
            return True
        else:
            return False
    
    def detect_data_type(self, filename: str) -> str:
        """Detect data type from filename"""
        if "services" in filename:
            return "services"
        elif "groups" in filename:
            return "groups"
        elif "programs" in filename:
            return "programs"
        elif "resources" in filename:
            return "resources"
        elif "trainings" in filename:
            return "trainings"
        else:
            return "unknown"
    
    def get_data_key(self, data_type: str) -> str:
        """Get the data key for a data type"""
        return data_type  # e.g., "services", "groups", etc.
    
    def validate_conversion(self, file_path: Path) -> bool:
        """Validate that conversion was successful"""
        try:
            # Load converted data
            converted_data = self.load_json_file(file_path)
            if not converted_data:
                return False
            
            # Check metadata
            metadata = converted_data.get("metadata", {})
            if "converted_at" not in metadata:
                self.logger.error(f"Missing conversion metadata in {file_path}")
                return False
            
            # Check data structure
            data_type = self.detect_data_type(file_path.name)
            data_key = self.get_data_key(data_type)
            
            if data_key not in converted_data:
                self.logger.error(f"Missing data key '{data_key}' in {file_path}")
                return False
            
            items = converted_data[data_key]
            if not isinstance(items, list):
                self.logger.error(f"Data items not a list in {file_path}")
                return False
            
            # Validate field standardization
            for i, item in enumerate(items[:5]):  # Check first 5 items
                missing_fields = self.standardizer.validate_standard_fields(item)
                if missing_fields:
                    self.logger.warning(f"Item {i} in {file_path} missing fields: {missing_fields}")
            
            self.logger.info(f"Validation passed for {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Validation failed for {file_path}: {e}")
            return False
    
    def convert_all_files(self) -> Dict[str, bool]:
        """Convert all target files"""
        results = {}
        
        self.logger.info("Starting legacy data conversion...")
        self.logger.info(f"Target files: {self.target_files}")
        self.logger.info(f"Backup directory: {self.backup_dir}")
        
        for filename in self.target_files:
            file_path = self.data_dir / filename
            
            if not file_path.exists():
                self.logger.warning(f"File not found: {file_path}")
                results[filename] = False
                continue
            
            # Convert file
            success = self.convert_file(file_path)
            results[filename] = success
            
            if success:
                # Validate conversion
                validation_success = self.validate_conversion(file_path)
                if not validation_success:
                    self.logger.warning(f"Validation failed for {filename}")
            
        return results
    
    def print_summary(self, results: Dict[str, bool]):
        """Print conversion summary"""
        print("\n" + "="*60)
        print("📊 LEGACY DATA CONVERSION SUMMARY")
        print("="*60)
        
        successful = [f for f, success in results.items() if success]
        failed = [f for f, success in results.items() if not success]
        
        print(f"\n✅ Successfully converted: {len(successful)}")
        for filename in successful:
            print(f"  • {filename}")
        
        if failed:
            print(f"\n❌ Failed to convert: {len(failed)}")
            for filename in failed:
                print(f"  • {filename}")
        
        print(f"\n📁 Backup location: {self.backup_dir}")
        print(f"📈 Success rate: {len(successful)}/{len(results)} ({len(successful)/len(results)*100:.1f}%)")
        
        if len(successful) == len(results):
            print("\n🎉 All files converted successfully!")
            print("✅ Data is now ready for use with the new CLI commands.")
        else:
            print(f"\n⚠️  {len(failed)} file(s) failed conversion.")
            print("Please check the logs for details.")


def main():
    """Main conversion function"""
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🔄 CISA Data Legacy Format Converter")
    print("="*60)
    print("This script converts existing JSON data files to the new")
    print("standardized field format (English fields, Chinese display).")
    print()
    
    # Get data directory from command line or use default
    data_dir = sys.argv[1] if len(sys.argv) > 1 else "data"
    
    # Create converter
    converter = LegacyDataConverter(data_dir)
    
    # Convert all files
    results = converter.convert_all_files()
    
    # Print summary
    converter.print_summary(results)
    
    # Return appropriate exit code
    all_successful = all(results.values())
    return 0 if all_successful else 1


if __name__ == "__main__":
    exit(main())
