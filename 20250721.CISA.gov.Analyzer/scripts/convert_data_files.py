#!/usr/bin/env python3
"""
Simple Data Conversion Script
Converts all cisa_*.json files to standardized field format
"""
import json
import shutil
from pathlib import Path
from datetime import datetime
import sys

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent / 'src'
sys.path.insert(0, str(src_dir))

from field_standardizer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def convert_file(file_path: Path, standardizer: FieldStandardizer):
    """Convert a single JSON file"""
    print(f"Converting {file_path.name}...")
    
    # Create backup
    backup_dir = file_path.parent / "backups" / f"conversion_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    backup_dir.mkdir(parents=True, exist_ok=True)
    backup_path = backup_dir / file_path.name
    shutil.copy2(file_path, backup_path)
    print(f"  Backup created: {backup_path}")
    
    # Load original data
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Determine data key
    data_keys = ['services', 'groups', 'programs', 'resources', 'trainings']
    data_key = None
    for key in data_keys:
        if key in data:
            data_key = key
            break
    
    if not data_key:
        print(f"  Error: No valid data key found in {file_path}")
        return False
    
    # Convert items
    original_items = data[data_key]
    converted_items = []
    
    for item in original_items:
        converted_item = standardizer.standardize_item(item)
        converted_items.append(converted_item)
    
    # Update data
    data[data_key] = converted_items
    
    # Update metadata
    if 'metadata' not in data:
        data['metadata'] = {}
    
    data['metadata']['converted_at'] = datetime.now().isoformat()
    data['metadata']['field_format'] = 'standardized_english'
    data['metadata']['analyzer_version'] = '2.0.0'
    
    # Save converted data
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"  Converted {len(converted_items)} items")
    print(f"  Saved to {file_path}")
    
    return True


def main():
    """Main conversion function"""
    print("🔄 CISA Data File Converter")
    print("=" * 50)
    
    # Initialize standardizer
    standardizer = FieldStandardizer()
    
    # Find all cisa_*.json files
    data_dir = Path("data")
    json_files = list(data_dir.glob("cisa_*.json"))
    
    if not json_files:
        print("No cisa_*.json files found in data directory")
        return 1
    
    print(f"Found {len(json_files)} files to convert:")
    for file_path in json_files:
        print(f"  • {file_path.name}")
    
    print()
    
    # Convert each file
    success_count = 0
    for file_path in json_files:
        try:
            if convert_file(file_path, standardizer):
                success_count += 1
            print()
        except Exception as e:
            print(f"  Error converting {file_path}: {e}")
            print()
    
    # Summary
    print("=" * 50)
    print(f"✅ Successfully converted: {success_count}/{len(json_files)} files")
    
    if success_count == len(json_files):
        print("🎉 All files converted successfully!")
        print("✅ Data is now ready for CLI commands.")
    else:
        print(f"⚠️  {len(json_files) - success_count} file(s) failed conversion.")
    
    return 0 if success_count == len(json_files) else 1


if __name__ == "__main__":
    exit(main())
