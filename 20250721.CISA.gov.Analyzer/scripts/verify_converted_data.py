#!/usr/bin/env python3
"""
Verification Script for Converted Data
Tests that all converted JSON files work correctly with CLI commands
"""
import subprocess
import sys
import json
from pathlib import Path
from typing import List, Dict, Any


def run_cli_command(json_file: Path) -> Dict[str, Any]:
    """Run CLI export command and return results"""
    try:
        # Run the CLI command with proxy settings
        cmd = [
            "uv", "run", "python", "cisa_cli.py", "export", str(json_file)
        ]

        import os
        env = os.environ.copy()
        env.update({
            "HTTP_PROXY": "http://127.0.0.1:8118/",
            "HTTPS_PROXY": "http://127.0.0.1:8118/"
        })

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            env=env,
            timeout=60
        )
        
        return {
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
        
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "stdout": "",
            "stderr": "Command timed out",
            "returncode": -1
        }
    except Exception as e:
        return {
            "success": False,
            "stdout": "",
            "stderr": str(e),
            "returncode": -1
        }


def verify_json_structure(json_file: Path) -> Dict[str, Any]:
    """Verify JSON file has correct structure"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Check metadata
        metadata = data.get("metadata", {})
        has_conversion_info = "field_format" in metadata and metadata["field_format"] == "standardized_english"
        
        # Find data key
        data_keys = ['services', 'groups', 'programs', 'resources', 'trainings']
        data_key = None
        for key in data_keys:
            if key in data:
                data_key = key
                break
        
        if not data_key:
            return {
                "valid": False,
                "error": "No valid data key found",
                "data_key": None,
                "item_count": 0,
                "has_conversion_info": has_conversion_info
            }
        
        items = data[data_key]
        
        # Check first item structure
        sample_fields = set()
        if items:
            sample_fields = set(items[0].keys())
        
        # Expected standard fields
        expected_fields = {
            'cisa_official_name', 'url', 'description', 'domain', 
            'target_audience', 'is_cybersecurity_related', 'is_cisa_official'
        }
        
        has_standard_fields = expected_fields.issubset(sample_fields)
        
        return {
            "valid": True,
            "error": None,
            "data_key": data_key,
            "item_count": len(items),
            "has_conversion_info": has_conversion_info,
            "sample_fields": list(sample_fields),
            "has_standard_fields": has_standard_fields
        }
        
    except Exception as e:
        return {
            "valid": False,
            "error": str(e),
            "data_key": None,
            "item_count": 0,
            "has_conversion_info": False
        }


def verify_markdown_output(json_file: Path) -> Dict[str, Any]:
    """Verify that markdown file was generated correctly"""
    # Determine expected markdown file name
    md_file = json_file.parent / f"{json_file.stem}_table.md"
    
    if not md_file.exists():
        return {
            "exists": False,
            "error": f"Markdown file {md_file} not found"
        }
    
    try:
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for expected content
        has_header = content.startswith('#')
        has_table = '|' in content
        has_chinese_headers = any(char for char in content if '\u4e00' <= char <= '\u9fff')
        
        return {
            "exists": True,
            "error": None,
            "has_header": has_header,
            "has_table": has_table,
            "has_chinese_headers": has_chinese_headers,
            "size": len(content)
        }
        
    except Exception as e:
        return {
            "exists": True,
            "error": str(e)
        }


def main():
    """Main verification function"""
    print("🔍 Converted Data Verification")
    print("=" * 50)
    
    # Find all cisa_*.json files
    data_dir = Path("data")
    json_files = list(data_dir.glob("cisa_*.json"))
    
    if not json_files:
        print("❌ No cisa_*.json files found in data directory")
        return 1
    
    print(f"Found {len(json_files)} files to verify:")
    for file_path in json_files:
        print(f"  • {file_path.name}")
    
    print()
    
    # Verify each file
    all_passed = True
    results = []
    
    for json_file in json_files:
        print(f"🔍 Verifying {json_file.name}...")
        
        # 1. Verify JSON structure
        json_result = verify_json_structure(json_file)
        print(f"  📄 JSON structure: {'✅' if json_result['valid'] else '❌'}")
        
        if json_result['valid']:
            print(f"    • Data type: {json_result['data_key']}")
            print(f"    • Item count: {json_result['item_count']}")
            print(f"    • Standard fields: {'✅' if json_result['has_standard_fields'] else '❌'}")
            print(f"    • Conversion info: {'✅' if json_result['has_conversion_info'] else '❌'}")
        else:
            print(f"    • Error: {json_result['error']}")
            all_passed = False
        
        # 2. Test CLI command
        cli_result = run_cli_command(json_file)
        print(f"  🖥️  CLI export: {'✅' if cli_result['success'] else '❌'}")
        
        if not cli_result['success']:
            print(f"    • Error: {cli_result['stderr']}")
            all_passed = False
        
        # 3. Verify markdown output
        md_result = verify_markdown_output(json_file)
        print(f"  📝 Markdown output: {'✅' if md_result['exists'] and not md_result.get('error') else '❌'}")
        
        if md_result['exists'] and not md_result.get('error'):
            print(f"    • Has table: {'✅' if md_result['has_table'] else '❌'}")
            print(f"    • Chinese headers: {'✅' if md_result['has_chinese_headers'] else '❌'}")
            print(f"    • Size: {md_result['size']} chars")
        elif md_result.get('error'):
            print(f"    • Error: {md_result['error']}")
            all_passed = False
        
        results.append({
            "file": json_file.name,
            "json_valid": json_result['valid'],
            "cli_success": cli_result['success'],
            "markdown_ok": md_result['exists'] and not md_result.get('error')
        })
        
        print()
    
    # Summary
    print("=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    for result in results:
        status = "✅" if all([result['json_valid'], result['cli_success'], result['markdown_ok']]) else "❌"
        print(f"{status} {result['file']}")
        print(f"    JSON: {'✅' if result['json_valid'] else '❌'} | "
              f"CLI: {'✅' if result['cli_success'] else '❌'} | "
              f"Markdown: {'✅' if result['markdown_ok'] else '❌'}")
    
    print()
    
    if all_passed:
        print("🎉 All files passed verification!")
        print("✅ Converted data is ready for production use.")
        print("✅ CLI commands work correctly with all files.")
        print("✅ Markdown export generates proper Chinese headers.")
    else:
        print("⚠️  Some files failed verification.")
        print("Please check the errors above and fix any issues.")
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    exit(main())
