[project]
name = "20250721-cisa-gov-analyzer"
version = "0.1.0"
description = "CISA.gov Services Analyzer - Automated tool for scraping and analyzing CISA website services"
requires-python = ">=3.12"
dependencies = [
    "aiofiles>=23.0.0",
    "anthropic>=0.7.0",
    "beautifulsoup4>=4.12.0",
    "google-generativeai>=0.3.0",
    "graphchain>=1.4.0",
    "graphlang>=0.0.1",
    "httpx>=0.25.0",
    "loguru>=0.7.3",
    "lxml>=4.9.0",
    "ollama>=0.1.0",
    "openai>=1.0.0",
    "pandas>=2.3.1",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "rich>=14.0.0",
    "typer>=0.16.0",
]
