# CISA All Resources & Tools 快速开始指南

## 功能概述

新增的 `all_resources_tools` 功能可以爬取 CISA 官网 https://www.cisa.gov/resources-tools/all-resources-tools 上的所有资源和工具信息，包括：

- **Services** (服务)
- **Programs** (项目)  
- **Resources** (资源)
- **Training** (培训)
- **Groups** (组织)
- **External Resources** (外部资源)

## 快速使用

### 1. 完整分析（一键执行）
```bash
# 使用代理（如果需要）
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py full --type all_resources_tools
```

### 2. 分步骤分析（推荐）
```bash
# 启动分步骤模式
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py step --type all_resources_tools

# 然后按照提示选择：
# 1. ⏸️ 抓取所有资源和工具列表
# 2. ⏸️ 抓取所有资源和工具内容 (需要前置步骤)
# 3. ⏸️ LLM分析所有资源和工具 (需要前置步骤)
# 4. ⏸️ 图形和链式分析 (需要前置步骤)
# 5. ⏸️ 保存最终结果 (需要前置步骤)
# 6. ⏸️ 导出Markdown表格 (需要前置步骤)
```

### 3. 测试功能
```bash
# 运行测试脚本
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ python3 test/test_all_resources_tools.py

# 运行演示脚本
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ python3 test/demo_all_resources_tools.py
```

## 输出结果

### 数据文件
分析完成后，结果将保存在：
- **JSON文件**: `data/cisa_all_resourcess_and_tools_info.json`

### 数据结构
每个项目包含以下信息：
```json
{
  "cisa_official_name": "项目名称",
  "url": "完整URL链接",
  "publication_date": "发布日期（如果可获取）",
  "item_type": "项目类型（Service/Program/Resource/Training/Group/Unknown）",
  "is_cisa_official": "是否为CISA官方提供（true/false）"
}
```

### 导出功能
```bash
# 导出为Markdown表格
python3 cisa_cli.py export data/cisa_all_resourcess_and_tools_info.json

# 导出为CSV文件
python3 cisa_cli.py export data/cisa_all_resourcess_and_tools_info.json --format csv
```

## 配置选项

可以通过环境变量调整配置：

```bash
# 设置最大页数（默认200页）
export MAX_ALL_RESOURCES_TOOLS_PAGES=100

# 设置输出文件路径
export ALL_RESOURCES_TOOLS_OUTPUT_FILE=data/my_custom_output.json

# 设置请求延迟（秒）
export REQUEST_DELAY=2.0
```

## 技术特点

### 1. 智能页面检测
- 自动检测总页数（当前约183页）
- 支持动态调整爬取范围

### 2. 数据提取能力
- **项目名称**: 从链接文本提取
- **完整URL**: 自动拼接为绝对路径
- **发布日期**: 使用多种正则表达式模式提取
- **项目类型**: 基于URL路径智能识别
- **来源标识**: 区分CISA官方和外部资源

### 3. 去重和优化
- 自动去除重复项目
- 跳过导航和过滤链接
- 智能容器识别

### 4. 错误处理
- 网络重试机制
- 代理支持
- 详细日志记录

## 示例输出

成功运行后，你将看到类似以下的输出：

```
🚀 启动 CISA All Resources & Tools 功能演示

📡 初始化爬虫...
✅ 目标URL: https://www.cisa.gov/resources-tools/all-resources-tools

📊 检测总页数...
✅ 发现 183 页数据

🔍 演示：抓取前3页数据...
✅ 成功抓取 171 个项目

📋 数据分析:
  📊 按类型统计:
    Resource: 145
    Unknown: 26
  📅 有发布日期的项目: 0/171 (0.0%)
  🔗 外部提供的项目: 171/171 (100.0%)

💾 保存数据...
✅ 数据已保存到: data/cisa_all_resourcess_and_tools_info.json
📊 包含 171 个项目的数据

🎉 演示完成!
```

## 故障排除

### 1. 网络问题
如果遇到网络连接问题，确保代理设置正确：
```bash
export HTTP_PROXY=http://127.0.0.1:8118/
export HTTPS_PROXY=http://127.0.0.1:8118/
```

### 2. 依赖问题
确保所有依赖已安装：
```bash
uv sync
```

### 3. 权限问题
确保有写入data目录的权限：
```bash
chmod 755 data/
```

## 进阶使用

### 1. 自定义爬取范围
```python
# 在代码中临时修改配置
from src.config import Config
Config.MAX_ALL_RESOURCES_TOOLS_PAGES = 50  # 只爬取前50页
```

### 2. 集成到现有工作流
```bash
# 与其他分析类型结合使用
python3 cisa_cli.py step --type both  # 先分析services和programs
python3 cisa_cli.py step --type all_resources_tools  # 再分析所有资源
```

### 3. 数据后处理
生成的JSON文件可以轻松集成到其他数据处理流程中，支持标准的JSON解析和处理。

---

**注意**: 由于目标网站有183页数据，完整爬取可能需要较长时间。建议首次使用时先运行演示脚本测试功能。
