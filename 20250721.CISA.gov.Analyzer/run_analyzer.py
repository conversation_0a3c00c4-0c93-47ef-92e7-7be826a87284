#!/usr/bin/env python3
"""
CISA Services Analyzer Runner
Convenient script to run the analyzer
"""
import os
import sys
import asyncio

# Add src directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

# Import and run main
from src.main import main

if __name__ == "__main__":
    print("=" * 60)
    print("CISA Services Analyzer")
    print("=" * 60)
    print()
    
    # Check if .env file exists
    env_file = os.path.join(current_dir, '.env')
    if not os.path.exists(env_file):
        print("Warning: .env file not found!")
        print("Please copy .env.example to .env and configure your settings.")
        print()
        
        # Ask user if they want to continue
        response = input("Continue anyway? (y/N): ").strip().lower()
        if response != 'y':
            print("Exiting...")
            sys.exit(1)
    
    print("Starting analysis...")
    print()

    success = asyncio.run(main())

    print()
    if success:
        print("✅ Analysis completed successfully!")
        print(f"Results saved to: data/cisa_services.json")
    else:
        print("❌ Analysis failed. Check logs for details.")

    print("=" * 60)
