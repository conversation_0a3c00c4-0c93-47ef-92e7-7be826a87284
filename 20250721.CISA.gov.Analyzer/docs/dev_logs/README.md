# 开发文档目录

此目录用于存放项目开发过程中的技术文档和决策记录。

## 文档分类

### 开发过程记录
- [开发需求记录](20250723_development_prompts.md) - 项目开发过程中的需求和提示词记录
- [项目清理总结](CLEANUP_SUMMARY.md) - 项目结构清理和优化记录
- [UV 迁移记录](MIGRATION_TO_UV.md) - 包管理器迁移过程记录

### 功能改进记录
- [分页功能改进](20250723_pagination_improvement.md) - 自动分页发现功能的设计和实现
- [分页问题修复](20250724_pagination_fix.md) - Resources 分页问题的诊断和修复
- [统一配置修复](20250724_unified_config.md) - 配置参数统一化的实现

## 文档编写规范

### 命名规范
- 使用日期前缀：`YYYYMMDD_功能描述.md`
- 例如：`20250724_pagination_fix.md`
- 重要的总结性文档可以使用描述性名称，如：`CLEANUP_SUMMARY.md`

### 内容结构
每个开发文档应包含以下部分：

```markdown
# 标题

**日期**: YYYY-MM-DD  
**类型**: 功能改进/问题修复/技术决策  
**状态**: 进行中/已完成/已取消

## 问题描述/需求背景
描述要解决的问题或实现的需求

## 解决方案/实现方案
详细描述技术方案和实现细节

## 实现结果
记录实现效果和验证结果

## 技术决策
记录重要的技术选择和原因

## 相关文件
列出涉及的代码文件
```

### 内容要求
- 使用中文编写，代码示例中的注释使用英文
- 记录技术决策的原因和考虑因素
- 包含必要的代码示例和配置说明
- 记录验证方法和测试结果
- 保持文档的及时更新

## 文档维护

### 定期整理
- 每月整理一次文档结构
- 归档过时的文档
- 更新索引和链接

### 版本控制
- 所有开发文档都应纳入版本控制
- 重要修改应记录变更原因
- 保持文档与代码的同步更新

## 相关链接

- [用户文档](../README.md) - 用户文档索引
- [调试日志](../debug_logs/) - 程序运行日志
- [项目根目录](../../README.md) - 项目主要说明
