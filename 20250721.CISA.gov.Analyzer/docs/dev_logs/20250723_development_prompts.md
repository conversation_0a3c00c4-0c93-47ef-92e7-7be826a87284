# 开发过程提示词记录

**日期**: 2025-07-23  
**类型**: 开发需求记录  
**状态**: 已完成

## 提示词 1 - 基础功能需求

1. 以 https://www.cisa.gov/resources-tools/services?page=0 为入口获取 CISA 提供的所有服务（Services），其中 page 取值范围可以是 0 到 29
2. 上述页面获取每个服务的基础信息，包括：CISA 官方名称、URL、是否 CISA 官方提供。非 CISA 提供的服务，在上述页面中会标记为"EXTERNAL PROVIDER"
3. 访问每个服务的 URL，抓取页面内容，调用 LLM 大模型进行分析，输出每个服务的以下信息（除"URL"和"CISA 官方名称"以外，都使用中文）：
   1. CISA 官方名称
   2. URL
   3. 服务简介
   4. 发布日期
   5. 对应领域
   6. 目标受众
   7. 是否网络安全相关
   8. 是否CISA官方提供
4. 将 LLM 大模型上面输出的字段的输出以 json 格式保存到 20250721.CISA.gov.Analyzer/data/cisa_services.json 中
5. 要遵循 20250721.CISA.gov.Analyzer/.augmentrules 20250721.CISA.gov.Analyzer/README.md 中的要求

## 提示词 2 - 技术栈要求

1. 使用 httpx、graphlang、graphchain 等软件包
2. 需要支持不同 LLM 模型，例如：QWEN3、DeepSeek、Ollama、Gemini 等

调整 src 下的代码，符合上述要求

## 提示词 3 - 功能扩展

1. 增加功能：可以把保存的 json 文件转换成 markdown 格式的表格，表格有表头，表头为 json 字段名对应的中文
2. 增加功能：使得可以分步骤独立完成，而不是必须从头到尾完整执行

## 提示词 4 - Programs 功能

在 20250721.CISA.gov.Analyzer 目录执行命令 HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py full --type programs 报错，最后屏幕显示如下：

【错误信息】

结合错误信息，分析 5 个可能的错误原因，从可能性由高到低：
1. 添加适当的调试日志，以便后续判断错误是否由此导致
2. 全面分析代码，给出不改变原有业务流程、功能、输入输出，且不会带来新问题的代码修改方案
所有对代码和文档操作必须遵循 20250721.CISA.gov.Analyzer/.augmentrules 20250721.CISA.gov.Analyzer/README.md 中的要求

## 提示词 5 - Groups 功能

增加 Groups 功能，参考 Services 和 Programs 的实现方式，实现对 CISA Groups 的分析功能。

要求：
1. 创建 Groups 相关的爬虫和主程序
2. 支持完整的分析流程
3. 输出格式与其他功能保持一致
4. 遵循项目的代码组织规范

## 提示词 6 - Resources 功能

实现 CISA Resources 分析功能：

1. 抓取 https://www.cisa.gov/resources-tools/resources 页面的所有资源
2. 支持完整的 LLM 分析流程
3. 输出中文字段信息
4. 保存到 data/cisa_resources.json

## 提示词 7 - Trainings 功能

实现 CISA Trainings 分析功能：

1. 抓取 https://www.cisa.gov/resources-tools/training 页面的所有培训信息
2. 使用 LLM 分析培训内容
3. 输出结构化的中文分析结果
4. 保存到 data/cisa_trainings.json

## 提示词 8 - Markdown 导出功能优化

请分析导出 markdown 格式表格的代码，以不改变原有业务流程、功能、输入输出，且不会带来新问题的为前提，对代码进行优化。

所有对代码和文档操作必须遵循 20250721.CISA.gov.Analyzer/.augmentrules 20250721.CISA.gov.Analyzer/README.md 中的要求

## 提示词 9

新增功能：增加将 JSON 数据导出为 csv 格式表格的功能。支持将 groups、programs、resources、services、trainings 的数据导出为 csv 。输出 json 中所有内容，不要截短。

全面分析代码，给出对原有业务流程、功能、输入输出影响最小的方案，且不会带来新问题的代码修改方案。
所有对代码和文档操作必须遵循 20250721.CISA.gov.Analyzer/.augmentrules 20250721.CISA.gov.Analyzer/README.md 中的要求。

## 提示词 10

爬取 https://www.cisa.gov/resources-tools/all-resources-tools?page=0 所有的页面，抓取所有项目名称、链接、发布时间（有部分无法抓取到时间）并保存为 data/cisa_all_resourcess_and_tools_info.json

全面分析代码，给出对原有业务流程、功能、输入输出影响最小的方案，且不会带来新问题的代码修改方案。
所有对代码和文档操作必须遵循 20250721.CISA.gov.Analyzer/.augmentrules 20250721.CISA.gov.Analyzer/README.md 中的要求。

##

新增功能：在抓取并分析 groups、programs、resources、services、trainings 对应的索引页，提取 groups、programs、resources、services、trainings 详细信息页面 URL 时，应该从尝试访问 all_resources_tools 对应的 json 文件。若文件存在，则应该从 all_resources_tools 对应的 json 文件中提取同一 URL 对应的 publication_date 作为对应 groups、programs、resources、services、trainings 的 publication_date 输入，除非 groups、programs、resources、services、trainings 详细页面分析获得了 publication_date，否则来自 all_resources_tools 对应 json 文件的 publication_date 就成为这个 URL 对应的 groups、programs、resources、services、trainings 的 publication_date 。

全面分析代码，给出对原有业务流程、功能、输入输出影响最小的方案，且不会带来新问题的代码修改方案。
所有对代码和文档操作必须遵循 20250721.CISA.gov.Analyzer/.augmentrules 20250721.CISA.gov.Analyzer/README.md 中的要求。

## 实现状态

- ✅ 基础 Services 功能
- ✅ Markdown 导出功能
- ✅ 分步骤执行功能
- ✅ Programs 功能
- ✅ Groups 功能
- ✅ Resources 功能
- ✅ Trainings 功能
- ✅ 多 LLM 提供商支持
- ✅ 统一 CLI 工具

## 技术决策记录

### 代码组织
- 采用模块化设计，每个功能独立实现
- 使用统一的基类和配置管理
- 遵循项目的命名规范

### 功能实现
- 支持多种分析类型（Services、Programs、Groups、Resources、Trainings）
- 实现分步骤执行和状态管理
- 提供灵活的 CLI 接口

### 技术栈
- 使用 httpx 进行网络请求
- 支持多种 LLM 提供商
- 使用 uv 进行包管理
- 遵循代理配置要求
