# CISA 资源分析功能使用说明

## 功能概述

CISA 资源分析功能可以从 CISA 官网 (https://www.cisa.gov/resources-tools/resources) 自动抓取和分析所有资源信息，支持从第 0 页到第 147 页的完整数据抓取。

## 功能特性

✨ **完整的资源抓取**
- 自动抓取 CISA 官网所有资源页面（0-147页）
- 智能提取资源基础信息（名称、URL、是否官方提供）
- 自动访问每个资源的详细页面获取完整内容

🤖 **智能 LLM 分析**
- 使用大语言模型分析每个资源的详细内容
- 自动提取中文字段信息
- 支持多种 LLM 提供商（OpenAI、Anthropic、DeepSeek 等）

📊 **结构化输出**
- 输出格式完全符合要求的 JSON 结构
- 支持中文字段名称
- 自动保存到指定文件

## 输出字段说明

每个资源的分析结果包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `cisa_official_name` | 字符串 | CISA 官方名称（英文） |
| `url` | 字符串 | 资源页面 URL |
| `资源简介` | 字符串 | 资源简介（中文） |
| `发布日期` | 字符串 | 发布日期（YYYY-MM-DD格式，如无则为null） |
| `对应领域` | 字符串 | 对应领域（中文） |
| `目标受众` | 字符串 | 目标受众（中文） |
| `是否网络安全相关` | 布尔值 | 是否与网络安全相关 |
| `是否CISA官方提供` | 布尔值 | 是否为 CISA 官方提供 |

## 使用方法

### 1. 环境准备

确保已安装依赖并配置环境：

```bash
# 安装依赖
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv sync

# 配置 LLM API 密钥（在 .env 文件中）
LLM_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_api_key_here
```

### 2. 使用 CLI 工具（推荐）

#### 分步骤执行模式
```bash
# 基础用法
python3 cisa_cli.py step --type resources

# 从指定步骤开始
python3 cisa_cli.py step --type resources --from-step scrape_content

# 使用 analyze 命令
python3 cisa_cli.py analyze resources --mode step
```

#### 完整分析模式
```bash
# 一次性完成所有步骤
python3 cisa_cli.py full --type resources

# 使用 analyze 命令
python3 cisa_cli.py analyze resources --mode full
```

### 3. 直接运行主程序

```bash
# 运行资源分析主程序
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ python3 src/cisa_resources_main.py
```

### 4. 演示和测试

```bash
# 运行演示脚本（仅处理前几页）
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ python3 test/demo_resources.py

# 运行测试脚本
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ python3 test/test_resources.py
```

## 输出文件

分析完成后，结果将保存在以下文件中：

- **主要输出**: `data/cisa_resources.json`
- **演示输出**: `data/demo_cisa_resources.json`
- **测试输出**: `data/test_cisa_resources.json`

## 输出示例

```json
{
  "metadata": {
    "total_resources": 1500,
    "analysis_type": "resources",
    "timestamp": "2025-07-24T02:07:39.854047"
  },
  "resources": [
    {
      "cisa_official_name": "Explosives Sweeps Guide",
      "url": "https://www.cisa.gov/resources-tools/resources/explosives-sweeps-guide",
      "资源简介": "该指南为炸弹小队和爆炸物探测犬团队在特殊活动中的工作提供了最佳实践...",
      "发布日期": "2025-07-10",
      "对应领域": "物理安全",
      "目标受众": "政府机构, 企业, 教育机构",
      "是否网络安全相关": false,
      "是否CISA官方提供": false
    }
  ]
}
```

## 配置选项

可以通过环境变量调整分析行为：

```bash
# 并发控制
MAX_CONCURRENT_REQUESTS=5          # 内容抓取并发数
MAX_CONCURRENT_LLM_REQUESTS=3      # LLM 分析并发数
REQUEST_DELAY=2.0                  # 请求间延迟（秒）

# 输出配置
RESOURCES_OUTPUT_FILE=data/cisa_resources.json

# 页面限制（安全限制）
MAX_RESOURCES_PAGES=148            # 最大页面数（0-147）
```

## 注意事项

1. **网络代理**: 由于网络限制，所有网络请求都需要通过代理
2. **LLM 配置**: 需要有效的 LLM API 密钥才能进行内容分析
3. **执行时间**: 完整分析所有 148 页可能需要数小时
4. **并发控制**: 建议使用保守的并发设置避免被网站限制
5. **数据量**: 预计会产生大量数据，确保有足够的存储空间

## 故障排除

### 常见问题

1. **网络连接失败**
   - 确保代理配置正确
   - 检查网络连接

2. **LLM 分析失败**
   - 检查 API 密钥配置
   - 确认 LLM 服务可用

3. **内存不足**
   - 减少并发数量
   - 分批处理数据

### 日志查看

分析过程中的详细日志保存在：
- `docs/debug_logs/cisa_resources_*.log`

## 技术实现

- **爬虫**: `src/cisa_resources_scraper.py`
- **内容抓取**: `src/content_scraper.py`
- **LLM 分析**: `src/llm_analyzer.py`
- **数据管理**: `src/data_manager.py`
- **主程序**: `src/cisa_resources_main.py`

## 相关文档

- [主要使用说明](../README.md)
- [新功能说明](NEW_FEATURES.md)
- [并发控制文档](concurrency_control.md)
