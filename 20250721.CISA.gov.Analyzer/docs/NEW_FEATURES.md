# CISA Services Analyzer 新功能说明

## 概述

本文档介绍了 CISA Services Analyzer 的新增功能：

1. **🎯 多类型分析支持** - 支持 Services、Programs 或同时分析
2. **🚀 灵活的执行控制** - 指定分析类型和起始步骤
3. **📊 JSON转Markdown表格功能** - 将保存的JSON文件转换成带中文表头的Markdown表格
4. **🔄 分步骤独立执行功能** - 支持分步骤独立完成分析，而不是必须从头到尾完整执行

## 新增功能详情

### 1. 🎯 多类型分析支持

#### 功能描述
- 支持单独分析 **Services**（服务）
- 支持单独分析 **Programs**（项目）
- 支持同时分析 **Both**（服务和项目）
- 每种类型有独立的状态管理和数据存储

#### 使用方法

**指定分析类型：**
```bash
# 分析 Services（默认）
python3 cisa_cli.py step --type services
python3 cisa_cli.py full --type services

# 分析 Programs
python3 cisa_cli.py step --type programs
python3 cisa_cli.py full --type programs

# 同时分析 Services 和 Programs
python3 cisa_cli.py step --type both
python3 cisa_cli.py full --type both
```

**输出文件：**
- Services: `data/cisa_services.json`
- Programs: `data/cisa_programs.json`
- Both: 两个文件都会生成

### 2. 🚀 灵活的执行控制

#### 功能描述
- 支持从任意步骤开始执行
- 支持指定分析类型和起始步骤的组合
- 新增 `analyze` 命令提供更灵活的参数组合

#### 使用方法

**从指定步骤开始：**
```bash
# 从内容抓取步骤开始
python3 cisa_cli.py step --from-step scrape_content

# 从 LLM 分析步骤开始（指定分析 Programs）
python3 cisa_cli.py step --type programs --from-step analyze_llm

# 从保存结果步骤开始
python3 cisa_cli.py step --from-step save_results
```

**使用新的 analyze 命令：**
```bash
# 基础用法
python3 cisa_cli.py analyze services --mode step
python3 cisa_cli.py analyze programs --mode full

# 高级用法
python3 cisa_cli.py analyze both --mode step --from-step scrape_content
```

**可用的步骤名称：**
- `scrape_services` - 抓取服务/项目列表
- `scrape_content` - 抓取详细内容
- `analyze_llm` - LLM 分析
- `graph_analysis` - 图形和链式分析
- `save_results` - 保存最终结果
- `export_markdown` - 导出 Markdown 表格

### 3. 📊 JSON转Markdown表格功能

#### 功能描述
- 将JSON格式的服务数据转换为易读的Markdown表格
- 支持中文表头，字段名自动映射为中文
- 可自定义包含的字段和描述长度
- 自动生成元数据信息（生成时间、数据来源、服务总数等）

#### 字段映射
| 英文字段名 | 中文表头 |
|-----------|----------|
| cisa_official_name | CISA官方名称 |
| url | 链接地址 |
| service_description | 服务描述 |
| publication_date | 发布日期 |
| domain | 领域 |
| target_audience | 目标受众 |
| is_cybersecurity_related | 网络安全相关 |
| is_cisa_official | CISA官方服务 |

#### 使用方法

**通过CLI工具使用：**
```bash
# 基本用法 - 使用默认设置
python3 cisa_cli.py export data/cisa_services.json

# 指定输出文件
python3 cisa_cli.py export data/cisa_services.json -o my_table.md

# 自定义字段
python3 cisa_cli.py export data/cisa_services.json -f cisa_official_name domain service_description

# 设置描述最大长度
python3 cisa_cli.py export data/cisa_services.json -l 500
```

**通过Python代码使用：**
```python
from src.markdown_exporter import MarkdownExporter

exporter = MarkdownExporter()
success = exporter.export_to_markdown(
    input_file='data/cisa_services.json',
    output_file='output_table.md',
    fields=['cisa_official_name', 'domain', 'service_description'],
    max_description_length=300
)
```

### 4. 🔄 分步骤独立执行功能

#### 功能描述
- 将完整的分析流程分解为6个独立步骤
- 支持从任意步骤开始执行
- 自动保存和恢复中间状态
- 提供状态管理和进度跟踪

#### 分析步骤
1. **抓取服务列表** - 从CISA网站获取所有服务的基本信息
2. **抓取服务内容** - 获取每个服务页面的详细内容
3. **LLM分析服务** - 使用大语言模型分析和结构化服务信息
4. **图形和链式分析** - 可选的高级分析功能
5. **保存最终结果** - 验证并保存分析结果到JSON文件
6. **导出Markdown表格** - 生成Markdown格式的表格文件

#### 使用方法

**启动分步骤执行模式：**
```bash
python3 cisa_cli.py step
```

**查看当前状态：**
```bash
python3 cisa_cli.py status
```

**列出备份文件：**
```bash
python3 cisa_cli.py list
```

**清除分析状态：**
```bash
python3 cisa_cli.py clear
```

#### 分步骤执行界面
启动分步骤模式后，会显示交互式菜单：

```
============================================================
选择要执行的步骤:
============================================================
  1. ⏸️ 抓取服务列表
  2. ⏸️ 抓取服务内容 (需要前置步骤)
  3. ⏸️ LLM分析服务 (需要前置步骤)
  4. ⏸️ 图形和链式分析 (需要前置步骤)
  5. ⏸️ 保存最终结果 (需要前置步骤)
  6. ⏸️ 导出Markdown表格 (需要前置步骤)
  7. 🔄 从当前状态继续
  8. 📊 显示状态
  9. 🗑️  清除状态
  0. 退出
============================================================
```

## 新增文件说明

### 核心模块文件

1. **`src/markdown_exporter.py`** - Markdown导出功能模块
   - `MarkdownExporter` 类：负责JSON到Markdown的转换
   - 支持字段映射、格式化和文件生成

2. **`src/step_manager.py`** - 步骤管理模块
   - `StepManager` 类：管理分析步骤和状态持久化
   - `AnalysisStep` 枚举：定义所有分析步骤

3. **`src/step_main.py`** - 分步骤执行主程序
   - 包含每个步骤的独立执行函数
   - 提供交互式菜单和状态管理

### CLI工具

4. **`cisa_cli.py`** - 统一命令行界面
   - 整合所有功能的CLI工具
   - 支持多种操作模式和参数

## 数据持久化

### 状态文件
- **`data/analysis_state.json`** - 保存当前分析状态
- 包含当前步骤、时间戳、数据文件路径等信息

### 备份文件
- **`data/backups/`** - 存储各步骤的中间数据
- 文件命名格式：`{步骤名}_{时间戳}.json`
- 支持状态恢复和数据追溯

## 兼容性说明

### 向后兼容
- 原有的完整执行模式（`run_analyzer.py`）保持不变
- 现有的配置文件和数据格式完全兼容
- 不影响现有的工作流程

### 新旧模式对比
| 特性 | 原有模式 | 新增分步骤模式 |
|------|----------|----------------|
| 执行方式 | 一次性完整执行 | 可分步骤执行 |
| 状态保存 | 仅保存最终结果 | 保存每步中间状态 |
| 错误恢复 | 需要重新开始 | 可从失败步骤继续 |
| 灵活性 | 固定流程 | 可选择任意步骤 |
| 调试能力 | 有限 | 可单独测试各步骤 |

## 使用建议

### 适用场景

**使用分步骤模式的情况：**
- 首次运行或测试新配置
- 网络不稳定或资源受限环境
- 需要调试特定步骤
- 想要检查中间结果
- 长时间运行可能被中断

**使用完整模式的情况：**
- 配置已验证且稳定
- 网络和资源充足
- 生产环境的定期运行
- 自动化脚本调用

### 最佳实践

1. **首次使用建议**
   - 先使用分步骤模式验证配置
   - 检查每个步骤的输出结果
   - 确认无误后可切换到完整模式

2. **错误处理**
   - 遇到错误时查看日志文件
   - 使用 `cisa_cli.py status` 检查当前状态
   - 必要时使用 `cisa_cli.py clear` 重置状态

3. **数据管理**
   - 定期清理备份文件夹
   - 重要结果及时备份
   - 使用版本控制管理配置文件

## 示例工作流程

### 完整的分步骤执行示例

```bash
# 1. 查看当前状态
python3 cisa_cli.py status

# 2. 启动分步骤模式
python3 cisa_cli.py step
# 选择步骤1开始执行

# 3. 检查中间结果
python3 cisa_cli.py list

# 4. 继续执行剩余步骤
python3 cisa_cli.py step
# 选择"从当前状态继续"

# 5. 导出Markdown表格
python3 cisa_cli.py export data/cisa_services.json -l 120

# 6. 查看最终状态
python3 cisa_cli.py status
```

## 技术实现细节

### 状态管理机制
- 使用JSON文件持久化状态信息
- 每个步骤完成后自动保存状态
- 支持状态验证和依赖检查

### 数据流转
- 步骤间通过文件系统传递数据
- 支持数据的加载和验证
- 提供数据完整性检查

### 错误处理
- 每个步骤独立的异常处理
- 详细的日志记录
- 用户友好的错误提示

这些新功能大大提升了CISA Services Analyzer的易用性和灵活性，使用户能够更好地控制分析流程并获得更好的使用体验。
