# 文档目录

此目录用于存放项目相关的文档。

## 用户文档

### 核心功能文档
- [新功能说明](NEW_FEATURES.md) - 最新功能特性和使用方法
- [并发控制说明](concurrency_control.md) - 并发配置和性能优化

### 分类功能使用说明
- [Resources 分析功能](RESOURCES_USAGE.md) - CISA 资源分析功能详细说明
- [Trainings 分析功能](TRAININGS_USAGE.md) - CISA 培训分析功能详细说明

## 开发文档

### 开发过程记录 (`dev_logs/`)
- 记录开发过程中的重要决策
- 技术方案设计文档
- 功能改进和修复记录
- 开发进度记录

### 调试日志 (`debug_logs/`)
- 程序运行日志（自动生成）
- 错误日志和调试信息
- 性能分析报告

## 文档编写规范

### 用户文档规范
- 使用 Markdown 格式
- 文件名使用功能描述，如：`RESOURCES_USAGE.md`
- 内容面向用户，重点说明使用方法
- 使用中文编写，代码示例中的注释使用英文

### 开发文档规范
- 使用 Markdown 格式
- 文件名使用日期前缀，如：`20250724_功能设计.md`
- 记录技术决策和实现细节
- 保持文档的及时更新
- 使用中文编写，代码示例中的注释使用英文
