# 并发控制配置指南

## 概述

CISA Services Analyzer 现在支持通过 `.env` 配置文件控制并发数量，以避免并发过大被网站限制或导致网站异常。

## 配置参数

### 1. 内容抓取并发控制

```bash
# 内容抓取的最大并发请求数
MAX_CONCURRENT_REQUESTS=5
```

**说明：**
- 控制同时进行的网页内容抓取请求数量
- 过高可能导致被目标网站封IP或触发反爬虫机制
- 过低会影响抓取效率

**推荐值：**
- 保守：3-5（适用于稳定网站，避免被封）
- 适中：5-10（适用于健壮网站）
- 激进：10-20（仅适用于非常健壮的网站，有被封风险）

### 2. LLM 分析并发控制

```bash
# LLM 分析的最大并发请求数
MAX_CONCURRENT_LLM_REQUESTS=3
```

**说明：**
- 控制同时进行的 LLM API 调用数量
- 过高可能触发 API 速率限制
- 不同 LLM 提供商有不同的速率限制

**推荐值：**
- OpenAI：3-5（取决于你的订阅等级）
- Anthropic：2-3（保守设置）
- DeepSeek：3-5（适中设置）
- 本地模型（Ollama）：1-2（取决于硬件性能）

### 3. 请求延迟控制

```bash
# 请求之间的延迟时间（秒）
REQUEST_DELAY=2.0
```

**说明：**
- 每个请求完成后的等待时间
- 配合并发控制使用，进一步降低被封风险
- 延迟时间越长，抓取速度越慢但越安全

## 使用场景配置

### 1. 保守配置（避免被封）

```bash
MAX_CONCURRENT_REQUESTS=3
MAX_CONCURRENT_LLM_REQUESTS=2
REQUEST_DELAY=3.0
```

**适用场景：**
- 网络不稳定
- 目标网站反爬虫严格
- 首次使用，不确定网站容忍度

### 2. 平衡配置（推荐）

```bash
MAX_CONCURRENT_REQUESTS=5
MAX_CONCURRENT_LLM_REQUESTS=3
REQUEST_DELAY=2.0
```

**适用场景：**
- 大多数情况下的默认选择
- 平衡速度和稳定性
- 适合 CISA 官网等政府网站

### 3. 激进配置（快速但有风险）

```bash
MAX_CONCURRENT_REQUESTS=10
MAX_CONCURRENT_LLM_REQUESTS=5
REQUEST_DELAY=1.0
```

**适用场景：**
- 网络环境良好
- 目标网站容忍度高
- 需要快速完成大量数据处理

### 4. 极保守配置（网络不稳定）

```bash
MAX_CONCURRENT_REQUESTS=1
MAX_CONCURRENT_LLM_REQUESTS=1
REQUEST_DELAY=5.0
```

**适用场景：**
- 网络极不稳定
- 频繁出现连接错误
- 调试和测试阶段

## 监控和调优

### 1. 日志监控

程序会在日志中输出并发执行的信息：

```
INFO - Starting concurrent scraping of 150 services with max 5 concurrent requests
INFO - Completed scraping 150 services: 148 successful, 2 failed
INFO - Starting concurrent LLM analysis of 150 services with max 3 concurrent requests
INFO - Completed LLM analysis: 150 successful, 0 failed
```

### 2. 错误指标

如果出现以下情况，建议降低并发数：

- **HTTP 429 错误**：速率限制，降低并发数或增加延迟
- **连接超时频繁**：网络负载过高，降低并发数
- **大量请求失败**：可能被反爬虫系统检测，降低并发数并增加延迟

### 3. 性能指标

监控以下指标来优化配置：

- **成功率**：应保持在 95% 以上
- **平均响应时间**：不应显著增加
- **总执行时间**：在可接受范围内

## 实现原理

### 1. 信号量控制

使用 `asyncio.Semaphore` 控制并发数量：

```python
semaphore = asyncio.Semaphore(max_concurrent)
async with semaphore:
    # 执行实际操作
    result = await operation()
```

### 2. 异常处理

每个并发任务都有独立的异常处理：

```python
results = await asyncio.gather(*tasks, return_exceptions=True)
for result in results:
    if isinstance(result, Exception):
        # 处理异常，创建回退结果
        fallback_result = create_fallback(original_data)
```

### 3. 进度跟踪

实时跟踪并发操作的进度和成功率：

```python
successful = sum(1 for result in results if not isinstance(result, Exception))
failed = len(results) - successful
logger.info(f"Completed: {successful} successful, {failed} failed")
```

## 故障排除

### 1. 被网站封IP

**症状：** HTTP 403/429 错误频繁出现

**解决方案：**
- 降低 `MAX_CONCURRENT_REQUESTS` 到 1-3
- 增加 `REQUEST_DELAY` 到 5-10 秒
- 考虑使用代理服务器

### 2. LLM API 速率限制

**症状：** LLM 请求返回速率限制错误

**解决方案：**
- 降低 `MAX_CONCURRENT_LLM_REQUESTS` 到 1-2
- 检查 API 订阅等级和限制
- 考虑升级 API 订阅

### 3. 内存使用过高

**症状：** 系统内存占用过高

**解决方案：**
- 降低总并发数
- 分批处理数据
- 增加处理间隔

### 4. 网络不稳定

**症状：** 连接超时和网络错误频繁

**解决方案：**
- 使用极保守配置
- 增加 `REQUEST_TIMEOUT`
- 增加 `MAX_RETRIES`

## 最佳实践

1. **从保守配置开始**：首次使用时采用保守配置，逐步调优
2. **监控日志**：密切关注错误率和性能指标
3. **分阶段测试**：先用小数据集测试配置效果
4. **备份配置**：保存有效的配置组合
5. **定期调整**：根据网站变化和网络环境调整配置

## 配置示例

完整的 `.env` 配置示例：

```bash
# 平衡配置示例
MAX_CONCURRENT_REQUESTS=5
MAX_CONCURRENT_LLM_REQUESTS=3
REQUEST_DELAY=2.0
REQUEST_TIMEOUT=30
MAX_RETRIES=3

# 网络代理（如需要）
HTTP_PROXY=http://127.0.0.1:8118/
HTTPS_PROXY=http://127.0.0.1:8118/
```

通过合理配置这些参数，可以在保证稳定性的前提下最大化处理效率。
