# CISA 培训分析功能使用说明

## 功能概述

CISA 培训分析功能可以自动从 CISA 官网抓取所有培训信息，并使用 LLM 进行智能分析，输出结构化的中文分析结果。

## 主要特性

✅ **自动抓取**: 从 https://www.cisa.gov/resources-tools/training?page=0 到 page=9 抓取所有培训信息  
✅ **智能分析**: 使用 LLM 分析培训内容，提取关键信息  
✅ **中文输出**: 除官方名称和 URL 外，所有字段都输出中文  
✅ **完整字段**: 包含培训简介、发布日期、对应领域、目标受众等完整信息  
✅ **JSON 格式**: 结果保存为标准 JSON 格式，便于后续处理  

## 输出字段说明

每个培训的分析结果包含以下字段：

- **cisa_official_name**: CISA 官方名称（英文）
- **url**: 培训页面 URL
- **training_description**: 培训简介（中文）
- **publication_date**: 发布日期（YYYY-MM-DD 格式，如无明确日期则为 null）
- **domain**: 对应领域（中文）
- **target_audience**: 目标受众（中文）
- **is_cybersecurity_related**: 是否网络安全相关（布尔值）
- **is_cisa_official**: 是否 CISA 官方提供（布尔值）

## 使用方法

### 方法一：使用 CLI 工具（推荐）

#### 分步骤执行模式
```bash
python cisa_cli.py step --type trainings
```

#### 完整分析模式
```bash
python cisa_cli.py full --type trainings
```

#### 从指定步骤开始
```bash
python cisa_cli.py step --type trainings --from-step scrape_content
```

### 方法二：使用主程序
```bash
python src/cisa_trainings_main.py
```

### 方法三：使用统一主程序
```bash
python src/unified_main.py  # 需要修改代码指定 trainings 类型
```

## 演示和测试

### 运行功能演示
```bash
python test/demo_trainings.py
```

### 运行功能测试
```bash
python test/test_trainings.py
```

## 输出文件

分析结果将保存到：
- **默认路径**: `data/cisa_trainings.json`
- **可配置**: 通过环境变量 `TRAININGS_OUTPUT_FILE` 自定义

## 示例输出

```json
{
  "metadata": {
    "total_trainings": 3,
    "analysis_date": "2025-01-23",
    "analysis_type": "trainings"
  },
  "trainings": [
    {
      "cisa_official_name": "Analyzing Cyber Risk with the CDM Agency Dashboard (IN-PERSON ONLY)",
      "url": "https://www.cisa.gov/resources-tools/training/analyzing-cyber-risk-cdm-agency-dashboard-person-only",
      "training_description": "这门为期两天的线下课程探索当前CDM机构仪表板版本的功能...",
      "publication_date": null,
      "domain": "风险管理",
      "target_audience": "IT专业人员",
      "is_cybersecurity_related": true,
      "is_cisa_official": true
    }
  ]
}
```

## 配置要求

### 环境变量
确保设置了以下环境变量：
- `LLM_PROVIDER`: LLM 提供商（如 openai、anthropic 等）
- `OPENAI_API_KEY` 或相应的 API 密钥
- `HTTP_PROXY` 和 `HTTPS_PROXY`: 代理配置（如需要）

### 网络配置
由于网络限制，需要使用代理：
```bash
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ python cisa_cli.py step --type trainings
```

## 性能说明

- **页面范围**: 抓取 page=0 到 page=9（共 10 页）
- **并发控制**: 支持可配置的并发数量
- **速率限制**: 内置延迟机制，避免被网站封禁
- **错误处理**: 完善的重试机制和错误恢复

## 注意事项

1. 首次运行可能需要较长时间（取决于培训数量和网络状况）
2. 确保有足够的 LLM API 配额
3. 建议在稳定网络环境下运行
4. 遵守 CISA.gov 使用条款

## 故障排除

### 常见问题

**Q: 抓取失败怎么办？**  
A: 检查网络连接和代理配置，确保可以访问 CISA 官网

**Q: LLM 分析失败怎么办？**  
A: 检查 API 密钥配置和网络连接，确认 LLM 服务可用

**Q: 输出格式不正确怎么办？**  
A: 运行测试脚本验证功能，检查配置文件

### 调试模式
设置环境变量启用详细日志：
```bash
export LOG_LEVEL=DEBUG
```

## 更多信息

- 项目主文档: [README.md](../README.md)
- 新功能说明: [NEW_FEATURES.md](NEW_FEATURES.md)
- 开发日志: [dev_logs/](dev_logs/)
