# 数据转换指南

**日期**: 2025-07-24  
**类型**: 用户指南  
**状态**: 已完成

## 概述

本指南说明如何使用数据转换脚本将现有的 `cisa_*.json` 文件转换为符合新字段标准的格式，使其能够被最新的CLI命令正常处理。

## 背景

在阶段二字段标准化实施后，所有JSON数据文件需要使用统一的英文字段名，而Markdown输出保持中文表头。转换脚本自动处理这一转换过程。

## 转换前后对比

### 转换前（旧格式）
```json
{
  "groups": [
    {
      "cisa_official_name": "Test Group",
      "组织简介": "组织描述",
      "对应领域": "政策制定",
      "目标受众": "政府机构",
      "是否网络安全相关": false,
      "是否CISA官方提供": true
    }
  ]
}
```

### 转换后（新格式）
```json
{
  "metadata": {
    "converted_at": "2025-07-24T13:32:16.731566",
    "field_format": "standardized_english",
    "analyzer_version": "2.0.0"
  },
  "groups": [
    {
      "cisa_official_name": "Test Group",
      "description": "组织描述",
      "domain": "政策制定",
      "target_audience": "政府机构",
      "is_cybersecurity_related": false,
      "is_cisa_official": true
    }
  ]
}
```

## 使用方法

### 1. 自动转换所有文件

```bash
# 转换 data/ 目录下所有 cisa_*.json 文件
python3 scripts/convert_data_files.py
```

### 2. 验证转换结果

```bash
# 验证所有转换后的文件
python3 scripts/verify_converted_data.py
```

### 3. 测试CLI命令

```bash
# 测试导出功能
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv run python cisa_cli.py export data/cisa_trainings.json
```

## 转换脚本功能

### `scripts/convert_data_files.py`

**功能**：
- 自动发现 `data/` 目录下的所有 `cisa_*.json` 文件
- 将旧字段名转换为标准英文字段名
- 创建备份文件
- 更新metadata信息

**特点**：
- ✅ 自动备份原始文件
- ✅ 支持所有数据类型（services、groups、programs、resources、trainings）
- ✅ 保持数据内容不变，仅转换字段名
- ✅ 更新metadata标记转换状态

### `scripts/verify_converted_data.py`

**功能**：
- 验证JSON文件结构正确性
- 测试CLI导出命令
- 检查Markdown输出质量

**检查项目**：
- ✅ JSON结构有效性
- ✅ 标准字段完整性
- ✅ CLI命令执行成功
- ✅ Markdown文件生成
- ✅ 中文表头显示

## 字段映射表

| 旧字段名（中文） | 新字段名（英文） | 说明 |
|---|---|---|
| 组织简介 | description | 组织描述 |
| 项目简介 | description | 项目描述 |
| 资源简介 | description | 资源描述 |
| service_description | description | 服务描述 |
| training_description | description | 培训描述 |
| 对应领域 | domain | 领域分类 |
| 目标受众 | target_audience | 目标用户群体 |
| 是否网络安全相关 | is_cybersecurity_related | 网络安全相关性 |
| 是否CISA官方提供 | is_cisa_official | CISA官方标识 |
| 发布日期 | publication_date | 发布时间 |

## 备份机制

转换脚本会自动创建备份：

```
data/backups/conversion_YYYYMMDD_HHMMSS/
├── cisa_services.json
├── cisa_groups.json
├── cisa_programs.json
├── cisa_resources.json
└── cisa_trainings.json
```

## 转换结果验证

### 成功标志

1. **JSON结构**：
   - ✅ 包含 `field_format: "standardized_english"` 标记
   - ✅ 所有字段名为英文
   - ✅ 数据内容保持不变

2. **CLI兼容性**：
   - ✅ `uv run python cisa_cli.py export` 命令正常执行
   - ✅ 生成Markdown表格文件
   - ✅ 表头显示为中文

3. **Markdown输出**：
   - ✅ 包含表格结构
   - ✅ 中文表头正确显示
   - ✅ 数据完整性保持

### 常见问题

**Q: 转换后CLI命令报错怎么办？**
A: 检查环境变量设置，确保使用正确的代理配置：
```bash
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/
```

**Q: 如何恢复原始数据？**
A: 从备份目录恢复：
```bash
cp data/backups/conversion_YYYYMMDD_HHMMSS/* data/
```

**Q: 转换脚本可以重复运行吗？**
A: 可以，脚本会检测已转换的文件并创建新的备份。

## 技术细节

### 转换逻辑

1. **字段标准化**：使用 `FieldStandardizer` 类进行字段名转换
2. **向后兼容**：保持与现有代码的兼容性
3. **数据完整性**：确保转换过程中数据不丢失
4. **元数据更新**：添加转换标记和版本信息

### 支持的数据类型

- `cisa_services.json` - CISA服务数据
- `cisa_groups.json` - CISA组织数据  
- `cisa_programs.json` - CISA项目数据
- `cisa_resources.json` - CISA资源数据
- `cisa_trainings.json` - CISA培训数据

## 总结

数据转换脚本提供了一个安全、可靠的方式来升级现有JSON数据文件，使其符合新的字段标准。通过自动化的转换和验证流程，确保数据迁移的成功和系统的正常运行。

转换完成后，所有 `cisa_*.json` 文件将能够被最新的CLI命令正常处理，同时保持Markdown输出的中文表头显示。
