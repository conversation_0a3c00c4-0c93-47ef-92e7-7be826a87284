# CISA 分析器使用说明

## 概述

CISA 分析器是一个全自动化的工具，用于抓取和分析 CISA.gov 网站的各类信息，包括 Services、Programs、Groups、Resources 和 Trainings。

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv sync

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置 API 密钥等配置
```

### 2. 基础使用

#### 使用 CLI 工具（推荐）

```bash
# 分步骤执行（推荐首次使用）
python cisa_cli.py step --type services

# 完整分析
python cisa_cli.py full --type services

# 查看状态
python cisa_cli.py status

# 导出结果为 Markdown 表格
python cisa_cli.py export data/cisa_services.json
```

#### 支持的分析类型

- `services` - 分析 CISA 服务
- `programs` - 分析 CISA 项目
- `groups` - 分析 CISA 组织
- `resources` - 分析 CISA 资源
- `trainings` - 分析 CISA 培训
- `both` - 同时分析 services 和 programs

### 3. 高级用法

#### 从指定步骤开始

```bash
# 从内容抓取步骤开始
python cisa_cli.py step --type resources --from-step scrape_content

# 从 LLM 分析步骤开始
python cisa_cli.py step --type programs --from-step analyze_llm
```

#### 使用 analyze 命令

```bash
# 灵活的分析命令
python cisa_cli.py analyze services --mode step
python cisa_cli.py analyze programs --mode full
python cisa_cli.py analyze both --mode step --from-step scrape_content
```

## 分析步骤说明

每个分析类型都包含以下步骤：

1. **抓取列表** - 从 CISA 网站抓取项目列表
2. **抓取内容** - 获取每个项目的详细内容
3. **LLM 分析** - 使用大语言模型分析内容
4. **图形分析** - 进行图形化和链式分析（可选）
5. **保存结果** - 保存分析结果到 JSON 文件
6. **导出表格** - 导出为 Markdown 表格

## 输出文件

分析完成后，结果将保存在以下文件中：

- **Services**: `data/cisa_services.json`
- **Programs**: `data/cisa_programs.json`
- **Groups**: `data/cisa_groups.json`
- **Resources**: `data/cisa_resources.json`
- **Trainings**: `data/cisa_trainings.json`

## 配置说明

### 并发控制

通过 `.env` 文件配置并发参数：

```bash
# 内容抓取并发数（推荐 3-5）
MAX_CONCURRENT_REQUESTS=5

# LLM 分析并发数（推荐 2-3）
MAX_CONCURRENT_LLM_REQUESTS=3

# 请求间延迟时间（秒）
REQUEST_DELAY=2.0
```

### 页面限制

```bash
# 基础页面限制
MAX_PAGES=100

# 各类型专用限制
MAX_SERVICES_PAGES=30
MAX_PROGRAMS_PAGES=5
MAX_GROUPS_PAGES=2
MAX_RESOURCES_PAGES=148
MAX_TRAINING_PAGES=10
```

### LLM 配置

```bash
# OpenAI 配置
OPENAI_API_KEY=your_api_key
OPENAI_MODEL=gpt-4

# 或使用其他提供商
LLM_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_api_key
```

## 故障排除

### 常见问题

1. **网络连接问题**
   - 确保代理配置正确
   - 检查网络连接稳定性

2. **API 密钥问题**
   - 验证 API 密钥是否有效
   - 检查 API 配额是否充足

3. **并发过高导致限制**
   - 降低并发数量
   - 增加请求延迟时间

### 调试方法

```bash
# 查看详细日志
python cisa_cli.py step --type services --verbose

# 运行测试验证
python test/test_analyzer.py

# 查看状态文件
cat data/analysis_state_services.json
```

## 性能优化建议

### 保守配置（稳定优先）
```bash
MAX_CONCURRENT_REQUESTS=3
MAX_CONCURRENT_LLM_REQUESTS=2
REQUEST_DELAY=3.0
```

### 平衡配置（推荐）
```bash
MAX_CONCURRENT_REQUESTS=5
MAX_CONCURRENT_LLM_REQUESTS=3
REQUEST_DELAY=2.0
```

### 激进配置（速度优先，有风险）
```bash
MAX_CONCURRENT_REQUESTS=10
MAX_CONCURRENT_LLM_REQUESTS=5
REQUEST_DELAY=1.0
```

## 注意事项

1. **遵守使用条款** - 请遵守 CISA.gov 的使用条款
2. **合理使用** - 避免过于频繁的请求
3. **网络稳定** - 建议在稳定的网络环境下运行
4. **资源消耗** - 完整分析可能需要较长时间和较多资源
5. **数据备份** - 重要数据会自动备份到 `data/backups/` 目录

## 更多信息

- [新功能说明](NEW_FEATURES.md) - 了解最新功能
- [Resources 分析](RESOURCES_USAGE.md) - Resources 功能详细说明
- [Trainings 分析](TRAININGS_USAGE.md) - Trainings 功能详细说明
- [并发控制](concurrency_control.md) - 并发配置详细说明
- [开发文档](dev_logs/) - 技术实现细节
