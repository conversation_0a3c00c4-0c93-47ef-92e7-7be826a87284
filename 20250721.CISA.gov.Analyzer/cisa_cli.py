#!/usr/bin/env python3
"""
CISA Resources & Tools Analyzer CLI Tool
Comprehensive command-line interface for CISA resources, services, programs, and tools analysis
"""
import os
import sys
import asyncio
import logging
from pathlib import Path
from typing import Optional, List

import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.text import Text

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / 'src'
sys.path.insert(0, str(src_dir))

from src.config import Config
from src.step_manager import StepManager, AnalysisStep
from src.markdown_exporter import MarkdownExporter
from src.csv_exporter import CSVExporter
from src.data_manager import DataManager

# Initialize Rich console
console = Console()
app = typer.Typer(
    name="cisa-cli",
    help="CISA Resources & Tools Analyzer - 全面的CISA资源、服务、项目和工具分析命令行工具",
    add_completion=False
)

def setup_basic_logging():
    """Setup basic logging for CLI"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def show_banner():
    """Show application banner using Rich"""
    banner_text = Text("🔧 CISA Resources & Tools Analyzer", style="bold blue")
    features = """
• 支持多种分析类型：Services, Programs, Trainings, Resources, Groups, All Resources & Tools
• 分步骤执行分析流程，支持断点续传
• JSON 数据转换为 Markdown/CSV 表格
• 状态管理和恢复功能
• 🆕 全面的资源和工具爬取功能
"""
    panel = Panel(
        features.strip(),
        title=banner_text,
        border_style="blue",
        padding=(1, 2)
    )
    console.print(panel)

async def run_step_mode(analysis_type: str = "services", from_step: Optional[str] = None, force_update: bool = False):
    """Run step-by-step analysis mode"""
    from src.step_main import main as step_main
    return await step_main(analysis_type=analysis_type, from_step=from_step, force_update=force_update)

async def run_full_analysis(analysis_type: str = "services", force_update: bool = False):
    """Run complete analysis from start to finish"""
    # Add debug logging for analysis type validation
    console.print(f"🔍 Debug: 开始执行完整分析，类型: {analysis_type}", style="cyan")

    if analysis_type == "services":
        from src.main import main as full_main
        return await full_main(force_update=force_update)
    elif analysis_type == "programs":
        from src.unified_main import main_programs
        return await main_programs(force_update=force_update)
    elif analysis_type == "trainings":
        from src.unified_main import main_trainings
        return await main_trainings(force_update=force_update)
    elif analysis_type == "groups":
        from src.cisa_groups_main import main as groups_main
        return await groups_main(force_update=force_update)
    elif analysis_type == "resources":
        from src.cisa_resources_main import main as resources_main
        console.print("🔄 开始分析 Resources...", style="blue")
        result = await resources_main(force_update=force_update)
        console.print(f"🔍 Resources 分析结果: {result}", style="cyan")
        return result
    elif analysis_type == "all_resources_tools":
        from src.unified_main import main_all_resources_tools
        console.print("🔄 开始分析 All Resources & Tools...", style="blue")
        result = await main_all_resources_tools(force_update=force_update)
        console.print(f"🔍 All Resources & Tools 分析结果: {result}", style="cyan")
        return result
    elif analysis_type == "both":
        # Run both services and programs analysis
        from src.main import main as services_main
        from src.cisa_programs_main import main as programs_main

        console.print("🔄 开始分析 Services...", style="blue")
        services_success = await services_main(force_update=force_update)

        if services_success:
            console.print("✅ Services 分析完成", style="green")
            console.print("🔄 开始分析 Programs...", style="blue")
            from src.unified_main import main_programs
            programs_success = await main_programs(force_update=force_update)

            if programs_success:
                console.print("✅ Programs 分析完成", style="green")
                return True
            else:
                console.print("❌ Programs 分析失败", style="red")
                return False
        else:
            console.print("❌ Services 分析失败，跳过 Programs 分析", style="red")
            return False
    else:
        console.print(f"❌ 不支持的分析类型: {analysis_type}", style="red")
        return False

@app.command()
def step(
    analysis_type: str = typer.Option(
        "services",
        "--type", "-t",
        help="分析类型: services, programs, trainings, groups, resources, all_resources_tools, 或 both"
    ),
    from_step: Optional[str] = typer.Option(
        None,
        "--from-step", "-f",
        help="从指定步骤开始执行: scrape_services, scrape_content, analyze_llm, graph_analysis, save_results, export_markdown"
    ),
    force_update: bool = typer.Option(
        False,
        "--force-update", "-F",
        help="强制更新所有项目，忽略已存在的正确内容"
    )
):
    """
    分步骤执行分析模式

    支持的分析类型：
    • services: CISA 服务分析
    • programs: CISA 项目分析
    • trainings: CISA 培训分析
    • resources: CISA 资源分析
    • groups: CISA 组织分析
    • all_resources_tools: 🆕 CISA 所有资源和工具分析（包含所有类型）
    • both: 同时分析服务和项目

    示例：
    python cisa_cli.py step --type all_resources_tools
    python cisa_cli.py step --type services --from-step scrape_content
    """
    show_banner()

    # Validate analysis type
    valid_types = ["services", "programs", "trainings", "groups", "resources", "all_resources_tools", "both"]
    if analysis_type not in valid_types:
        console.print(f"❌ 无效的分析类型: {analysis_type}", style="red")
        console.print(f"支持的类型: {', '.join(valid_types)}", style="yellow")
        raise typer.Exit(1)

    # Validate from_step if provided
    if from_step:
        valid_steps = ["scrape_services", "scrape_content", "analyze_llm", "graph_analysis", "save_results", "export_markdown"]
        if from_step not in valid_steps:
            console.print(f"❌ 无效的步骤: {from_step}", style="red")
            console.print(f"支持的步骤: {', '.join(valid_steps)}", style="yellow")
            raise typer.Exit(1)

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task(f"启动分步骤模式 ({analysis_type})...", total=None)

        try:
            if force_update:
                console.print("🔄 强制更新模式：将重新分析所有项目", style="yellow")
            success = asyncio.run(run_step_mode(analysis_type=analysis_type, from_step=from_step, force_update=force_update))
            if success:
                console.print("✅ 分步骤执行完成！", style="green")
            else:
                console.print("❌ 分步骤执行失败", style="red")
                raise typer.Exit(1)
        except KeyboardInterrupt:
            console.print("\n👋 用户中断程序", style="yellow")
            raise typer.Exit(1)

@app.command()
def full(
    analysis_type: str = typer.Option(
        "services",
        "--type", "-t",
        help="分析类型: services, programs, trainings, groups, resources, all_resources_tools, 或 both"
    ),
    force_update: bool = typer.Option(
        False,
        "--force-update", "-F",
        help="强制更新所有项目，忽略已存在的正确内容"
    )
):
    """
    完整分析模式（一键执行所有步骤）

    支持的分析类型：
    • services: CISA 服务分析
    • programs: CISA 项目分析
    • trainings: CISA 培训分析
    • resources: CISA 资源分析
    • groups: CISA 组织分析
    • all_resources_tools: 🆕 CISA 所有资源和工具分析（包含所有类型，约183页数据）
    • both: 同时分析服务和项目

    示例：
    python cisa_cli.py full --type all_resources_tools
    python cisa_cli.py full --type services --force-update
    """
    show_banner()

    # Validate analysis type
    valid_types = ["services", "programs", "trainings", "groups", "resources", "all_resources_tools", "both"]
    if analysis_type not in valid_types:
        console.print(f"❌ 无效的分析类型: {analysis_type}", style="red")
        console.print(f"支持的类型: {', '.join(valid_types)}", style="yellow")
        raise typer.Exit(1)

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task(f"启动完整分析模式 ({analysis_type})...", total=None)

        try:
            if force_update:
                console.print("🔄 强制更新模式：将重新分析所有项目", style="yellow")
            console.print(f"🚀 开始执行完整分析 (类型: {analysis_type})", style="blue")
            success = asyncio.run(run_full_analysis(analysis_type=analysis_type, force_update=force_update))
            console.print(f"🔍 分析函数返回值: {success} (类型: {type(success)})", style="cyan")
            if success:
                console.print("✅ 完整分析完成！", style="green")
            else:
                console.print("❌ 完整分析失败", style="red")
                raise typer.Exit(1)
        except KeyboardInterrupt:
            console.print("\n👋 用户中断程序", style="yellow")
            raise typer.Exit(1)

@app.command()
def analyze(
    target: str = typer.Argument(
        ...,
        help="分析目标: services, programs, trainings, groups, resources, all_resources_tools, 或 both"
    ),
    mode: str = typer.Option(
        "step",
        "--mode", "-m",
        help="执行模式: step (分步骤) 或 full (完整)"
    ),
    from_step: Optional[str] = typer.Option(
        None,
        "--from-step", "-f",
        help="从指定步骤开始执行 (仅在 step 模式下有效)"
    ),
    force_update: bool = typer.Option(
        False,
        "--force-update", "-F",
        help="强制更新所有项目，忽略已存在的正确内容"
    )
):
    """
    灵活的分析命令 - 支持指定目标和模式

    分析目标：
    • services: CISA 服务分析
    • programs: CISA 项目分析
    • trainings: CISA 培训分析
    • resources: CISA 资源分析
    • groups: CISA 组织分析
    • all_resources_tools: 🆕 CISA 所有资源和工具分析
    • both: 同时分析服务和项目

    执行模式：
    • step: 分步骤执行，支持断点续传
    • full: 一键完整执行

    示例：
    python cisa_cli.py analyze all_resources_tools --mode full
    python cisa_cli.py analyze services --mode step --from-step scrape_content
    """
    show_banner()

    # Validate target
    valid_targets = ["services", "programs", "trainings", "groups", "resources", "all_resources_tools", "both"]
    if target not in valid_targets:
        console.print(f"❌ 无效的分析目标: {target}", style="red")
        console.print(f"支持的目标: {', '.join(valid_targets)}", style="yellow")
        raise typer.Exit(1)

    # Validate mode
    valid_modes = ["step", "full"]
    if mode not in valid_modes:
        console.print(f"❌ 无效的执行模式: {mode}", style="red")
        console.print(f"支持的模式: {', '.join(valid_modes)}", style="yellow")
        raise typer.Exit(1)

    # Validate from_step if provided
    if from_step and mode != "step":
        console.print("⚠️  --from-step 参数仅在 step 模式下有效", style="yellow")
        from_step = None

    if from_step:
        valid_steps = ["scrape_services", "scrape_content", "analyze_llm", "graph_analysis", "save_results", "export_markdown"]
        if from_step not in valid_steps:
            console.print(f"❌ 无效的步骤: {from_step}", style="red")
            console.print(f"支持的步骤: {', '.join(valid_steps)}", style="yellow")
            raise typer.Exit(1)

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task(f"启动{mode}模式分析 ({target})...", total=None)

        try:
            if force_update:
                console.print("🔄 强制更新模式：将重新分析所有项目", style="yellow")
            if mode == "step":
                success = asyncio.run(run_step_mode(analysis_type=target, from_step=from_step, force_update=force_update))
            else:  # mode == "full"
                success = asyncio.run(run_full_analysis(analysis_type=target, force_update=force_update))

            if success:
                console.print("✅ 分析完成！", style="green")
            else:
                console.print("❌ 分析失败", style="red")
                raise typer.Exit(1)
        except KeyboardInterrupt:
            console.print("\n👋 用户中断程序", style="yellow")
            raise typer.Exit(1)


@app.command()
def export(
    input_file: str = typer.Argument(..., help="输入 JSON 文件路径"),
    output: Optional[str] = typer.Option(None, "-o", "--output", help="输出文件路径"),
    format: str = typer.Option("markdown", "--format", "-fmt", help="导出格式: markdown 或 csv"),
    fields: Optional[List[str]] = typer.Option(None, "-f", "--fields", help="要包含的字段列表"),
    max_length: int = typer.Option(300, "-l", "--max-length", help="描述字段最大长度"),
    include_metadata: bool = typer.Option(True, "--metadata/--no-metadata", help="是否包含元数据文件 (仅CSV)")
):
    """
    导出 JSON 数据为 Markdown 表格或 CSV 文件

    支持导出所有分析结果，包括：
    • CISA Services 分析结果
    • CISA Programs 分析结果
    • CISA Trainings 分析结果
    • CISA Resources 分析结果
    • CISA Groups 分析结果
    • 🆕 CISA All Resources & Tools 分析结果

    导出格式：
    • markdown: 生成易读的 Markdown 表格
    • csv: 生成 CSV 文件，便于数据分析

    示例：
    python cisa_cli.py export data/cisa_all_resourcess_and_tools_info.json
    python cisa_cli.py export data/cisa_services.json --format csv -o services.csv
    python cisa_cli.py export data/cisa_groups.json --fields cisa_official_name,url,description
    """
    setup_basic_logging()

    # Validate format
    valid_formats = ["markdown", "csv"]
    if format.lower() not in valid_formats:
        console.print(f"❌ 无效的导出格式: {format}", style="red")
        console.print(f"支持的格式: {', '.join(valid_formats)}", style="yellow")
        raise typer.Exit(1)

    if not os.path.exists(input_file):
        console.print(f"❌ 输入文件不存在: {input_file}", style="red")
        raise typer.Exit(1)

    format_lower = format.lower()
    format_name = "CSV 文件" if format_lower == "csv" else "Markdown 表格"
    console.print(f"📝 正在导出 {input_file} 到 {format_name}...")

    # Choose appropriate exporter
    if format_lower == "csv":
        exporter = CSVExporter()
    else:
        exporter = MarkdownExporter()

    # Load and validate data
    services = exporter.load_json_data(input_file)
    if not services:
        console.print("❌ 无法加载 JSON 数据", style="red")
        raise typer.Exit(1)

    console.print(f"✅ 加载了 {len(services)} 个记录", style="green")

    # Show available fields if none specified
    if fields is None:
        available_fields = exporter.get_available_fields(services)
        console.print("\n可用字段:", style="blue")
        exporter.print_field_mappings()
        console.print(f"\n数据中的所有字段: {', '.join(available_fields)}")

        # Auto-detect data type and use appropriate default fields
        data_type = exporter.auto_detect_data_type(services, input_file)
        default_fields = exporter.get_default_fields_for_type(data_type)
        console.print(f"检测到数据类型: {data_type}")
        console.print(f"使用默认字段: {', '.join(default_fields)}")
        fields = default_fields

    # Export based on format
    if format_lower == "csv":
        success = exporter.export_to_csv(
            input_file,
            output,
            fields,
            max_description_length=max_length,
            include_metadata=include_metadata
        )
        default_extension = ".csv"
    else:
        success = exporter.export_to_markdown(
            input_file,
            output,
            fields,
            max_description_length=max_length
        )
        default_extension = "_table.md"

    if success:
        if output:
            output_path = output
        else:
            base_name = os.path.splitext(input_file)[0]
            output_path = f"{base_name}{default_extension}"

        console.print(f"✅ 成功导出到: {output_path}", style="green")

        # Show additional info for CSV exports
        if format_lower == "csv" and include_metadata:
            metadata_path = output_path.replace('.csv', '_metadata.txt')
            console.print(f"📄 元数据文件: {metadata_path}", style="blue")
    else:
        console.print("❌ 导出失败", style="red")
        raise typer.Exit(1)

@app.command()
def status():
    """显示分析状态"""
    setup_basic_logging()
    step_manager = StepManager()

    # Create a rich table for status display
    table = Table(title="分析状态", show_header=True, header_style="bold blue")
    table.add_column("步骤", style="cyan", no_wrap=True)
    table.add_column("状态", justify="center")
    table.add_column("描述", style="magenta")

    completed_steps = step_manager.get_completed_steps()
    state = step_manager.load_state()

    if state:
        console.print(f"当前步骤: {step_manager.step_descriptions.get(AnalysisStep(state['current_step']), state['current_step'])}")
        console.print(f"最后更新: {state['timestamp']}")
    else:
        console.print("当前步骤: 未开始")

    # Add rows to table
    for i, step in enumerate(AnalysisStep):
        status_icon = "✅" if step in completed_steps else "⏸️"
        step_num = f"{i+1}"
        description = step_manager.step_descriptions[step]
        table.add_row(step_num, status_icon, description)

    console.print(table)

@app.command()
def clear():
    """清除分析状态"""
    setup_basic_logging()
    step_manager = StepManager()

    confirm = typer.confirm("确认清除所有分析状态?")
    if confirm:
        if step_manager.clear_state():
            console.print("✅ 分析状态已清除", style="green")
        else:
            console.print("❌ 清除状态失败", style="red")
            raise typer.Exit(1)
    else:
        console.print("取消操作", style="yellow")

@app.command("list")
def list_files():
    """列出备份文件"""
    setup_basic_logging()

    backup_dir = Path(Config.SERVICES_OUTPUT_FILE).parent / 'backups'
    if not backup_dir.exists():
        console.print("📁 备份目录不存在", style="yellow")
        return

    backup_files = list(backup_dir.glob('*.json'))
    if not backup_files:
        console.print("📁 没有找到备份文件", style="yellow")
        return

    # Create a rich table for backup files
    table = Table(title=f"备份文件 ({len(backup_files)} 个)", show_header=True, header_style="bold blue")
    table.add_column("步骤类型", style="cyan", no_wrap=True)
    table.add_column("文件名", style="green")
    table.add_column("大小", justify="right", style="magenta")
    table.add_column("修改时间", style="blue")

    # Group by step type
    step_files = {}
    for file in backup_files:
        step_name = file.name.split('_')[0]
        if step_name not in step_files:
            step_files[step_name] = []
        step_files[step_name].append(file)

    for step_name, files in step_files.items():
        for file in sorted(files, key=lambda x: x.stat().st_mtime, reverse=True):
            size = file.stat().st_size
            mtime = file.stat().st_mtime
            import datetime
            time_str = datetime.datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
            table.add_row(
                step_name.upper(),
                file.name,
                f"{size:,} bytes",
                time_str
            )

    console.print(table)

@app.callback(invoke_without_command=True)
def main(ctx: typer.Context):
    """
    CISA Services Analyzer CLI Tool

    示例用法:

    基础命令:
    • cisa_cli.py step                                    # 分步骤执行模式 (默认 services)
    • cisa_cli.py full                                    # 完整分析模式 (默认 services)
    • cisa_cli.py status                                  # 显示分析状态
    • cisa_cli.py export data/cisa_services.json         # 导出为 Markdown 表格
    • cisa_cli.py export data/cisa_services.json --format csv # 导出为 CSV 文件
    • cisa_cli.py export data/cisa_all_resourcess_and_tools_info.json # 🆕 导出所有资源和工具

    指定分析类型:
    • cisa_cli.py step --type programs                    # 分步骤分析 programs
    • cisa_cli.py step --type all_resources_tools         # 🆕 分步骤分析所有资源和工具
    • cisa_cli.py full --type both                        # 完整分析 services 和 programs
    • cisa_cli.py full --type all_resources_tools         # 🆕 完整分析所有资源和工具
    • cisa_cli.py analyze services --mode step            # 使用新的 analyze 命令

    从指定步骤开始:
    • cisa_cli.py step --from-step scrape_content         # 从内容抓取步骤开始
    • cisa_cli.py analyze programs --from-step analyze_llm # 从 LLM 分析步骤开始

    强制更新模式:
    • cisa_cli.py step --force-update                     # 强制重新分析所有项目
    • cisa_cli.py full --type programs --force-update     # 强制更新所有 programs
    • cisa_cli.py analyze both --mode full --force-update # 强制更新所有数据

    管理命令:
    • cisa_cli.py clear                                   # 清除分析状态
    • cisa_cli.py list                                    # 列出备份文件
    """
    if ctx.invoked_subcommand is None:
        show_banner()
        console.print("\n使用 --help 查看可用命令", style="dim")

if __name__ == "__main__":
    try:
        app()
    except KeyboardInterrupt:
        console.print("\n👋 用户中断程序", style="yellow")
        sys.exit(1)
    except Exception as e:
        console.print(f"❌ 错误: {e}", style="red")
        sys.exit(1)
