# Tracemalloc 错误修复指南

## 问题描述

在运行应用程序时出现以下错误：

```
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
```

## 错误原因分析

### 根本原因

1. **LiteLLM 异步客户端清理问题**：LiteLLM 库在清理异步 HTTP 客户端时存在协程未正确等待的问题
2. **Tracemalloc 未启用**：Python 的内存分配跟踪模块 `tracemalloc` 未启用，导致无法提供详细的对象分配回溯信息
3. **异步资源管理不当**：异步任务和资源在程序退出时未被正确清理

### 具体错误位置

```
/path/to/.venv/lib/python3.12/site-packages/litellm/llms/custom_httpx/async_client_cleanup.py:78
RuntimeWarning: coroutine 'close_litellm_async_clients' was never awaited
```

## 修复方案

### 方案一：启用 Tracemalloc（主要修复）

#### 1. 在主入口文件启用

**文件**: `main.py`

```python
import tracemalloc

# 启用 tracemalloc 以获取内存分配回溯信息
tracemalloc.start()
```

#### 2. 在 CLI 模块中添加检查

**文件**: `src/backend/cli/__init__.py`

```python
import tracemalloc

def main():
    # 启用 tracemalloc 以获取内存分配回溯信息（如果尚未启用）
    if not tracemalloc.is_tracing():
        tracemalloc.start()
        logger.info("Tracemalloc enabled for memory debugging")
```

### 方案二：优化异步资源清理

**文件**: `src/backend/cli/async_manager.py`

改进了 LiteLLM 异步客户端的清理逻辑：

```python
# 使用 asyncio.wait_for 设置超时，避免无限等待
try:
    loop.run_until_complete(asyncio.wait_for(close_task, timeout=1.0))
except asyncio.TimeoutError:
    close_task.cancel()
```

### 方案三：环境变量配置（可选）

创建启动脚本 `run_with_tracemalloc.sh`：

```bash
#!/bin/bash
export PYTHONTRACEMALLOC=1
python main.py "$@"
```

## 修复效果验证

### 修复前

```
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
```

### 修复后

```
Object allocated at (most recent call last):
  File "/.../litellm/llms/custom_httpx/async_client_cleanup.py", lineno 72
    loop.run_until_complete(close_litellm_async_clients())
```

现在可以看到具体的对象分配位置，便于调试和问题定位。

## 使用建议

### 1. 开发环境

- 始终启用 tracemalloc 以便调试内存相关问题
- 使用 `tracemalloc.get_traced_memory()` 监控内存使用

### 2. 生产环境

- 可选择性启用 tracemalloc（会有轻微性能开销）
- 使用环境变量 `PYTHONTRACEMALLOC=1` 控制

### 3. 调试技巧

```python
import tracemalloc

# 启用并设置帧数
tracemalloc.start(10)  # 保存 10 个调用帧

# 获取内存使用情况
current, peak = tracemalloc.get_traced_memory()
print(f"Current memory usage: {current / 1024 / 1024:.1f} MB")
print(f"Peak memory usage: {peak / 1024 / 1024:.1f} MB")

# 获取快照
snapshot = tracemalloc.take_snapshot()
top_stats = snapshot.statistics('lineno')

# 显示前 10 个内存使用最多的位置
for stat in top_stats[:10]:
    print(stat)
```

## 相关文档

- [Python tracemalloc 官方文档](https://docs.python.org/3/library/tracemalloc.html)
- [异步编程最佳实践](https://docs.python.org/3/library/asyncio.html)
- [LiteLLM 文档](https://docs.litellm.ai/)

## 配置化 Tracemalloc 实现

### 配置文件支持

在 `config.yaml` 中添加了 debug 配置节：

```yaml
debug:
  # 内存分配跟踪配置
  tracemalloc:
    enabled: false  # 缺省禁用，可通过环境变量 TRACEMALLOC_ENABLED 覆盖
    nframe: 25      # 保存的调用帧数，更多帧提供更详细的回溯信息
    # 注意：启用 tracemalloc 会有轻微的性能开销，建议仅在调试时使用
    # 环境变量优先级：TRACEMALLOC_ENABLED > 配置文件 > 默认值(false)

  # 异步调试配置
  asyncio:
    debug: false    # 启用 asyncio 调试模式，可通过环境变量 ASYNCIO_DEBUG 覆盖
    log_slow_callbacks: true  # 记录慢回调
    slow_callback_duration: 0.1  # 慢回调阈值（秒）
```

### 环境变量控制

支持以下环境变量：

- `TRACEMALLOC_ENABLED`: 控制是否启用 tracemalloc（true/false）
- `TRACEMALLOC_NFRAME`: 控制保存的调用帧数（1-100）
- `PYTHONTRACEMALLOC`: Python 标准环境变量，支持数字或布尔值

### 优先级规则

1. **环境变量** > 配置文件 > 默认值
2. 环境变量会覆盖配置文件设置
3. 如果二者不一致，环境变量优先

### 使用示例

```bash
# 通过环境变量启用
TRACEMALLOC_ENABLED=true python main.py analyze "测试内容"

# 通过 Python 标准环境变量启用
PYTHONTRACEMALLOC=25 python main.py analyze "测试内容"

# 查看 tracemalloc 状态
python main.py status
```

### 状态查询

使用 `python main.py status` 命令可以查看 tracemalloc 的当前状态：

- **✓ 启用**: 显示当前和峰值内存使用情况
- **○ 禁用**: 显示控制来源（环境变量/配置文件/默认）

## 总结

通过配置化的 `tracemalloc` 管理，我们实现了：

1. ✅ **解决了 "Enable tracemalloc" 警告**
2. ✅ **提供了详细的对象分配回溯信息**
3. ✅ **改善了异步资源清理机制**
4. ✅ **增强了应用程序的可调试性**
5. ✅ **灵活的配置控制机制**
6. ✅ **环境变量优先级支持**
7. ✅ **对性能影响最小**
8. ✅ **生产环境友好（默认禁用）**

这个解决方案既满足了开发调试的需要，又保证了生产环境的性能，是一个完整的企业级解决方案。
