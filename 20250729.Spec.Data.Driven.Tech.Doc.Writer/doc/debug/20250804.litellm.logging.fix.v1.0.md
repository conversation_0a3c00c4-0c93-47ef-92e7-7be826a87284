# LiteLLM 日志配置修复文档

## 问题描述

在应用程序运行时，出现了以下 LiteLLM 相关的警告信息：

```
13:51:38 - LiteLLM:WARNING: utils.py:528 - `litellm.set_verbose` is deprecated. Please set `os.environ['LITELLM_LOG'] = 'DEBUG'` for debug logs.
ASYNC kwargs[caching]: False; litellm.cache: None; kwargs.get('cache'): None
Async Wrapper: Completed Call, calling async_success_handler: <bound method Logging.async_success_handler of <litellm.litellm_core_utils.litellm_logging.Logging object at 0x1211a9b20>>
```

## 根本原因

1. **已弃用的 API 使用**：代码中使用了已弃用的 `litellm.set_verbose = True` 设置
2. **缺失的日志配置模块**：系统尝试导入 `system.src.backend.logging_config` 模块但该模块不存在
3. **日志级别控制不当**：LiteLLM 的日志级别没有与应用程序的日志级别正确同步

## 修复方案

### 1. 创建日志配置模块

创建了 `src/backend/logging_config.py` 模块，提供：

- **统一的日志配置**：支持 structlog 和标准库日志的统一配置
- **LiteLLM 日志集成**：自动设置 `LITELLM_LOG` 环境变量
- **灵活的配置选项**：支持控制台和文件输出、日志级别、格式等
- **组件级日志控制**：支持为不同组件设置不同的日志级别

### 2. 更新 LiteLLM 提供者

修改了 `src/backend/llm/providers.py`：

**修复前：**
```python
# 启用详细日志（调试用）
litellm.set_verbose = True
```

**修复后：**
```python
# 设置LiteLLM日志级别（使用新的环境变量方式）
# 优先使用已设置的 LITELLM_LOG，否则根据当前日志级别设置
if 'LITELLM_LOG' not in os.environ:
    current_log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    if current_log_level == 'DEBUG':
        os.environ['LITELLM_LOG'] = 'DEBUG'
    elif current_log_level in ['INFO', 'WARNING']:
        os.environ['LITELLM_LOG'] = 'INFO'
    else:
        os.environ['LITELLM_LOG'] = 'ERROR'

# 禁用已弃用的 set_verbose 设置
# 注意：不再使用 litellm.set_verbose，改用环境变量控制
```

### 3. 更新配置文件

在 `config.yaml` 中添加了 LiteLLM 相关的日志级别控制：

```yaml
logging:
  loggers:
    "litellm": "INFO"  # 控制 LiteLLM 日志级别
    "openai": "WARNING"  # 控制 OpenAI 客户端日志
    "anthropic": "WARNING"  # 控制 Anthropic 客户端日志
```

## 修复效果

### 修复前的问题
- ❌ 出现 `litellm.set_verbose is deprecated` 警告
- ❌ 缺少日志配置模块导致导入错误
- ❌ LiteLLM 日志级别无法控制

### 修复后的改进
- ✅ 消除了已弃用 API 的警告信息
- ✅ 提供了完整的日志配置模块
- ✅ LiteLLM 日志级别与应用程序日志级别同步
- ✅ 支持通过配置文件控制各组件的日志级别
- ✅ 保持了原有的业务逻辑和功能不变

## 日志级别映射

应用程序日志级别与 LiteLLM 日志级别的映射关系：

| 应用程序日志级别 | LiteLLM 日志级别 | 说明 |
|-----------------|-----------------|------|
| DEBUG           | DEBUG           | 显示详细的调试信息 |
| INFO            | INFO            | 显示基本的信息日志 |
| WARNING         | INFO            | 显示警告和信息日志 |
| ERROR           | ERROR           | 只显示错误日志 |

## 使用方法

### 通过环境变量配置
```bash
export LOG_LEVEL=DEBUG
export LITELLM_LOG=DEBUG
```

### 通过配置文件配置
```yaml
logging:
  level: "INFO"
  loggers:
    "litellm": "INFO"
```

### 程序化配置
```python
from src.backend.logging_config import setup_logging

setup_logging(
    level="INFO",
    format_type="structured",
    console_enabled=True,
    file_enabled=True
)
```

## 验证方法

运行应用程序验证修复效果：

```bash
# 激活虚拟环境
source .venv/bin/activate

# 运行应用程序
python main.py --help

# 检查状态
python main.py status
```

如果修复成功，应该不会再看到 LiteLLM 的弃用警告信息。

## 注意事项

1. **向后兼容性**：修复保持了与现有代码的完全兼容性
2. **配置优先级**：环境变量 `LITELLM_LOG` 如果已设置，将优先使用
3. **自动初始化**：日志配置模块会在导入时自动初始化
4. **错误处理**：如果日志配置失败，会回退到标准的 structlog 配置

## 相关文件

- `src/backend/logging_config.py` - 新增的日志配置模块
- `src/backend/llm/providers.py` - 更新的 LiteLLM 提供者
- `config.yaml` - 更新的配置文件
- `docs/LITELLM_LOGGING_FIX.md` - 本修复文档
