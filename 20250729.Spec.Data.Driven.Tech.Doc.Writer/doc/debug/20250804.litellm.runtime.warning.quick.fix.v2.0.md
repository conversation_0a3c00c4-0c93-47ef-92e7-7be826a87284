# LiteLLM RuntimeWarning 快速修复指南

## 问题症状
```
RuntimeWarning: coroutine 'close_litellm_async_clients' was never awaited
  loop.close()
```

## 快速修复方案

### 1. 在应用程序入口设置环境变量
```python
# 在导入任何模块之前
import os
os.environ['LITELLM_DISABLE_ASYNC_CLEANUP'] = '1'
```

### 2. 实现 atexit 拦截机制
```python
import atexit

# 保存原始 register 函数
original_register = atexit.register

def filtered_register(func, *args, **kwargs):
    """过滤 LiteLLM 清理函数注册"""
    func_name = getattr(func, '__name__', str(func))
    module_name = getattr(func, '__module__', 'unknown')
    
    # 阻止 LiteLLM 清理函数注册
    if ('cleanup' in func_name.lower() and 
        ('litellm' in module_name.lower() or 'async_client' in module_name.lower())):
        return  # 不注册
    
    return original_register(func, *args, **kwargs)

# 替换 atexit.register
atexit.register = filtered_register
```

### 3. 禁用 LiteLLM 清理函数
```python
import litellm

# 替换为无操作函数
def disabled_cleanup():
    pass

litellm.register_async_client_cleanup = disabled_cleanup
```

## 验证修复
运行应用程序，确认：
- ✅ 无 RuntimeWarning 输出
- ✅ 程序正常退出
- ✅ LiteLLM 功能正常

## 完整实现参考
详细实现请参考：
- `src/backend/cli/__init__.py` - CLI 初始化拦截
- `src/backend/llm/providers.py` - LLM 提供者配置
- `doc/debug/20250804.litellm.runtime.warning.fix.v2.0.md` - 完整文档

---
**状态**: ✅ 已验证有效  
**适用版本**: LiteLLM 1.40.0+  
**Python 版本**: 3.11+ / 3.12+
