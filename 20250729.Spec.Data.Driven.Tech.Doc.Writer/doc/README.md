# 项目文档目录

本目录包含多智能体技术文档写作系统的所有文档，按照功能和用途进行分类组织。

## 目录结构

### 用户文档
- **README.md** - 本文档，项目文档概述
- **usage/** - 使用说明和配置指南
  - 快速开始指南
  - 配置说明文档
  - 使用示例和最佳实践

### 需求文档
- **requirements/** - 原始用户需求文档
  - 用户直接提出的原始需求清单
  - 需求分析和处理状态跟踪

### API文档
- **api/** - API规格说明和接口文档
  - API接口规范
  - 接口使用示例
  - API变更记录

### 设计文档
- **design/** - 最终设计文档、架构图、系统规格说明
  - 系统架构设计
  - 多智能体工作流设计
  - 技术选型和设计决策

### 开发文档
- **dev/** - 开发过程记录、会议纪要、技术决策
  - **architecture/** - 架构决策和设计
  - **deployment/** - 部署相关文档
  - **implementation/** - 实现过程记录
  - **knowledge-base/** - 知识库和最佳实践
  - **meetings/** - 会议记录
  - **processes/** - 开发流程和规范
  - **requirements/** - 开发需求和待办事项
  - **sprints/** - Sprint 规划和回顾
  - **testing/** - 测试策略和计划

### 调试文档
- **debug/** - 调试、排错过程记录
  - 问题诊断和解决方案
  - 修复过程记录
  - 故障排除指南

## 文档命名规范

### 二级目录文档格式
`YYYYMMDD.文档名.va.b.md`

- **YYYYMMDD**：文档创建或最后重大更新日期
- **文档名**：使用英文点号分隔的描述性名称
- **va.b**：版本号（a为主版本，b为次版本）

### 版本更新规则
- 重大变更：更新主版本号（a）
- 小修改：更新次版本号（b）
- 日期反映最后修改时间

## 文档维护原则

1. **全面审视**：更新、编写文档前要全面审视现有文档
2. **表达准确**：选择准确完整表达、LLM 易理解、省 token 的方式
3. **优先更新**：优先更新现有文档，而不是创建新文档
4. **优先引用**：优先引用现有文档的内容，而不是重复编写
5. **分类明确**：确保文档放置在正确的目录中
6. **命名规范**：严格遵循文档命名规范
