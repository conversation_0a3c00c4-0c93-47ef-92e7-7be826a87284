# API 文档

## 概述

本文档描述了数据驱动技术文档写作多智能体系统的API接口规范。系统提供RESTful API和Python SDK两种访问方式。

## 版本信息

- **API版本**: v1.0
- **文档版本**: v1.0
- **最后更新**: 2025-07-30

## 基础信息

### 基础URL
```
http://localhost:8000/api/v1
```

### 认证方式
```http
Authorization: Bearer <your-api-key>
```

### 响应格式
所有API响应均采用JSON格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2025-07-30T10:00:00Z",
  "request_id": "req_123456789"
}
```

## 核心API接口

### 1. 工作流管理

#### 1.1 创建文档生成任务

**接口**: `POST /workflows`

**描述**: 创建新的文档生成工作流任务

**请求体**:
```json
{
  "topic": "人工智能在医疗诊断中的应用",
  "scope": "技术现状、挑战和发展趋势",
  "format": "技术调研报告",
  "language": "zh-CN",
  "deadline": "2025-08-15T00:00:00Z",
  "requirements": {
    "word_count": 5000,
    "sections": ["摘要", "技术现状", "挑战分析", "发展趋势", "结论"],
    "citation_style": "APA",
    "include_charts": true
  },
  "sources": {
    "academic_papers": true,
    "industry_reports": true,
    "news_articles": false,
    "government_docs": true
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "workflow_id": "wf_abc123def456",
    "status": "created",
    "estimated_duration": "2-4小时",
    "created_at": "2025-07-30T10:00:00Z"
  },
  "message": "工作流创建成功"
}
```

#### 1.2 查询工作流状态

**接口**: `GET /workflows/{workflow_id}`

**描述**: 查询指定工作流的执行状态

**响应**:
```json
{
  "success": true,
  "data": {
    "workflow_id": "wf_abc123def456",
    "status": "in_progress",
    "current_stage": "document_writing",
    "progress": 65,
    "stages": [
      {
        "name": "requirement_analysis",
        "status": "completed",
        "duration": "5分钟",
        "completed_at": "2025-07-30T10:05:00Z"
      },
      {
        "name": "research",
        "status": "completed", 
        "duration": "45分钟",
        "completed_at": "2025-07-30T10:50:00Z"
      },
      {
        "name": "document_writing",
        "status": "in_progress",
        "progress": 40,
        "estimated_remaining": "30分钟"
      }
    ],
    "created_at": "2025-07-30T10:00:00Z",
    "updated_at": "2025-07-30T11:20:00Z"
  }
}
```

#### 1.3 获取工作流结果

**接口**: `GET /workflows/{workflow_id}/result`

**描述**: 获取已完成工作流的生成结果

**响应**:
```json
{
  "success": true,
  "data": {
    "workflow_id": "wf_abc123def456",
    "status": "completed",
    "document": {
      "title": "人工智能在医疗诊断中的应用技术调研报告",
      "content_url": "/api/v1/documents/doc_xyz789/content",
      "download_url": "/api/v1/documents/doc_xyz789/download",
      "metadata": {
        "word_count": 4856,
        "page_count": 12,
        "sections": 5,
        "references": 23,
        "charts": 3
      },
      "formats": ["markdown", "pdf", "docx"]
    },
    "research_data": {
      "sources_count": 45,
      "academic_papers": 18,
      "industry_reports": 12,
      "government_docs": 15
    },
    "quality_metrics": {
      "content_score": 8.5,
      "structure_score": 9.0,
      "citation_score": 8.8,
      "overall_score": 8.8
    },
    "completed_at": "2025-07-30T12:30:00Z"
  }
}
```

### 2. 文档管理

#### 2.1 获取文档内容

**接口**: `GET /documents/{document_id}/content`

**描述**: 获取文档的原始内容

**查询参数**:
- `format`: 内容格式 (markdown, html, plain)

**响应**:
```json
{
  "success": true,
  "data": {
    "document_id": "doc_xyz789",
    "format": "markdown",
    "content": "# 人工智能在医疗诊断中的应用技术调研报告\n\n## 摘要\n...",
    "metadata": {
      "title": "人工智能在医疗诊断中的应用技术调研报告",
      "author": "AI文档生成系统",
      "created_at": "2025-07-30T12:30:00Z",
      "word_count": 4856
    }
  }
}
```

#### 2.2 下载文档

**接口**: `GET /documents/{document_id}/download`

**描述**: 下载指定格式的文档文件

**查询参数**:
- `format`: 文件格式 (pdf, docx, markdown)

**响应**: 文件流

### 3. 用户反馈

#### 3.1 提交反馈

**接口**: `POST /workflows/{workflow_id}/feedback`

**描述**: 对工作流结果提交反馈意见

**请求体**:
```json
{
  "feedback_type": "content_revision",
  "priority": "high",
  "sections": ["技术现状", "发展趋势"],
  "comments": [
    {
      "section": "技术现状",
      "type": "content_addition",
      "content": "需要补充深度学习在影像诊断中的最新进展"
    },
    {
      "section": "发展趋势", 
      "type": "content_modification",
      "content": "对联邦学习的描述需要更加详细"
    }
  ],
  "overall_rating": 7,
  "suggestions": "整体结构清晰，但需要补充更多技术细节"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "feedback_id": "fb_def456ghi789",
    "workflow_id": "wf_abc123def456",
    "status": "received",
    "analysis": {
      "requires_research": true,
      "requires_rewrite": true,
      "estimated_time": "1-2小时"
    },
    "next_steps": [
      "补充深度学习相关调研",
      "重写技术现状章节",
      "扩展发展趋势内容"
    ]
  }
}
```

### 4. 系统管理

#### 4.1 系统状态

**接口**: `GET /system/status`

**描述**: 获取系统运行状态

**响应**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": "72小时15分钟",
    "agents": {
      "total": 6,
      "active": 6,
      "idle": 4,
      "busy": 2
    },
    "workflows": {
      "total": 156,
      "running": 3,
      "completed": 148,
      "failed": 5
    },
    "resources": {
      "cpu_usage": "45%",
      "memory_usage": "68%",
      "disk_usage": "23%"
    }
  }
}
```

## Python SDK

### 安装

```bash
pip install tech-doc-writer-sdk
```

### 基本使用

```python
from tech_doc_writer import TechDocClient

# 初始化客户端
client = TechDocClient(
    api_key="your-api-key",
    base_url="http://localhost:8000/api/v1"
)

# 创建文档生成任务
workflow = client.create_workflow(
    topic="人工智能在医疗诊断中的应用",
    scope="技术现状、挑战和发展趋势",
    format="技术调研报告"
)

# 等待完成
result = workflow.wait_for_completion(timeout=3600)

# 下载文档
document = result.download_document(format="pdf")
```

### 异步使用

```python
import asyncio
from tech_doc_writer import AsyncTechDocClient

async def generate_document():
    client = AsyncTechDocClient(api_key="your-api-key")
    
    # 创建任务
    workflow = await client.create_workflow(
        topic="区块链技术发展趋势",
        scope="技术原理、应用场景、发展前景"
    )
    
    # 监听进度
    async for progress in workflow.progress_stream():
        print(f"进度: {progress.percentage}% - {progress.current_stage}")
    
    # 获取结果
    result = await workflow.get_result()
    return result

# 运行
result = asyncio.run(generate_document())
```

## 错误处理

### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": {
      "field": "topic",
      "reason": "主题不能为空"
    }
  },
  "request_id": "req_123456789",
  "timestamp": "2025-07-30T10:00:00Z"
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| INVALID_REQUEST | 400 | 请求参数无效 |
| UNAUTHORIZED | 401 | 认证失败 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| RATE_LIMITED | 429 | 请求频率超限 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |
| SERVICE_UNAVAILABLE | 503 | 服务暂时不可用 |

## 限制说明

### 请求限制
- 每分钟最多100个请求
- 单个文档最大50,000字
- 同时运行的工作流最多10个

### 支持的语言
- 中文 (zh-CN)
- 英文 (en-US)
- 日文 (ja-JP)

### 支持的文档格式
- Markdown (.md)
- PDF (.pdf)
- Word文档 (.docx)
- HTML (.html)

## 更新日志

### v1.0.0 (2025-07-30)
- 初始版本发布
- 支持基础文档生成功能
- 提供RESTful API和Python SDK
- 支持用户反馈和迭代优化

## 联系支持

如有API使用问题，请联系：
- 技术支持邮箱: <EMAIL>
- 文档反馈: <EMAIL>
- Gitea Issues: https://gitea.example.com/example/tech-doc-writer/issues
