# 项目根目录配置指南

## 概述

项目根目录是所有相对路径的基准目录。系统支持两种方式确定项目根目录：
1. **自动检测**（默认）：通过查找标识文件自动确定
2. **手动配置**：通过配置文件或环境变量明确指定

## 配置位置

在 `config.yaml.example` 的第 **93-94行**：

```yaml
# 项目根目录配置 - 所有相对路径的基准目录（可选，默认自动检测）
# project_root: "${PROJECT_ROOT:-/opt/app}"
```

## 配置方式

### 方式1: 自动检测（默认，推荐）

**无需配置**，系统会自动检测项目根目录：

```yaml
# config.yaml - 不配置 project_root，使用自动检测
data_directory: "./data"
output_directory: "./data/output"
```

**检测逻辑**：
- 从当前文件向上查找包含以下标识文件的目录：
  - `pyproject.toml`
  - `.augmentrules`
  - `main.py`
  - `config.yaml.example`

### 方式2: 配置文件指定

```yaml
# config.yaml - 明确指定项目根目录
project_root: "/opt/app"
data_directory: "./data"
output_directory: "./data/output"
```

### 方式3: 环境变量指定

```bash
# 设置环境变量
export PROJECT_ROOT="/opt/myapp"

# 配置文件中使用环境变量
# config.yaml
project_root: "${PROJECT_ROOT}"
data_directory: "./data"
```

### 方式4: 环境变量 + 默认值

```yaml
# config.yaml - 环境变量优先，有默认值
project_root: "${PROJECT_ROOT:-/app}"
data_directory: "./data"
output_directory: "./data/output"
```

## 使用场景

### 场景1: 开发环境（推荐自动检测）

```yaml
# config.yaml - 开发环境
# project_root: 不配置，使用自动检测
data_directory: "./data"
output_directory: "./data/output"

cache:
  filesystem_base_path: "./data/cache"
```

**效果**：
- 项目根目录：自动检测为 `/path/to/project`
- 数据目录：`/path/to/project/data`
- 输出目录：`/path/to/project/data/output`
- 缓存目录：`/path/to/project/data/cache`

### 场景2: 生产环境（推荐手动配置）

```yaml
# config.yaml - 生产环境
project_root: "${PROJECT_ROOT:-/opt/docwriter}"
data_directory: "./data"
output_directory: "./output"

cache:
  filesystem_base_path: "./cache"
```

```bash
# 环境变量
export PROJECT_ROOT="/opt/docwriter"
```

**效果**：
- 项目根目录：`/opt/docwriter`
- 数据目录：`/opt/docwriter/data`
- 输出目录：`/opt/docwriter/output`
- 缓存目录：`/opt/docwriter/cache`

### 场景3: Docker环境

```yaml
# config.yaml - Docker环境
project_root: "/app"
data_directory: "./data"
output_directory: "./output"

cache:
  filesystem_base_path: "./cache"
```

```dockerfile
# Dockerfile
WORKDIR /app
COPY . /app
ENV PROJECT_ROOT=/app
VOLUME ["/app/data", "/app/output", "/app/cache"]
```

**效果**：
- 项目根目录：`/app`
- 数据目录：`/app/data`
- 输出目录：`/app/output`
- 缓存目录：`/app/cache`

### 场景4: 多实例部署

```yaml
# config.yaml - 实例1
project_root: "${PROJECT_ROOT}"
data_directory: "./data"
output_directory: "./output"
```

```bash
# 实例1
export PROJECT_ROOT="/opt/docwriter/instance1"
python main.py

# 实例2
export PROJECT_ROOT="/opt/docwriter/instance2"
python main.py
```

## 路径解析规则

### 1. 项目根目录确定优先级

1. **配置文件中的 `project_root`**（最高优先级）
2. **环境变量 `PROJECT_ROOT`**
3. **自动检测**（默认）

### 2. 相对路径解析

所有相对路径都基于项目根目录解析：

```yaml
project_root: "/opt/app"
data_directory: "./data"        # 解析为 /opt/app/data
output_directory: "../output"   # 解析为 /opt/output
```

### 3. 绝对路径处理

绝对路径不受项目根目录影响：

```yaml
project_root: "/opt/app"
data_directory: "/var/lib/data"  # 直接使用 /var/lib/data
```

## 验证配置

### 1. 检查项目根目录

```python
from src.backend.config import Config
from src.backend.utils.project_paths import get_path_manager

config = Config()
path_manager = get_path_manager(config)
print(f"项目根目录: {path_manager.project_root}")
```

### 2. 检查所有路径

```python
from src.backend.config import Config
from src.backend.utils.project_paths import get_path_manager

config = Config()
path_manager = get_path_manager(config)

print(f"项目根目录: {path_manager.project_root}")
print(f"数据目录: {path_manager.get_data_dir()}")
print(f"缓存目录: {path_manager.get_cache_dir()}")
print(f"输出目录: {path_manager.get_output_dir()}")
```

### 3. 验证目录创建

```bash
# 运行程序并检查目录是否创建
python main.py

# 检查目录结构
ls -la /your/project/root/
```

## 故障排除

### 问题1: 自动检测失败

```
RuntimeError: Could not find project root directory
```

**原因**: 无法找到标识文件
**解决方案**:
1. 确保在项目目录中运行
2. 手动配置项目根目录：
   ```yaml
   project_root: "/path/to/your/project"
   ```

### 问题2: 路径权限错误

```
PermissionError: [Errno 13] Permission denied
```

**解决方案**:
1. 检查项目根目录权限
2. 确保应用程序用户有访问权限
3. 使用有权限的目录作为项目根目录

### 问题3: 配置不生效

**检查步骤**:
1. 确认配置文件语法正确
2. 检查环境变量是否设置
3. 验证配置加载：
   ```python
   from src.backend.config import Config
   config = Config()
   print(config.project_root)
   ```

## 最佳实践

### 1. 开发环境

- ✅ 使用自动检测（不配置 `project_root`）
- ✅ 使用相对路径
- ✅ 在项目根目录运行程序

### 2. 生产环境

- ✅ 明确配置项目根目录
- ✅ 使用环境变量
- ✅ 使用绝对路径或基于明确根目录的相对路径

### 3. 容器化部署

- ✅ 在 Dockerfile 中设置 `WORKDIR`
- ✅ 配置 `PROJECT_ROOT` 环境变量
- ✅ 使用 Volume 挂载数据目录

### 4. 多环境配置

```yaml
# 基础配置
project_root: "${PROJECT_ROOT}"
data_directory: "${DATA_DIRECTORY:-./data}"
output_directory: "${OUTPUT_DIRECTORY:-./output}"
```

```bash
# 开发环境
export PROJECT_ROOT="/home/<USER>/project"
export DATA_DIRECTORY="./dev-data"

# 测试环境
export PROJECT_ROOT="/opt/test"
export DATA_DIRECTORY="/var/test/data"

# 生产环境
export PROJECT_ROOT="/opt/prod"
export DATA_DIRECTORY="/var/prod/data"
```

## 配置示例总结

### 最简配置（推荐开发环境）

```yaml
# config.yaml - 使用默认自动检测
data_directory: "./data"
output_directory: "./data/output"

cache:
  filesystem_base_path: "./data/cache"
```

### 完整配置（推荐生产环境）

```yaml
# config.yaml - 完整配置
project_root: "${PROJECT_ROOT:-/opt/docwriter}"
data_directory: "${DATA_DIRECTORY:-./data}"
output_directory: "${OUTPUT_DIRECTORY:-./output}"

cache:
  filesystem_base_path: "${CACHE_FILESYSTEM_PATH:-./cache}"
```

通过合理配置项目根目录，可以确保系统在任何环境下都能正确解析路径，彻底解决路径相关的问题。
