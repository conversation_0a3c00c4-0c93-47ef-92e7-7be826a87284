# 开发环境搭建指南

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31

## 1. 前置要求

### 1.1 系统要求
- **操作系统**: macOS 10.15+, Ubuntu 20.04+, Windows 10+
- **Python版本**: 3.11+
- **内存**: 最少8GB，推荐16GB
- **磁盘空间**: 最少10GB可用空间

### 1.2 必需软件
- Python 3.11+
- Git
- uv (Python包管理器)
- 代码编辑器 (推荐VSCode)

## 2. 快速开始

### 2.1 一键安装脚本
```bash
# 下载并运行安装脚本
curl -sSL https://raw.gitea.example.com/your-repo/setup.sh | bash

# 或者手动执行以下步骤
```

### 2.2 手动安装步骤

#### 步骤1: 克隆项目
```bash
git clone <repository-url>
cd 20250729.Spec.Data.Driven.Tech.Doc.Writer
```

#### 步骤2: 安装uv包管理器
```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows (PowerShell)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 或使用pip安装
pip install uv
```

#### 步骤3: 创建虚拟环境
```bash
# 创建Python 3.11虚拟环境
uv venv --python 3.11

# 激活虚拟环境
# macOS/Linux
source .venv/bin/activate

# Windows
.venv\Scripts\activate
```

#### 步骤4: 安装依赖
```bash
# 安装项目依赖
uv pip install -e .

# 安装开发依赖
uv pip install -e ".[dev]"
```

#### 步骤5: 配置环境
```bash
# 复制配置文件模板
cp config.yaml.example config.yaml
cp .env.example .env

# 编辑配置文件
# 设置API密钥和代理配置
```

#### 步骤6: 初始化项目
```bash
# 创建必要目录
mkdir -p data/cache/{raw,converted,llm,metadata}
mkdir -p log

# 初始化数据库
python -m src.database.init_db

# 运行环境检查
python -m src.utils.check_env
```

## 3. 配置详细说明

### 3.1 环境变量配置 (.env)
```bash
# 应用环境
APP_ENVIRONMENT=development
APP_DEBUG=true

# LLM API密钥 (必需)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 网络代理 (必需)
HTTP_PROXY=http://127.0.0.1:8118/
HTTPS_PROXY=http://127.0.0.1:8118/

# 数据库配置
DB_PATH=data/app.db

# 缓存配置
CACHE_DIR=data/cache
CACHE_MAX_SIZE_GB=5

# 日志配置
LOG_LEVEL=DEBUG
LOG_DIR=log
```

### 3.2 主配置文件 (config.yaml)
```yaml
app:
  name: "multi-agent-tech-doc-writer"
  environment: "development"
  debug: true

database:
  type: "sqlite"
  path: "data/app.db"

cache:
  type: "file"
  directory: "data/cache"
  max_memory_items: 500

llm:
  default_provider: "openai"
  retry_attempts: 3
  
  providers:
    openai:
      api_key: "${OPENAI_API_KEY}"
      models:
        default: "gpt-4"
        fast: "gpt-3.5-turbo"

network:
  proxy:
    http: "${HTTP_PROXY}"
    https: "${HTTPS_PROXY}"

logging:
  level: "DEBUG"
  outputs:
    console: true
    file: true
```

## 4. 开发工具配置

### 4.1 VSCode配置 (.vscode/settings.json)
```json
{
  "python.defaultInterpreterPath": "./.venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "python.formatting.provider": "black",
  "python.linting.mypyEnabled": true,
  "files.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true,
    ".venv": false
  }
}
```

### 4.2 推荐VSCode插件
- Python
- Pylance
- Black Formatter
- MyPy Type Checker
- YAML
- Docker

### 4.3 Git配置
```bash
# 设置Git钩子
cp scripts/pre-commit .git/hooks/
chmod +x .git/hooks/pre-commit

# 配置Git忽略文件
echo "已包含在.gitignore中"
```

## 5. 验证安装

### 5.1 环境检查命令
```bash
# 检查Python版本
python --version

# 检查虚拟环境
which python

# 检查依赖安装
uv pip list

# 运行环境检查脚本
python -m src.utils.check_env

# 运行单元测试
python -m pytest test/ -v
```

### 5.2 快速功能测试
```bash
# 测试基础功能
python -c "
from src.config import ConfigManager
config = ConfigManager()
print('配置加载成功')
print(f'环境: {config.get(\"app.environment\")}')
"

# 测试数据库连接
python -c "
from src.database import DatabaseManager
db = DatabaseManager()
print('数据库连接成功')
"

# 测试缓存系统
python -c "
from src.cache import CacheManager
cache = CacheManager()
print('缓存系统初始化成功')
"
```

## 6. 常见问题解决

### 6.1 Python版本问题
```bash
# 如果系统Python版本不符合要求
# 使用pyenv安装指定版本
curl https://pyenv.run | bash
pyenv install 3.11.0
pyenv local 3.11.0

# 重新创建虚拟环境
uv venv --python 3.11
```

### 6.2 网络代理问题
```bash
# 测试代理连接
curl --proxy http://127.0.0.1:8118/ https://www.google.com

# 如果代理不可用，临时禁用
unset HTTP_PROXY HTTPS_PROXY

# 或在配置文件中注释掉代理设置
```

### 6.3 依赖安装问题
```bash
# 清理缓存重新安装
uv cache clean
uv pip install --force-reinstall -e .

# 如果某个包安装失败，单独安装
uv pip install package_name
```

### 6.4 权限问题
```bash
# macOS/Linux权限问题
sudo chown -R $USER:$USER .
chmod -R 755 .

# Windows权限问题
# 以管理员身份运行命令提示符
```

## 7. 开发工作流

### 7.1 日常开发流程
```bash
# 1. 激活虚拟环境
source .venv/bin/activate

# 2. 拉取最新代码
git pull origin main

# 3. 更新依赖（如有变化）
uv pip install -e .

# 4. 运行测试
python -m pytest

# 5. 开始开发
# 编辑代码...

# 6. 运行代码检查
python -m black src/
python -m pylint src/
python -m mypy src/

# 7. 运行测试
python -m pytest test/

# 8. 提交代码
git add .
git commit -m "feat: add new feature"
git push origin feature-branch
```

### 7.2 调试技巧
```bash
# 启用详细日志
export LOG_LEVEL=DEBUG

# 运行单个测试
python -m pytest test/test_specific.py::test_function -v -s

# 使用调试器
python -m pdb src/main.py

# 查看详细错误信息
python -c "
import traceback
try:
    # 你的代码
    pass
except Exception as e:
    traceback.print_exc()
"
```

## 8. 性能优化建议

### 8.1 开发环境优化
- 使用SSD硬盘提高IO性能
- 增加内存减少缓存换页
- 配置合适的缓存大小
- 使用本地代理减少网络延迟

### 8.2 代码开发建议
- 遵循项目代码规范
- 编写单元测试
- 使用类型注解
- 添加详细的文档字符串
- 定期运行性能分析

## 9. 获取帮助

### 9.1 文档资源
- 项目README: `README.md`
- API文档: `doc/api/`
- 设计文档: `doc/design/`
- 开发文档: `doc/dev/`

### 9.2 问题反馈
- 创建Gitea Issue
- 查看常见问题文档
- 联系项目维护者

### 9.3 社区资源
- 项目Wiki
- 开发者论坛
- 技术交流群
