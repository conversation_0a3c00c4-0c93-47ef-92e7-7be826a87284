# 依赖管理规范

## 概述

本项目使用 uv 作为包管理工具，遵循以下依赖管理规范：

## 规范要求

### 1. 使用 uv 命令管理依赖

**添加新依赖：**
```bash
# 添加生产依赖
uv add package-name

# 添加开发依赖
uv add --dev package-name

# 添加可选依赖组
uv add --optional group-name package-name
```

**移除依赖：**
```bash
uv remove package-name
```

**更新依赖：**
```bash
# 更新所有依赖
uv sync

# 更新特定依赖
uv add package-name@latest
```

### 2. pyproject.toml 编辑规范

**允许的手动编辑：**
- 项目元数据（name, version, description, authors）
- 构建配置（build-system, tool.hatch）
- 开发工具配置（tool.black, tool.mypy, tool.pytest）
- 项目脚本（project.scripts）

**禁止的手动编辑：**
- dependencies 数组（应使用 `uv add`）
- optional-dependencies（应使用 `uv add --optional`）

### 3. 当前状态

项目当前的 pyproject.toml 配置是合理的，因为：
1. 依赖列表是项目初始化时设置的核心依赖
2. 所有依赖都有适当的版本约束
3. 依赖按功能分组并有清晰的注释
4. uv.lock 文件正确反映了依赖解析结果

### 4. 后续维护

在后续开发中，应该：
1. 使用 `uv add` 添加新依赖
2. 使用 `uv remove` 移除不需要的依赖
3. 定期运行 `uv sync` 保持环境同步
4. 提交时确保 uv.lock 文件已更新

## 网络代理配置

所有 uv 操作都应该使用代理：

```bash
export HTTP_PROXY=http://127.0.0.1:8118/
export HTTPS_PROXY=http://127.0.0.1:8118/
uv add package-name
```

或者在命令前临时设置：

```bash
HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/ uv add package-name
```
