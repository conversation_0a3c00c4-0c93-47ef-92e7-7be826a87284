# 开发工作流程

## 概述
本文档定义了团队的标准开发工作流程，确保代码质量和交付效率。

## 分支策略
采用 GitFlow 分支模型：

- **main**: 生产环境代码
- **develop**: 开发主分支
- **feature/***: 功能开发分支
- **release/***: 发布准备分支
- **hotfix/***: 紧急修复分支

## 开发流程

### 1. 功能开发
1. 从 develop 分支创建 feature 分支
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/US-001-user-login
   ```

2. 开发功能并提交代码
   ```bash
   git add .
   git commit -m "feat: implement user login functionality"
   ```

3. 推送分支并创建 Pull Request
   ```bash
   git push origin feature/US-001-user-login
   ```

### 2. 代码审查
- 所有代码必须经过至少一人审查
- 审查要点：
  - 代码质量和规范
  - 测试覆盖率
  - 安全性检查
  - 性能考虑
  - LangGraph 0.6.2函数式API使用规范
  - 并行执行和错误处理机制

### 3. 合并流程
1. 通过代码审查后合并到 develop
2. 删除 feature 分支
3. 更新本地 develop 分支

### 4. 发布流程
1. 从 develop 创建 release 分支
2. 进行发布前测试和修复
3. 合并到 main 和 develop
4. 创建版本标签

## 提交规范
使用 Conventional Commits 规范：

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(auth): add user login functionality
fix(api): resolve null pointer exception in user service
docs(readme): update installation instructions
```

## 质量门禁
代码合并前必须满足：

- [ ] 单元测试通过
- [ ] 代码覆盖率 ≥ 80%
- [ ] 静态代码分析通过
- [ ] 安全扫描通过
- [ ] 代码审查通过
- [ ] LangGraph 0.6.2函数式API测试通过
- [ ] 并行执行性能测试达标
- [ ] 函数式API迁移兼容性检查通过

## 持续集成
每次提交触发：

1. 自动化测试
2. 代码质量检查
3. 安全扫描
4. 构建验证

## 部署流程
- **开发环境**: 自动部署 develop 分支
- **测试环境**: 手动部署 release 分支
- **生产环境**: 手动部署 main 分支

## 回滚策略
如果发现生产问题：

1. 立即回滚到上一个稳定版本
2. 创建 hotfix 分支修复问题
3. 经过测试后重新部署

## 工具和环境
- **版本控制**: Git
- **CI/CD**: GitHub Actions
- **代码质量**: SonarQube
- **测试框架**: pytest (支持异步测试)
- **LangGraph版本**: 0.6.2+ (函数式API)
- **Python版本**: 3.11+
- **类型检查**: mypy
- **性能测试**: pytest-benchmark
- **并发测试**: pytest-asyncio

## LangGraph 0.6.2 开发规范

### 函数式API使用规范
1. **优先使用@task装饰器**：新开发的智能体任务应使用@task装饰器
2. **工作流使用@entrypoint**：主工作流入口使用@entrypoint装饰器
3. **并行任务设计**：充分利用函数式API的并行执行能力
4. **检查点配置**：合理配置检查点器，支持容错恢复

### 迁移检查清单
- [ ] 确认StateGraph代码是否需要迁移到函数式API
- [ ] 验证并行执行逻辑的正确性
- [ ] 测试Send API动态分发功能
- [ ] 确认检查点和恢复机制正常工作
- [ ] 性能基准测试通过（≥3倍提升）

### 测试要求
- [ ] 单元测试覆盖率≥80%
- [ ] 集成测试覆盖核心工作流
- [ ] 性能测试验证并行执行效果
- [ ] 端到端测试验证用户场景
- **部署工具**: [具体工具]
