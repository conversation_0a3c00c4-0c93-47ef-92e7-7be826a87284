# Sprint 规划模板

## Sprint 基本信息
- **Sprint 编号**: Sprint-XX
- **开始日期**: YYYY-MM-DD
- **结束日期**: YYYY-MM-DD
- **Sprint 目标**: [本次 Sprint 的主要目标]
- **Scrum Master**: [姓名]
- **Product Owner**: [姓名]

## 团队成员
| 姓名 | 角色 | 可用工作日 | 备注 |
|------|------|------------|------|
| [姓名] | [角色] | [天数] | [请假/培训等] |

## Sprint 目标
[详细描述本次 Sprint 要达成的业务目标和技术目标]

## 选定的用户故事
| 故事ID | 故事标题 | 优先级 | 估算(SP) | 负责人 | 状态 |
|--------|----------|--------|----------|--------|------|
| US-001 | [故事标题] | 高 | 5 | [姓名] | 待办 |
| US-002 | [故事标题] | 中 | 3 | [姓名] | 待办 |

**总估算**: [总故事点数] SP  
**团队速率**: [基于历史数据的预期速率] SP

## 任务分解
### US-001: [故事标题]
- [ ] 任务1：[具体任务描述] - [估算小时] - [负责人]
- [ ] 任务2：[具体任务描述] - [估算小时] - [负责人]
- [ ] 任务3：[具体任务描述] - [估算小时] - [负责人]

### US-002: [故事标题]
- [ ] 任务1：[具体任务描述] - [估算小时] - [负责人]
- [ ] 任务2：[具体任务描述] - [估算小时] - [负责人]

## 技术债务和改进项
- [ ] [技术债务项目1] - [负责人] - [预计工作量]
- [ ] [技术债务项目2] - [负责人] - [预计工作量]

## 风险和依赖
### 风险
1. **风险1**: [风险描述]
   - 影响：[对 Sprint 的影响]
   - 缓解措施：[应对方案]

2. **风险2**: [风险描述]
   - 影响：[对 Sprint 的影响]
   - 缓解措施：[应对方案]

### 依赖
- [外部依赖1]：[描述和预期解决时间]
- [外部依赖2]：[描述和预期解决时间]

## Definition of Done
- [ ] 代码完成并通过代码审查
- [ ] 单元测试覆盖率达到 80% 以上
- [ ] 集成测试通过
- [ ] 功能测试通过
- [ ] 文档更新完成
- [ ] 部署到测试环境
- [ ] Product Owner 验收通过

## 会议安排
- **Daily Standup**: 每日 [时间]
- **Sprint Review**: [日期] [时间]
- **Sprint Retrospective**: [日期] [时间]

## 备注
[其他需要记录的信息]

---
**创建日期**: YYYY-MM-DD  
**创建人**: [姓名]  
**最后更新**: YYYY-MM-DD
