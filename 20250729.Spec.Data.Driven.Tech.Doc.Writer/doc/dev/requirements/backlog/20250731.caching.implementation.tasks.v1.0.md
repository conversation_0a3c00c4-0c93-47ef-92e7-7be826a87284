# 二层缓存系统实施任务清单

## 文档信息
- **文档版本**: v2.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31
- **相关文档**: 20250731.intelligent.caching.strategy.v1.0.md

## 1. Sprint 1 缓存任务 (P0)

### TECH-006: 二层缓存基础框架 (5 SP)
**目标**: 建立二层缓存基础设施
**交付物**:
- Redis内存缓存配置 (L1)
- 文件系统缓存目录结构 (L2)
- 统一缓存接口定义
- 缓存管理逻辑

**技术实现**:
```python
# 二层缓存管理器
class TwoLayerCacheManager:
    def __init__(self):
        self.memory_cache = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            decode_responses=True
        )
        self.file_cache = FileSystemCacheManager()

    async def get(self, key: str) -> Optional[Any]:
        # L1: 内存缓存
        result = await self.memory_cache.get(key)
        if result:
            return json.loads(result)

        # L2: 文件系统缓存
        content = await self.file_cache.read_file(key)
        if content:
            # 热点数据缓存到内存
            if await self.is_hot_data(key):
                ttl = self.get_ttl_for_content_type(self.get_content_type(key))
                await self.memory_cache.setex(key, ttl, json.dumps(content))
            return content

        return None

    async def set(self, key: str, value: Any, content_type: str = 'general',
                  is_raw_file: bool = False):
        # 所有数据都存储到文件系统
        file_path = await self.file_cache.store_content(
            key, value, content_type, is_raw_file
        )

        # 热点数据同时缓存到内存
        if await self.should_cache_to_memory(key, content_type):
            ttl = self.get_ttl_for_content_type(content_type)
            await self.memory_cache.setex(key, ttl, json.dumps(value))

        return file_path

    async def store_raw_file(self, url: str, content: bytes, file_type: str):
        """存储原始网络文件"""
        return await self.file_cache.store_raw_file(url, content, file_type)

    async def store_processed_file(self, source_url: str, content: str,
                                 process_type: str):
        """存储转换后的文件"""
        return await self.file_cache.store_processed_file(
            source_url, content, process_type
        )
```

**验收标准**:
- [ ] Redis内存缓存正常工作
- [ ] 文件系统目录结构创建正确
- [ ] 原始文件和转换文件分别存储
- [ ] 统一接口可以透明访问二层缓存
- [ ] 所有网络获取的文件都能正确缓存

### TECH-007: LLM响应缓存 (8 SP)
**目标**: 实现LLM调用结果的智能缓存
**交付物**:
- LLM调用缓存装饰器
- 输入标准化算法
- 缓存键生成策略
- 基础相似度匹配

**技术实现**:
```python
class LLMCache:
    def __init__(self):
        self.cache_manager = CacheManager()
        self.normalizer = InputNormalizer()
    
    def cache_key(self, agent_type: str, input_text: str, model: str) -> str:
        normalized_input = self.normalizer.normalize(input_text)
        input_hash = hashlib.md5(normalized_input.encode()).hexdigest()
        return f"llm:{agent_type}:{input_hash}:{model}"
    
    async def get_or_compute(self, agent_type: str, input_text: str, 
                           model: str, compute_func: Callable) -> Any:
        cache_key = self.cache_key(agent_type, input_text, model)
        
        # 尝试从缓存获取
        cached_result = await self.cache_manager.get(cache_key)
        if cached_result:
            return json.loads(cached_result)
        
        # 计算新结果
        result = await compute_func()
        
        # 缓存结果 (使用统一配置的TTL)
        ttl = self.get_ttl_for_content_type('llm_responses')
        await self.cache_manager.set(
            cache_key,
            json.dumps(result, ensure_ascii=False),
            ttl=ttl
        )
        
        return result
```

**验收标准**:
- [ ] LLM调用自动缓存
- [ ] 相同输入命中缓存
- [ ] 输入标准化正常
- [ ] 缓存命中率 > 30% (初期目标)

## 2. Sprint 2 缓存任务 (P0)

### TECH-008: 文件系统缓存实现 (8 SP)
**目标**: 实现大文件内容的文件系统缓存
**交付物**:
- Markdown内容文件缓存
- PDF文档文件缓存
- 图片和媒体文件缓存
- 文件系统缓存管理
- 自动目录组织

**技术实现**:
```python
class FileSystemCacheManager:
    def __init__(self):
        self.base_path = "cache/content"
        self.db_cache = DatabaseCacheManager()

        # 确保缓存目录存在
        self.ensure_cache_directories()

    def ensure_cache_directories(self):
        directories = [
            "cache/content/markdown/academic",
            "cache/content/markdown/news",
            "cache/content/markdown/blog",
            "cache/content/pdf",
            "cache/content/images",
            "cache/content/media",
            "cache/temp",
            "cache/index"
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

    async def store_content(self, url: str, content: str, content_type: str,
                          metadata: Dict = None) -> str:
        # 生成文件路径
        file_path = self.generate_file_path(url, content_type)

        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # 写入文件
        if isinstance(content, str):
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        else:  # 二进制内容 (PDF, 图片等)
            with open(file_path, 'wb') as f:
                f.write(content)

        # 记录到数据库
        file_size = os.path.getsize(file_path)
        await self.db_cache.store_file_record(url, {
            'file_path': file_path,
            'content_type': content_type,
            'file_size': file_size,
            'storage_layer': 'filesystem',
            'metadata': metadata or {}
        })

        return file_path

    async def read_file(self, file_path: str) -> Optional[str]:
        if os.path.exists(file_path):
            try:
                # 根据文件扩展名判断读取方式
                if file_path.endswith(('.txt', '.md', '.json')):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        return f.read()
                else:
                    with open(file_path, 'rb') as f:
                        return f.read()
            except Exception as e:
                logger.error(f"Error reading file {file_path}: {e}")
        return None

    def generate_file_path(self, url: str, content_type: str) -> str:
        url_hash = hashlib.md5(url.encode()).hexdigest()
        date_path = datetime.now().strftime("%Y/%m")

        type_mapping = {
            'academic_paper': ('markdown/academic', 'md'),
            'news_article': ('markdown/news', 'md'),
            'blog_post': ('markdown/blog', 'md'),
            'pdf_document': ('pdf', 'pdf'),
            'image': ('images', 'jpg'),
            'video': ('media', 'mp4')
        }

        type_dir, extension = type_mapping.get(content_type, ('markdown/general', 'md'))

        return f"cache/content/{type_dir}/{date_path}/{url_hash}.{extension}"
```

**验收标准**:
- [ ] 文件系统目录结构正确创建
- [ ] Markdown内容正确存储和读取
- [ ] PDF文档正确存储和读取
- [ ] 文件路径在数据库中正确索引
- [ ] 大文件 (>1MB) 自动存储到文件系统
- [ ] 文件缓存命中率 > 30% (初期目标)

## 3. Sprint 3 缓存任务 (P1)

### TECH-009: 智能缓存策略 (8 SP)
**目标**: 实现语义相似度匹配和智能失效
**交付物**:
- 语义相似度计算
- 智能缓存匹配
- 动态TTL调整
- 缓存预热机制

**技术实现**:
```python
class IntelligentCacheStrategy:
    def __init__(self, config: Dict):
        self.cache_manager = CacheManager()
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.config = config
        # 使用统一配置的相似度阈值
        self.similarity_threshold = config.get('similarity_thresholds', {}).get('semantic_match', 0.95)
    
    async def semantic_cache_lookup(self, input_text: str, cache_type: str) -> Optional[Any]:
        # 计算输入的embedding
        input_embedding = self.embedding_model.encode(input_text)
        
        # 搜索相似的缓存项
        pattern = f"{cache_type}:*"
        cache_keys = await self.cache_manager.scan_keys(pattern)
        
        for key in cache_keys:
            cached_item = await self.cache_manager.get(key)
            if cached_item:
                cached_data = json.loads(cached_item)
                cached_embedding = cached_data.get('embedding')
                
                if cached_embedding:
                    similarity = cosine_similarity(
                        input_embedding.reshape(1, -1),
                        np.array(cached_embedding).reshape(1, -1)
                    )[0][0]
                    
                    if similarity > self.similarity_threshold:
                        return cached_data['result']
        
        return None
    
    async def cache_with_embedding(self, key: str, input_text: str, 
                                 result: Any, ttl: int):
        embedding = self.embedding_model.encode(input_text).tolist()
        
        cached_data = {
            'input_text': input_text,
            'embedding': embedding,
            'result': result,
            'timestamp': time.time()
        }
        
        await self.cache_manager.set(
            key,
            json.dumps(cached_data, ensure_ascii=False),
            ttl
        )
```

**验收标准**:
- [ ] 语义相似度匹配工作正常
- [ ] 相似输入能命中缓存
- [ ] 缓存命中率提升到 > 50%
- [ ] 响应时间减少 > 30%

### TECH-010: 缓存监控系统 (5 SP)
**目标**: 实现缓存性能监控和告警
**交付物**:
- 缓存指标收集
- 性能监控面板
- 告警机制
- 缓存统计报告

**技术实现**:
```python
class CacheMonitor:
    def __init__(self):
        self.metrics = {
            'hit_count': 0,
            'miss_count': 0,
            'total_requests': 0,
            'avg_response_time': 0.0,
            'cache_size': 0
        }
    
    def record_hit(self, cache_type: str, response_time: float):
        self.metrics['hit_count'] += 1
        self.metrics['total_requests'] += 1
        self.update_avg_response_time(response_time)
        
        # 记录到时序数据库
        self.influxdb.write_point({
            'measurement': 'cache_metrics',
            'tags': {'type': cache_type, 'result': 'hit'},
            'fields': {'response_time': response_time},
            'time': datetime.utcnow()
        })
    
    def record_miss(self, cache_type: str, response_time: float):
        self.metrics['miss_count'] += 1
        self.metrics['total_requests'] += 1
        self.update_avg_response_time(response_time)
        
        # 记录到时序数据库
        self.influxdb.write_point({
            'measurement': 'cache_metrics',
            'tags': {'type': cache_type, 'result': 'miss'},
            'fields': {'response_time': response_time},
            'time': datetime.utcnow()
        })
    
    def get_hit_rate(self) -> float:
        if self.metrics['total_requests'] == 0:
            return 0.0
        return self.metrics['hit_count'] / self.metrics['total_requests']
    
    def check_alerts(self):
        hit_rate = self.get_hit_rate()
        
        # 缓存命中率过低告警
        if hit_rate < 0.4:
            self.send_alert(f"Cache hit rate too low: {hit_rate:.2%}")
        
        # 响应时间过长告警
        if self.metrics['avg_response_time'] > 100:  # 100ms
            self.send_alert(f"Cache response time too high: {self.metrics['avg_response_time']:.2f}ms")
```

**验收标准**:
- [ ] 缓存指标正确收集
- [ ] 监控面板显示正常
- [ ] 告警机制工作正常
- [ ] 性能报告生成正常

## 4. Sprint 4-6 高级缓存任务 (P2)

### TECH-013: 缓存预热系统 (8 SP)
**目标**: 实现热门主题和用户兴趣预缓存
**交付物**:
- 热门主题识别
- 预缓存调度器
- 用户行为分析
- 个性化预缓存

### TECH-014: 缓存性能优化 (8 SP)
**目标**: 优化缓存性能和资源使用
**交付物**:
- 缓存压缩算法
- 内存使用优化
- 缓存分片策略
- 性能调优

## 5. 缓存配置管理



### 5.2 统一缓存配置
```yaml
# config/cache.yaml - 统一缓存配置
redis:
  host: localhost
  port: 6379
  password: ""
  max_connections: 100

cache_settings:
  default_ttl: 3600
  max_memory: "2GB"
  eviction_policy: "allkeys-lru"

# TTL配置 (与智能缓存策略文档保持一致)
cache_ttl:
  # LLM响应缓存
  llm_responses: 86400        # 24小时
  requirement_analysis: 86400 # 24小时
  content_analysis: 21600     # 6小时
  keyword_extraction: 43200   # 12小时
  document_generation: 3600   # 1小时

  # 网络内容缓存
  web_content: 21600          # 6小时
  api_responses: 43200        # 12小时
  search_results: 21600       # 6小时

  # 用户会话缓存
  user_sessions: 7200         # 2小时
  system_config: 3600         # 1小时

  # 默认TTL
  default: 3600               # 1小时

# 热点数据阈值配置
hot_data_thresholds:
  access_count_threshold: 5   # 访问次数超过5次
  access_frequency: 3600      # 1小时内访问频率
  cache_to_memory_types:      # 优先缓存到内存的类型
    - llm_response
    - api_metadata
    - user_session
    - system_config

# 语义相似度配置
similarity_thresholds:
  exact_match: 1.0           # 精确匹配
  semantic_match: 0.95       # 语义匹配
  fuzzy_match: 0.85          # 模糊匹配
  min_threshold: 0.80        # 最低阈值
```

### 5.3 缓存键命名规范
```
格式: {类型}:{子类型}:{标识符}:{版本}

示例:
- llm:requirement_analyst:a1b2c3d4:v1.0
- web:academic_paper:e5f6g7h8:latest
- api:arxiv:search:m5n4o3p2:v1.0
- task:intent_analysis:x9y8z7w6:v1.0
```

## 6. 测试策略

### 6.1 单元测试
- 缓存基础操作测试
- TTL机制测试
- 相似度匹配测试
- 性能指标测试

### 6.2 集成测试
- 端到端缓存流程测试
- 多组件缓存协作测试
- 故障恢复测试
- 性能压力测试

### 6.3 性能测试
- 缓存命中率测试
- 响应时间测试
- 并发访问测试
- 内存使用测试

## 7. 部署和运维

### 7.1 Redis集群配置
```bash
# Redis主从配置
redis-server --port 6379 --slaveof 127.0.0.1 6380
redis-server --port 6380

# Redis Sentinel配置
redis-sentinel sentinel.conf
```

### 7.2 监控告警
- 缓存命中率 < 40% 告警
- 内存使用率 > 80% 告警
- 响应时间 > 100ms 告警
- Redis连接失败告警

### 7.3 备份策略
- Redis RDB快照备份
- AOF日志备份
- 定期数据导出
- 灾难恢复预案

## 8. 成功指标

### 8.1 性能指标
- **LLM缓存命中率**: > 60%
- **数据缓存命中率**: > 40%
- **响应时间减少**: > 50%
- **Token消耗减少**: > 50%

### 8.2 成本指标
- **LLM API成本**: 节约 > 50%
- **网络带宽**: 节约 > 40%
- **服务器资源**: 节约 > 30%

### 8.3 用户体验指标
- **系统响应时间**: < 15分钟
- **用户满意度**: > 4.0/5.0
- **系统可用性**: > 99.5%
