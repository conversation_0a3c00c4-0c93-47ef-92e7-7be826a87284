# 需求分析阶段优先实现策略

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31
- **相关PRD**: 20250731.multi.agent.tech.research.system.prd.v1.0.md

## 1. 策略概述

### 1.1 核心理念
基于"流程跑通优先"的原则，优先实现需求分析阶段的核心功能，确保从用户输入到基础调研报告的完整流程能够端到端运行，为后续功能迭代奠定坚实基础。

### 1.2 实现原则
- **MVP优先**: 先实现最小可行产品，确保核心价值交付
- **流程完整**: 优先保证端到端流程的完整性
- **质量可控**: 在功能简化的同时保证输出质量
- **迭代优化**: 基于用户反馈持续优化和扩展功能

## 2. 需求分析阶段功能分解

### 2.1 P0 核心功能 (Sprint 1-2)

#### 2.1.1 用户需求输入处理 (Sprint 1)
**功能描述**: 接收和初步处理用户的自然语言调研需求

**最小实现**:
- 简单的文本输入界面 (命令行 + Web表单)
- 基础的输入验证和清理
- 需求文本的结构化存储

**技术方案**:
```python
class RequirementInput:
    def process_user_input(self, raw_input: str) -> StructuredRequirement:
        # 基础文本清理
        cleaned_text = self.clean_text(raw_input)
        
        # 简单结构化
        requirement = StructuredRequirement(
            raw_text=raw_input,
            cleaned_text=cleaned_text,
            timestamp=datetime.now(),
            language=self.detect_language(cleaned_text)
        )
        
        return requirement
```

**验收标准**:
- 支持中英文输入
- 输入长度限制: 50-2000字符
- 处理时间 < 5秒
- 输入格式验证准确率 > 95%

#### 2.1.2 基础意图识别 (Sprint 1)
**功能描述**: 识别用户调研需求的基本意图和类型

**最小实现**:
- 基于关键词匹配的意图分类
- 支持5种基础调研类型识别
- 简单的领域识别

**调研类型分类**:
1. **技术概述型**: 了解某项技术的基本情况
2. **现状分析型**: 分析技术或行业的当前状态
3. **趋势预测型**: 预测技术或行业的发展趋势
4. **应用案例型**: 了解技术的具体应用场景
5. **对比分析型**: 对比不同技术或解决方案

**技术方案**:
```python
class IntentAnalyzer:
    def __init__(self):
        self.intent_keywords = {
            'tech_overview': ['是什么', '介绍', '概述', 'what is', 'overview'],
            'current_status': ['现状', '状态', '发展情况', 'current', 'status'],
            'trend_analysis': ['趋势', '发展', '未来', 'trend', 'future'],
            'use_cases': ['应用', '案例', '场景', 'application', 'use case'],
            'comparison': ['对比', '比较', '差异', 'compare', 'vs']
        }
    
    def analyze_intent(self, requirement: StructuredRequirement) -> IntentAnalysis:
        # 基于关键词匹配的简单意图识别
        scores = {}
        for intent, keywords in self.intent_keywords.items():
            score = self.calculate_keyword_score(requirement.cleaned_text, keywords)
            scores[intent] = score
        
        primary_intent = max(scores, key=scores.get)
        confidence = scores[primary_intent]
        
        return IntentAnalysis(
            primary_intent=primary_intent,
            confidence=confidence,
            all_scores=scores
        )
```

#### 2.1.3 关键词提取和扩展 (Sprint 1)
**功能描述**: 从用户需求中提取关键词并进行适当扩展

**最小实现**:
- 基于TF-IDF的关键词提取
- 简单的同义词扩展
- 中英文关键词生成

**技术方案**:
```python
class KeywordExtractor:
    def extract_keywords(self, requirement: StructuredRequirement) -> List[Keyword]:
        # 使用jieba分词 (中文) + NLTK (英文)
        tokens = self.tokenize(requirement.cleaned_text, requirement.language)
        
        # TF-IDF关键词提取
        keywords = self.tfidf_extract(tokens, top_k=10)
        
        # 简单同义词扩展
        expanded_keywords = self.expand_synonyms(keywords)
        
        return expanded_keywords
    
    def expand_synonyms(self, keywords: List[str]) -> List[Keyword]:
        # 基于预定义词典的同义词扩展
        expanded = []
        for kw in keywords:
            synonyms = self.synonym_dict.get(kw, [])
            expanded.append(Keyword(
                term=kw,
                synonyms=synonyms,
                language=self.detect_language(kw)
            ))
        return expanded
```

#### 2.1.4 简化调研范围界定 (Sprint 2)
**功能描述**: 基于意图和关键词界定调研的基本范围

**最小实现**:
- 基于意图类型的范围模板
- 时间范围建议 (默认近2年)
- 基础信息源类型选择

**范围界定模板**:
```python
SCOPE_TEMPLATES = {
    'tech_overview': {
        'time_range': '2022-2024',
        'source_types': ['academic', 'tech_media', 'official_docs'],
        'content_focus': ['definition', 'principles', 'key_features'],
        'depth_level': 'basic'
    },
    'current_status': {
        'time_range': '2023-2024',
        'source_types': ['industry_reports', 'news', 'market_data'],
        'content_focus': ['market_size', 'adoption_rate', 'key_players'],
        'depth_level': 'intermediate'
    },
    # ... 其他模板
}
```

#### 2.1.5 基础调研提纲生成 (Sprint 2)
**功能描述**: 生成结构化的调研报告提纲

**最小实现**:
- 基于意图类型的提纲模板
- 3级标题结构
- 章节内容要点提示

**提纲模板示例**:
```markdown
# {技术名称}技术调研报告

## 1. 执行摘要
- 技术定义和核心特点
- 主要发现和结论
- 关键建议

## 2. 技术概述
### 2.1 技术定义
### 2.2 核心原理
### 2.3 关键特性

## 3. 发展现状
### 3.1 技术成熟度
### 3.2 市场采用情况
### 3.3 主要厂商和产品

## 4. 应用场景
### 4.1 典型应用领域
### 4.2 成功案例分析
### 4.3 应用效果评估

## 5. 发展趋势
### 5.1 技术发展方向
### 5.2 市场趋势预测
### 5.3 挑战和机遇

## 6. 结论和建议
### 6.1 主要结论
### 6.2 实施建议
### 6.3 风险提示

## 参考文献
```

### 2.2 P1 增强功能 (Sprint 3-4)

#### 2.2.1 智能意图识别 (Sprint 3)
**功能描述**: 使用LLM提升意图识别的准确性

**增强实现**:
- 集成LLM进行语义理解
- 支持复合意图识别
- 意图置信度评估

#### 2.2.2 智能关键词优化 (Sprint 3)
**功能描述**: 基于LLM的关键词生成和优化

**增强实现**:
- LLM辅助关键词生成
- 上下文相关的关键词扩展
- 多语言关键词对应

#### 2.2.3 动态提纲调整 (Sprint 4)
**功能描述**: 基于关键词和范围动态调整提纲结构

**增强实现**:
- 个性化提纲生成
- 章节重要性排序
- 内容深度调整

### 2.3 P2 高级功能 (Sprint 5+)

#### 2.3.1 用户偏好学习
- 基于历史需求的偏好识别
- 个性化模板推荐
- 智能默认设置

#### 2.3.2 协作需求分析
- 多用户需求合并
- 需求冲突检测和解决
- 团队偏好管理

## 3. 技术实现架构

### 3.1 需求分析智能体架构
```python
class RequirementAnalystAgent:
    def __init__(self):
        self.input_processor = RequirementInputProcessor()
        self.intent_analyzer = IntentAnalyzer()
        self.keyword_extractor = KeywordExtractor()
        self.scope_definer = ScopeDefiner()
        self.outline_generator = OutlineGenerator()
    
    async def analyze_requirement(self, user_input: str) -> RequirementAnalysisResult:
        # 1. 输入处理
        structured_req = self.input_processor.process(user_input)
        
        # 2. 意图分析
        intent = self.intent_analyzer.analyze(structured_req)
        
        # 3. 关键词提取
        keywords = self.keyword_extractor.extract(structured_req)
        
        # 4. 范围界定
        scope = self.scope_definer.define(intent, keywords)
        
        # 5. 提纲生成
        outline = self.outline_generator.generate(intent, keywords, scope)
        
        return RequirementAnalysisResult(
            structured_requirement=structured_req,
            intent_analysis=intent,
            keywords=keywords,
            research_scope=scope,
            document_outline=outline
        )
```

### 3.2 数据模型设计
```python
@dataclass
class StructuredRequirement:
    raw_text: str
    cleaned_text: str
    language: str
    timestamp: datetime
    user_id: Optional[str] = None

@dataclass
class IntentAnalysis:
    primary_intent: str
    confidence: float
    secondary_intents: List[Tuple[str, float]]
    domain: Optional[str] = None

@dataclass
class ResearchScope:
    time_range: Tuple[str, str]
    geographic_scope: List[str]
    source_types: List[str]
    content_focus: List[str]
    depth_level: str

@dataclass
class DocumentOutline:
    title: str
    sections: List[Section]
    estimated_length: int
    complexity_level: str
```

## 4. 质量保证策略

### 4.1 输入质量控制
- **输入验证**: 长度、格式、语言检查
- **意图明确性**: 模糊需求的澄清提示
- **关键信息完整性**: 必要信息的补充引导

### 4.2 输出质量保证
- **提纲逻辑性**: 章节结构的逻辑检查
- **内容完整性**: 关键要素的覆盖检查
- **可执行性**: 后续调研的可操作性验证

### 4.3 用户反馈机制
- **实时反馈**: 分析过程中的用户确认
- **结果调整**: 基于用户反馈的快速调整
- **学习优化**: 反馈数据用于模型优化

## 5. 性能优化

### 5.1 响应时间优化
- **并行处理**: 意图分析和关键词提取并行
- **二层缓存**: LLM调用结果内存缓存，所有处理结果文件系统缓存
- **避免重复处理**: 所有分析结果都持久化，避免重复计算
- **预计算**: 热门领域的模板预生成
- **Token优化**: 通过缓存减少重复LLM调用

### 5.2 准确性提升
- **多模型集成**: 结合规则和机器学习方法
- **置信度评估**: 输出结果的可信度评分
- **人工校验**: 低置信度结果的人工审核

## 6. 测试策略

### 6.1 功能测试
- **单元测试**: 各组件功能的独立测试
- **集成测试**: 端到端流程测试
- **边界测试**: 极端输入情况的处理

### 6.2 性能测试
- **响应时间**: 各阶段处理时间测试
- **并发能力**: 多用户同时使用测试
- **资源消耗**: 内存和CPU使用监控

### 6.3 用户验收测试
- **真实场景**: 实际用户需求的测试
- **易用性**: 用户体验和界面友好性
- **准确性**: 输出结果的用户满意度

## 7. 风险控制

### 7.1 技术风险
- **LLM依赖**: 多模型备选方案
- **中文处理**: 中文NLP工具的准确性
- **性能瓶颈**: 处理复杂需求的性能问题

### 7.2 业务风险
- **需求理解偏差**: 用户意图识别错误
- **提纲不合理**: 生成的提纲不符合预期
- **用户接受度**: 自动化分析的用户信任度

### 7.3 缓解措施
- **多重验证**: 多种方法交叉验证结果
- **用户参与**: 关键节点的用户确认
- **渐进优化**: 基于反馈的持续改进

## 8. 成功指标

### 8.1 功能指标
- **意图识别准确率**: > 85%
- **关键词提取相关性**: > 80%
- **提纲用户满意度**: > 4.0/5.0

### 8.2 性能指标
- **分析响应时间**: < 15秒 (缓存优化后)
- **缓存命中率**: LLM调用缓存命中率 > 60%
- **Token节省**: 通过缓存减少token消耗 > 50%
- **系统可用性**: > 99%
- **并发处理能力**: 支持50+用户

### 8.3 业务指标
- **用户采用率**: > 70%
- **需求分析成功率**: > 90%
- **后续调研启动率**: > 85%

## 9. 后续迭代计划

### 9.1 短期优化 (1-2个月)
- 基于用户反馈优化意图识别
- 扩展关键词词典和同义词库
- 增加更多提纲模板

### 9.2 中期增强 (3-6个月)
- 集成更强大的LLM模型
- 实现个性化需求分析
- 支持多轮对话式需求澄清

### 9.3 长期规划 (6个月+)
- 构建领域专业知识图谱
- 实现智能需求推荐
- 支持复杂项目的需求分解
