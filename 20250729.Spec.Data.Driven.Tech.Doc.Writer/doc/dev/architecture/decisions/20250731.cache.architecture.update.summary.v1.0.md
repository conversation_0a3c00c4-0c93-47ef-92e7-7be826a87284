# 缓存架构更新总结

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31

## 1. 更新概述

根据用户需求，对缓存架构进行了重大调整，从三层缓存简化为二层缓存，并统一了技术栈。

### 1.1 主要变更
1. **架构简化**: 从三层缓存（内存+数据库+文件系统）简化为二层缓存（内存+文件系统）
2. **缓存策略调整**: 缓存策略与文件大小无关，所有网络文件和转换文件都缓存到文件系统
3. **技术栈统一**: 统一使用PostgreSQL作为业务数据库，消除SQLite/PostgreSQL的不一致
4. **职责明确**: 数据库专门存储业务数据，不再作为缓存层

### 1.2 设计原则
- **避免重复网络请求**: 所有网络获取的原始文件都缓存到文件系统
- **避免重复格式转换**: 所有抽取转换的文件都缓存到文件系统
- **热点数据内存缓存**: 高频访问数据缓存到内存提升性能
- **统一接口**: 透明的缓存访问，自动管理两层缓存

## 2. 新的二层缓存架构

### 2.1 架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                        应用层                                    │
│                 统一缓存接口 (CacheManager)                       │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
┌───────────────▼─────┐ ┌──────▼──────────────────┐
│   L1: 内存缓存层     │ │   L2: 文件系统缓存层     │
│     Redis           │ │     Local Files         │
├─────────────────────┤ ├─────────────────────────┤
│• LLM响应结果        │ │• 原始网络文件            │
│• API响应元数据      │ │  - HTML页面             │
│• 用户会话数据       │ │  - PDF文档              │
│• 系统配置信息       │ │  - 图片/视频            │
│• 热点查询结果       │ │  - JSON/XML数据         │
│• 临时计算结果       │ │• 转换后文件             │
│                     │ │  - Markdown文档         │
│                     │ │  - 提取的纯文本         │
│                     │ │  - 结构化数据           │
└─────────────────────┘ └─────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                    PostgreSQL 数据库                            │
│                   (业务数据，非缓存)                             │
├─────────────────────────────────────────────────────────────────┤
│• 用户数据           │• 项目数据           │• 系统配置           │
│• 调研任务记录       │• 工作流状态         │• 访问日志           │
│• 文档元数据         │• 用户行为统计       │• 性能指标           │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 文件系统目录结构
```
cache/
├── raw/                       # 原始网络文件
│   ├── html/                  # HTML页面
│   ├── pdf/                   # PDF文档
│   ├── images/                # 图片文件
│   ├── media/                 # 音视频文件
│   ├── json/                  # API响应JSON
│   └── xml/                   # XML数据
├── processed/                 # 转换后文件
│   ├── markdown/              # 转换的Markdown
│   │   ├── academic/          # 学术论文
│   │   ├── news/              # 新闻文章
│   │   └── general/           # 通用内容
│   ├── text/                  # 提取的纯文本
│   ├── structured/            # 结构化数据
│   └── summaries/             # 摘要文件
├── temp/                      # 临时文件
└── index/                     # 本地索引文件
```

## 3. 更新的文档列表

### 3.1 主要文档更新
1. **PRD主文档**: `20250731.multi.agent.tech.research.system.prd.v1.0.md`
   - 技术栈统一为PostgreSQL
   - 缓存系统重新设计为二层架构
   - 功能描述更新

2. **智能缓存策略**: `20250731.intelligent.caching.strategy.v1.0.md`
   - 从三层架构重新设计为二层架构
   - 缓存策略与文件大小无关
   - 所有网络文件和转换文件都缓存到文件系统

3. **缓存实施任务**: `20250731.caching.implementation.tasks.v1.0.md`
   - 任务重新规划为二层缓存实施
   - 技术实现代码更新
   - 验收标准调整

4. **产品待办事项**: `20250731.product.backlog.v1.0.md`
   - 缓存相关任务重新规划
   - Sprint计划调整
   - 工作量重新估算

5. **多数据源策略**: `20250731.multi.source.data.acquisition.strategy.v1.0.md`
   - 缓存策略部分更新
   - 技术实现代码调整

6. **需求分析策略**: `20250731.requirement.analysis.priority.strategy.v1.0.md`
   - 缓存优化部分更新

### 3.2 新增文档
1. **二层缓存架构**: `20250731.two.layer.cache.architecture.v1.0.md`
   - 全新的二层缓存架构设计文档
   - 详细的技术实现方案
   - 性能监控和维护策略

### 3.3 删除文档
1. **三层缓存架构**: `20250731.three.layer.cache.architecture.v1.0.md`
   - 已删除，替换为二层缓存架构

## 4. 技术栈统一

### 4.1 数据库技术栈
- **统一使用**: PostgreSQL（开发和生产环境）
- **消除不一致**: 移除所有SQLite相关配置和代码
- **角色明确**: PostgreSQL专门用于业务数据存储，不作为缓存层

### 4.2 缓存技术栈
- **内存缓存**: Redis（热点数据）
- **文件缓存**: 本地文件系统（所有原始文件和转换文件）
- **统一接口**: TwoLayerCacheManager

## 5. 缓存策略调整

### 5.1 原始文件缓存
- **所有网络文件**: 不论大小，都缓存到文件系统
- **文件类型**: HTML、PDF、图片、视频、JSON、XML等
- **目录组织**: 按类型和日期组织目录结构
- **避免重复下载**: 所有网络请求结果都持久化

### 5.2 转换文件缓存
- **所有转换结果**: 不论大小，都缓存到文件系统
- **转换类型**: HTML转Markdown、PDF提取文本、OCR识别等
- **避免重复转换**: 所有格式转换结果都持久化
- **版本管理**: 支持同一源文件的多种转换格式

### 5.3 内存缓存策略
- **热点数据**: 高频访问的数据缓存到内存
- **LLM响应**: 智能体处理结果的临时缓存
- **用户会话**: 当前用户状态和偏好
- **系统配置**: 热门关键词、系统设置

## 6. 实施计划调整

### 6.1 Sprint 1 (29 SP)
- 二层缓存基础框架 (5 SP)
- 原始文件缓存 (8 SP)
- 基础架构搭建 (16 SP)

### 6.2 Sprint 2 (28 SP)
- 转换文件缓存 (5 SP)
- LLM响应缓存 (5 SP)
- 工作流引擎 (18 SP)

### 6.3 Sprint 3及后续
- 缓存管理优化
- 性能监控系统
- 自动维护机制

## 7. 预期效果

### 7.1 性能提升
- **避免重复网络请求**: 网络请求减少 70%+
- **避免重复格式转换**: 处理时间减少 60%+
- **热点数据快速访问**: 响应时间减少 50%+

### 7.2 架构简化
- **减少复杂性**: 从三层简化为二层，降低维护成本
- **统一技术栈**: 消除SQLite/PostgreSQL不一致问题
- **明确职责**: 缓存和业务数据分离，职责清晰

### 7.3 开发效率
- **统一接口**: 简化应用层开发
- **自动管理**: 缓存策略自动化，减少手动干预
- **易于扩展**: 简化的架构更容易扩展和优化

## 8. 风险控制

### 8.1 存储空间管理
- **自动清理**: 定期清理过期和低频访问文件
- **压缩存储**: 大文件自动压缩，节省空间
- **监控告警**: 存储空间使用率监控

### 8.2 性能监控
- **缓存命中率**: 实时监控两层缓存命中率
- **响应时间**: 监控缓存访问性能
- **错误处理**: 缓存失败的降级策略

### 8.3 数据一致性
- **原子操作**: 确保缓存操作的原子性
- **失效策略**: 合理的缓存失效和更新机制
- **备份恢复**: 重要缓存数据的备份策略

这次更新大幅简化了缓存架构，统一了技术栈，明确了缓存策略，为系统的高性能和可维护性奠定了坚实基础。
