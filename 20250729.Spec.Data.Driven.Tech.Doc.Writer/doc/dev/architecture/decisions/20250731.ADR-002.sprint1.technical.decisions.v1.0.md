# ADR-002: Sprint 1 技术决策记录

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31
- **决策类型**: 架构决策记录 (Architecture Decision Record, ADR)
- **决策状态**: 已接受 (Accepted)
- **相关文档**: 
  - [ADR-001: 多智能体架构优化决策](./20250731.ADR-001.multi.agent.architecture.optimization.v1.0.md)
  - [产品待办事项列表](../../requirements/backlog/20250731.product.backlog.v1.0.md)

## 决策背景

基于 Sprint 1 的开发目标"函数式API优先策略：基于LangGraph 0.6函数式API搭建基础架构，实现高性能多智能体系统的技术基础"，需要对关键技术实现方案进行明确决策，确保开发团队能够高效协作并交付高质量的基础架构。

**核心策略调整**：经过技术调研和架构分析，决定采用函数式API优先策略，完全基于LangGraph 0.6的@task和@entrypoint装饰器实现多智能体系统，以实现设计文档承诺的3-5倍性能提升。

## 核心技术决策

### 1. 多智能体基础框架实现方案

**决策**: **函数式API优先** - 完全基于LangGraph 0.6的@task和@entrypoint装饰器

**LangGraph 0.6 函数式API核心优势**:
- **@task装饰器**: 自动支持并行执行、检查点机制和错误恢复
- **@entrypoint装饰器**: 简化工作流组织，减少50%样板代码
- **Send API**: 支持动态任务分发和智能负载均衡
- **原生并行**: 天然支持并发执行，实现3-5倍性能提升
- **自动检查点**: 无需手动管理状态，自动保存和恢复
- **类型安全**: 与Pydantic模型完美集成

**实施方案**:
```python
# 使用函数式API构建智能体
from langgraph import task, entrypoint
from langgraph.graph import MessagesState
from langgraph.types import Send

class RequirementAnalystAgent:
    @task
    async def parse_user_input(self, state: MessagesState) -> MessagesState:
        """解析用户输入任务"""
        # 自动支持并行执行和检查点
        pass

    @task
    async def analyze_intent(self, state: MessagesState) -> MessagesState:
        """意图分析任务"""
        pass

    @entrypoint
    async def requirement_analysis_workflow(self, state: MessagesState) -> MessagesState:
        """需求分析主工作流"""
        # 并行执行多个任务
        parsed = await self.parse_user_input(state)
        analyzed = await self.analyze_intent(parsed)
        return analyzed

# Send API动态任务分发
@task
async def distribute_research_tasks(state: MessagesState) -> List[Send]:
    """动态分发调研任务"""
    tasks = []
    for topic in state["research_topics"]:
        tasks.append(Send("research_task", {"topic": topic}))
    return tasks
```

**理由**:
- 完全符合设计文档的函数式API优先原则
- 自动实现3-5倍性能提升和10+并发任务支持
- 减少50%样板代码，提升40-60%开发效率
- 原生支持协调者-执行者模式和Send API动态分发

### 2. BaseAgent 基类标准生命周期方法定义

**决策**: **函数式API原生设计** - 完全基于@task装饰器的BaseAgent基类

**设计原则**:
- **函数式优先**: 所有核心方法使用@task装饰器，自动支持并行和检查点
- **Send API集成**: 原生支持动态任务分发和负载均衡
- **自动状态管理**: 利用函数式API自动检查点，无需手动状态管理
- **并行执行**: 天然支持并发执行，实现性能提升
- **类型安全**: 与Pydantic模型完美集成

**核心接口设计**:
```python
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from pydantic import BaseModel
from langgraph import task, entrypoint
from langgraph.graph import MessagesState
from langgraph.types import Send

class BaseAgent(ABC):
    """
    基于LangGraph 0.6函数式API的智能体基类
    使用@task装饰器实现并行执行和自动检查点
    """

    # === 函数式API核心方法 ===
    @abstractmethod
    @task
    async def execute_task(self, state: MessagesState) -> MessagesState:
        """使用@task装饰器的核心执行方法"""
        pass

    @abstractmethod
    @task
    async def validate_input(self, state: MessagesState) -> bool:
        """输入验证任务"""
        pass

    @abstractmethod
    @task
    async def process_parallel_tasks(self, state: MessagesState) -> List[Send]:
        """生成并行任务的Send对象列表"""
        pass

    # === 工作流组织方法 ===
    @entrypoint
    async def run_workflow(self, state: MessagesState) -> MessagesState:
        """使用@entrypoint装饰器的主工作流"""
        # 自动管理任务依赖和并行执行
        is_valid = await self.validate_input(state)
        if not is_valid:
            raise ValueError("Input validation failed")

        result = await self.execute_task(state)

        # Send API动态分发并行任务
        parallel_sends = await self.process_parallel_tasks(result)
        if parallel_sends:
            return Send(parallel_sends)

        return result
```

**理由**:
- 完全符合LangGraph 0.6函数式API最佳实践
- 自动支持并行执行、检查点机制和错误恢复
- Send API原生支持动态任务分发
- 减少样板代码，提升开发效率
- 实现设计文档承诺的性能提升目标

### 3. 二层缓存存储策略

**决策**: A) 按内容类型分类：`/cache/raw_files/`, `/cache/processed/`, `/cache/llm_responses/`

**目录结构设计**:
```
cache/
├── raw_files/          # 原始网络文件
│   ├── web_pages/      # 网页内容
│   │   ├── html/       # 原始 HTML
│   │   └── metadata/   # 页面元数据
│   ├── documents/      # 文档文件 (PDF, DOC, etc.)
│   └── images/         # 图片文件
├── processed/          # 转换后的文件
│   ├── markdown/       # 转换为 Markdown
│   ├── text/          # 提取的纯文本
│   └── structured/     # 结构化数据 (JSON, YAML)
└── llm_responses/      # LLM 调用结果
    ├── analysis/       # 分析结果
    ├── generation/     # 生成内容
    └── evaluation/     # 评估结果
```

**实施方案**:
```python
class TwoLayerCacheManager:
    def __init__(self):
        self.memory_cache = redis.Redis()  # L1: Redis 内存缓存
        self.file_cache = FileSystemCache()  # L2: 文件系统缓存
    
    async def get(self, key: str) -> Optional[Any]:
        # L1: 内存缓存
        result = await self.memory_cache.get(key)
        if result:
            return json.loads(result)
        
        # L2: 文件系统缓存
        content = await self.file_cache.read_file(key)
        if content:
            # 热点数据缓存到内存
            if await self.is_hot_data(key):
                await self.memory_cache.setex(key, 86400, json.dumps(content))
            return content
        
        return None
```

**理由**:
- 按内容类型分类便于管理和清理
- 支持不同类型数据的差异化处理策略
- 便于实现数据生命周期管理
- 支持缓存性能监控和优化

### 4. Redis 缓存 TTL 策略

**决策**: A) 固定 TTL：所有数据 24 小时过期

**实施细节**:
- **内存缓存**: 所有数据统一 24 小时 TTL
- **文件缓存**: 永久保存，通过定期清理维护
- **热点数据**: 自动续期机制
- **手动管理**: 支持手动清理和刷新

**配置示例**:
```python
CACHE_CONFIG = {
    "redis": {
        "default_ttl": 86400,  # 24 小时
        "hot_data_ttl": 3600,  # 热点数据 1 小时续期
        "max_memory": "2gb"
    },
    "file_system": {
        "max_size": "50gb",
        "cleanup_interval": "7d",
        "retention_period": "30d"
    }
}
```

**理由**:
- 简化缓存管理逻辑
- 避免复杂的 TTL 策略带来的维护成本
- 24 小时足够覆盖大部分使用场景
- 便于监控和调试

### 5. 智能缺省配置实现范围

**决策**: B) 分层配置：核心配置必需，功能配置可选，优化配置自动

**配置层次设计**:
```yaml
# config.yaml.example
core:
  llm:
    api_key: "${LLM_API_KEY}"  # 必需，从环境变量读取
    model: "gpt-4"             # 可选，有缺省值

functional:
  database:
    url: "sqlite:///data/app.db"  # 可选，自动检测
  cache:
    redis_url: "redis://localhost:6379"  # 可选，自动检测
  proxy:
    http: "${HTTP_PROXY}"      # 可选，从环境变量读取
    https: "${HTTPS_PROXY}"

optimization:
  concurrency:
    max_workers: 4             # 自动根据 CPU 核数设置
  performance:
    batch_size: 10             # 自动优化
  monitoring:
    log_level: "INFO"          # 环境感知
```

**智能缺省机制**:
```python
class ConfigManager:
    def load_config(self):
        config = self.load_base_config()
        
        # 环境自适应
        if self.is_development():
            config.update(self.dev_defaults())
        elif self.is_production():
            config.update(self.prod_defaults())
        
        # 服务自动发现
        config = self.auto_discover_services(config)
        
        # 性能参数自动优化
        config = self.optimize_performance_params(config)
        
        return config
```

**理由**:
- 降低用户使用门槛
- 支持不同环境的自动适配
- 减少配置错误和维护成本
- 提高开发和部署效率

### 6. Sprint 1 CLI 界面实现深度

**决策**: C) 渐进实现：先基础框架，后续 Sprint 完善交互

**Sprint 1 实现范围**:
```python
# 基础命令结构
class CLI:
    def __init__(self):
        self.parser = argparse.ArgumentParser()
        self.setup_commands()
    
    def setup_commands(self):
        # 基础命令
        self.parser.add_argument('--config', help='配置文件路径')
        self.parser.add_argument('--verbose', action='store_true')
        
        # 子命令
        subparsers = self.parser.add_subparsers(dest='command')
        
        # 需求输入命令
        req_parser = subparsers.add_parser('analyze')
        req_parser.add_argument('input', help='需求描述')
        req_parser.add_argument('--output', help='输出文件')
        
        # 状态查询命令
        status_parser = subparsers.add_parser('status')
        status_parser.add_argument('--task-id', help='任务ID')
```

**后续完善计划**:
- Sprint 2: 交互式需求输入
- Sprint 3: 实时进度监控
- Sprint 4: 丰富的输出格式
- Sprint 5: 配置管理命令

**理由**:
- 专注于核心功能实现
- 避免过早优化用户界面
- 便于快速验证基础架构
- 支持迭代改进

### 7. 代理配置自动化程度

**决策**: B) 配置化：从环境变量和 config.yaml 读取代理设置，环境变量优先级高于配置文件

**配置优先级**:
1. **环境变量**: `HTTP_PROXY`, `HTTPS_PROXY`
2. **配置文件**: `config.yaml` 中的 proxy 设置
3. **缺省值**: `http://127.0.0.1:8118/`

**实施方案**:
```python
def get_proxy_config():
    # 1. 环境变量优先
    http_proxy = os.getenv('HTTP_PROXY')
    https_proxy = os.getenv('HTTPS_PROXY')
    
    if http_proxy and https_proxy:
        return {'http': http_proxy, 'https': https_proxy}
    
    # 2. 配置文件次之
    config_proxy = config.get('proxy', {})
    if config_proxy:
        return config_proxy
    
    # 3. 缺省值
    return {
        'http': 'http://127.0.0.1:8118/',
        'https': 'http://127.0.0.1:8118/'
    }

# 网络请求时使用
async def make_request(url: str):
    proxy_config = get_proxy_config()
    # httpx 使用 proxy 参数，不是 proxies
    proxy_url = proxy_config.get('https') or proxy_config.get('http')
    async with httpx.AsyncClient(proxy=proxy_url) as client:
        response = await client.get(url)
        return response
```

**理由**:
- 支持不同环境的灵活配置
- 环境变量便于容器化部署
- 配置文件便于本地开发
- 缺省值确保基本可用性

### 8. Sprint 1 测试覆盖范围

**决策**: **函数式API专项测试优先** - 重点测试@task装饰器和Send API机制

**测试重点**:
```python
# 函数式API专项测试
class TestFunctionalAPI:
    async def test_task_decorator_parallel_execution(self):
        # 测试@task装饰器的并行执行能力
        pass

    async def test_entrypoint_workflow_management(self):
        # 测试@entrypoint的工作流管理
        pass

    async def test_send_api_dynamic_dispatch(self):
        # 测试Send API的动态任务分发
        pass

    async def test_automatic_checkpointing(self):
        # 测试自动检查点机制
        pass

# BaseAgent函数式API测试
class TestBaseAgentFunctionalAPI:
    async def test_task_based_agent_execution(self):
        # 测试基于@task的智能体执行
        pass

    async def test_parallel_task_processing(self):
        # 测试并行任务处理能力
        pass

    async def test_performance_improvement(self):
        # 测试3-5倍性能提升目标
        pass

# 缓存系统与函数式API集成测试
class TestCacheSystemIntegration:
    async def test_cache_with_parallel_tasks(self):
        # 测试缓存系统与并行任务的集成
        pass

    async def test_cache_performance_with_concurrency(self):
        # 测试并发场景下的缓存性能
        pass
```

**测试策略**:
- **函数式API测试**: 50% 覆盖率，重点测试@task和Send API
- **性能测试**: 30% 覆盖率，验证3-5倍性能提升
- **集成测试**: 20% 覆盖率，测试组件协作

**理由**:
- 函数式API是Sprint 1的核心技术变更
- 必须验证性能提升目标的实现
- 确保@task装饰器和Send API正确工作

## 决策影响分析

### 正面影响
- **开发效率**: 明确的技术方案减少开发过程中的决策成本
- **代码质量**: 标准化的 BaseAgent 基类提高代码一致性
- **系统性能**: 二层缓存系统显著提升响应速度
- **用户体验**: 智能配置降低使用门槛

### 风险和挑战
- **学习成本**: 团队需要熟悉 LangGraph 0.6 的新特性
- **复杂性**: 二层缓存增加了系统复杂度
- **调试难度**: 异步执行可能增加调试难度

### 缓解措施
- 提供详细的技术文档和示例代码
- 建立完善的日志和监控系统
- 实施渐进式开发和测试策略

## 实施计划

### Week 1: 基础框架搭建
- [ ] 实现 BaseAgent 基类
- [ ] 搭建二层缓存系统
- [ ] 建立项目目录结构

### Week 2: 核心功能实现
- [ ] 实现 CLI 界面框架
- [ ] 集成 LangGraph 工作流引擎
- [ ] 完成配置管理系统

## 成功标准

### 技术指标
- [ ] BaseAgent 基类通过所有单元测试
- [ ] 缓存系统读写性能 < 100ms
- [ ] CLI 界面支持基本命令操作
- [ ] 配置系统支持零配置启动

### 质量指标
- [ ] 集成测试覆盖率 > 70%
- [ ] 代码通过静态类型检查
- [ ] 文档覆盖所有公共接口

## 决策记录

| 决策项 | 选择方案 | 决策日期 | 决策人 | 状态 |
|--------|----------|----------|--------|------|
| 多智能体框架 | **函数式API优先** - @task/@entrypoint | 2025-07-31 | 开发团队 | ✅ 已确认 |
| BaseAgent 设计 | **函数式API原生设计** - 完全基于@task | 2025-07-31 | 开发团队 | ✅ 已确认 |
| 缓存策略 | 按内容类型分类 + 并行优化 | 2025-07-31 | 开发团队 | ✅ 已确认 |
| TTL 策略 | 固定 24 小时 | 2025-07-31 | 开发团队 | ✅ 已确认 |
| 配置管理 | 分层智能配置 | 2025-07-31 | 开发团队 | ✅ 已确认 |
| CLI 实现 | 渐进式实现 + 函数式API集成 | 2025-07-31 | 开发团队 | ✅ 已确认 |
| 代理配置 | 环境变量优先 | 2025-07-31 | 开发团队 | ✅ 已确认 |
| 测试策略 | **函数式API专项测试优先** | 2025-07-31 | 开发团队 | ✅ 已确认 |

## 后续行动

### 立即行动
- [ ] 创建 BaseAgent 基类实现
- [ ] 搭建缓存系统原型
- [ ] 建立开发环境和工具链

### 持续监控
- [ ] 跟踪实施进度和质量指标
- [ ] 收集开发过程中的反馈
- [ ] 根据实际情况调整技术方案

---

*本决策记录遵循项目 `.augmentrules` 规范，保存在 `doc/dev/architecture/decisions/` 目录下。*
