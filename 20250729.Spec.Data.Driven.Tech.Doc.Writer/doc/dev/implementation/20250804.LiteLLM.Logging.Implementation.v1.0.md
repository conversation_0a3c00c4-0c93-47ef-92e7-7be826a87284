# LiteLLM 日志配置实现文档

## 概述

根据 `.augmentrules` 第167行要求，实现了 LiteLLM 的专用日志功能：

> "LittleLLM 需要遵循统一的日志级别，将日志保存到指定目录下，并以 little_llm.YYYYMMDD.log 为文件名将所有和 LLM 的交互以日志形式保存下来。"

## 实现特性

### ✅ 核心要求满足

1. **统一日志级别**: 遵循系统全局日志级别配置
2. **指定目录**: 日志保存在配置的目录下（默认 `./log`）
3. **标准文件名**: 使用 `little_llm.YYYYMMDD.log` 格式
4. **完整交互记录**: 记录所有 LLM 请求、响应和错误

### 🚀 增强功能

1. **JSON 格式**: 结构化日志便于解析和分析
2. **元数据记录**: 包含模型、提供商、耗时、token 数等信息
3. **自动轮转**: 按日期自动轮转，保留指定天数的历史日志
4. **错误处理**: 完整记录错误信息和上下文
5. **性能监控**: 记录请求耗时和响应大小

## 架构设计

### 模块结构

```
src/backend/llm/
├── providers.py          # LLM 提供商（集成日志调用）
├── logging_handler.py    # LiteLLM 专用日志处理器
└── __init__.py
```

### 配置结构

```yaml
logging:
  litellm:
    enabled: true                           # 启用 LiteLLM 日志
    level: "INFO"                          # 日志级别
    directory: "./log"                     # 日志目录
    filename_pattern: "little_llm.{date}.log"  # 文件名模式
    max_size_mb: 50                        # 单文件最大大小
    backup_count: 30                       # 保留天数
    include_request_response: true         # 包含请求响应内容
    include_metadata: true                 # 包含元数据
    json_format: true                      # JSON 格式
```

## 实现细节

### 1. 日志处理器 (`logging_handler.py`)

**核心类**: `LiteLLMLoggingHandler`

**主要功能**:
- 按日期创建日志文件
- JSON 格式化日志记录
- 自动轮转和清理
- 完整的交互信息记录

**日志记录格式**:
```json
{
  "timestamp": "2025-01-04T10:30:45.123456",
  "interaction_type": "response",
  "provider": "gemini",
  "model": "gemini/gemini-2.5-flash",
  "request": {
    "messages": [...],
    "message_count": 1,
    "total_chars": 25
  },
  "response": {
    "content": "人工智能是...",
    "char_count": 150
  },
  "metadata": {
    "duration_seconds": 2.34,
    "temperature": 0.1,
    "max_tokens": 100
  },
  "success": true
}
```

### 2. LLM 提供商集成 (`providers.py`)

**集成点**:
- 请求前记录: `log_llm_request()`
- 响应后记录: `log_llm_response()`
- 错误时记录: `log_llm_error()`

**时间测量**: 使用 `time.time()` 精确测量请求耗时

### 3. 配置管理 (`config.py`)

**新增配置模型**: `LiteLLMLoggingConfig`

**自动初始化**: 在配置加载时自动初始化日志处理器

## 使用方法

### 1. 配置启用

在 `config.yaml` 中添加：

```yaml
logging:
  litellm:
    enabled: true
    level: "INFO"
    directory: "./log"
    # 其他配置使用默认值
```

### 2. 自动记录

所有通过 `generate_llm_response()` 的调用都会自动记录日志，无需额外代码。

### 3. 手动记录

```python
from src.backend.llm.logging_handler import log_llm_request, log_llm_response

# 记录请求
log_llm_request("gemini", "gemini-2.5-flash", messages, temperature=0.1)

# 记录响应
log_llm_response("gemini", "gemini-2.5-flash", messages, response, 2.34)
```

## 日志文件示例

### 文件命名
```
log/
├── little_llm.20250104.log    # 今天的日志
├── little_llm.20250103.log    # 昨天的日志
├── little_llm.20250102.log    # 前天的日志
└── app.log                    # 系统日志
```

### 日志内容示例
```json
{"timestamp": "2025-01-04T10:30:45.123456", "interaction_type": "request", "provider": "gemini", "model": "gemini/gemini-2.5-flash", "request": {"messages": [{"role": "user", "content": "请简单介绍一下人工智能。"}], "message_count": 1, "total_chars": 15}, "metadata": {"temperature": 0.1, "max_tokens": 100, "timeout": 300}, "success": true}
{"timestamp": "2025-01-04T10:30:47.456789", "interaction_type": "response", "provider": "gemini", "model": "gemini/gemini-2.5-flash", "request": {"messages": [{"role": "user", "content": "请简单介绍一下人工智能。"}], "message_count": 1, "total_chars": 15}, "response": {"content": "人工智能（AI）是计算机科学的一个分支...", "char_count": 150}, "metadata": {"duration_seconds": 2.34, "temperature": 0.1, "max_tokens": 100}, "success": true}
```

## 测试验证

### 测试脚本
运行 `test/debug/test_litellm_logging.py` 验证功能：

```bash
cd 20250729.Spec.Data.Driven.Tech.Doc.Writer
python test/debug/test_litellm_logging.py
```

### 验证要点
1. ✅ 日志文件按 `little_llm.YYYYMMDD.log` 格式命名
2. ✅ 日志保存在指定目录
3. ✅ 记录完整的 LLM 交互信息
4. ✅ JSON 格式便于解析
5. ✅ 包含错误处理和元数据

## 性能考虑

### 异步安全
- 使用线程安全的日志记录器
- 避免阻塞主要业务流程

### 存储优化
- 自动轮转避免单文件过大
- 可配置保留天数控制存储空间
- JSON 格式压缩存储

### 性能影响
- 日志记录为异步操作，对 LLM 调用性能影响最小
- 可通过 `enabled: false` 完全禁用

## 故障排除

### 常见问题

1. **日志文件未生成**
   - 检查目录权限
   - 确认 `enabled: true`
   - 查看系统日志错误信息

2. **日志内容为空**
   - 检查日志级别配置
   - 确认 LLM 调用是否成功执行

3. **文件轮转异常**
   - 检查磁盘空间
   - 确认文件权限

### 调试方法
```python
# 检查日志处理器状态
from src.backend.llm.logging_handler import get_litellm_logging_handler
handler = get_litellm_logging_handler()
print(f"Handler enabled: {handler.enabled if handler else 'Not initialized'}")
```

## 扩展计划

### 未来增强
1. **日志分析工具**: 解析和统计 LLM 使用情况
2. **实时监控**: 集成到监控系统
3. **成本分析**: 基于 token 使用量计算成本
4. **性能优化**: 批量写入和压缩存储

### 集成计划
1. **Prometheus 指标**: 导出 LLM 使用指标
2. **ELK Stack**: 集成到日志分析平台
3. **告警系统**: 异常情况自动告警
