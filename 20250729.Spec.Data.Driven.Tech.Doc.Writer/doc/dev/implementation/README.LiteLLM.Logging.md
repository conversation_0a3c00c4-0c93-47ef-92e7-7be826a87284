# LiteLLM 日志配置实现

## 概述

本实现完全满足 `.augmentrules` 第167行的要求：

> "LittleLLM 需要遵循统一的日志级别，将日志保存到指定目录下，并以 little_llm.YYYYMMDD.log 为文件名将所有和 LLM 的交互以日志形式保存下来。"

## ✅ 实现验证

### 测试结果
```bash
$ python test/debug/test_litellm_logging.py
✅ 测试成功完成！
📋 验证要点:
  ✓ 日志文件按 little_llm.YYYYMMDD.log 格式命名
  ✓ 日志保存在指定目录 (./log)
  ✓ 记录了 LLM 交互的完整信息
  ✓ 支持 JSON 格式便于解析
```

### 日志文件示例
```
log/
├── little_llm.20250804.log    # LiteLLM 专用日志
└── app.log                    # 系统日志
```

### 日志内容示例
```json
{
  "timestamp": "2025-08-04T15:11:01.321426",
  "interaction_type": "response",
  "provider": "gemini",
  "model": "gemini/gemini-2.5-flash",
  "request": {
    "messages": [{"role": "user", "content": "请简单介绍一下人工智能。"}],
    "message_count": 1,
    "total_chars": 12
  },
  "response": {
    "content": "人工智能（AI）简单来说，就是...",
    "char_count": 225
  },
  "metadata": {
    "duration_seconds": 6.82,
    "temperature": 0.1,
    "max_tokens": 100
  },
  "success": true
}
```

## 🚀 核心功能

### 1. 自动日志记录
- **请求记录**: 记录所有 LLM 请求的详细信息
- **响应记录**: 记录完整的响应内容和元数据
- **错误记录**: 记录错误信息和上下文
- **性能监控**: 记录请求耗时和响应大小

### 2. 标准化文件命名
- **格式**: `little_llm.YYYYMMDD.log`
- **示例**: `little_llm.20250804.log`
- **自动轮转**: 按日期自动创建新文件

### 3. 结构化日志格式
- **JSON 格式**: 便于程序解析和分析
- **完整信息**: 包含请求、响应、元数据、错误信息
- **时间戳**: 精确到微秒的时间记录

### 4. 配置灵活性
```yaml
logging:
  litellm:
    enabled: true                    # 可开关
    level: "INFO"                   # 可配置日志级别
    directory: "./log"              # 可指定目录
    include_request_response: true  # 可控制内容详细程度
    json_format: true              # 可选择格式
```

## 📁 文件结构

### 新增文件
```
src/backend/llm/
├── logging_handler.py           # LiteLLM 专用日志处理器
└── providers.py                 # 集成日志记录功能

test/debug/
└── test_litellm_logging.py     # 功能测试脚本

script/
└── view_litellm_logs.py        # 日志查看工具

doc/dev/implementation/
├── 20250804.LiteLLM.Logging.Implementation.v1.0.md
└── README.LiteLLM.Logging.md   # 本文档
```

### 修改文件
```
config.yaml                      # 添加 LiteLLM 日志配置
config.yaml.example             # 添加配置示例
src/backend/config.py           # 添加配置模型和初始化
```

## 🛠️ 使用方法

### 1. 配置启用
在 `config.yaml` 中确保以下配置：
```yaml
logging:
  litellm:
    enabled: true
    level: "INFO"
    directory: "./log"
```

### 2. 自动记录
所有通过 `generate_llm_response()` 的调用都会自动记录，无需额外代码。

### 3. 查看日志
```bash
# 查看今天的日志
python script/view_litellm_logs.py

# 查看指定日期的日志
python script/view_litellm_logs.py --date 20250804

# 只显示摘要
python script/view_litellm_logs.py --summary-only

# 只显示错误
python script/view_litellm_logs.py --errors-only
```

### 4. 测试功能
```bash
python test/debug/test_litellm_logging.py
```

## 📊 日志分析示例

### 使用日志查看工具
```bash
$ python script/view_litellm_logs.py
📊 日志摘要
==================================================
总条目数: 6
请求数: 3
响应数: 1
错误数: 2
成功率: 33.3%

🤖 提供商统计:
  deepseek: 2 次
  gemini: 4 次

📱 模型统计:
  deepseek-chat: 2 次
  gemini/gemini-2.5-flash: 4 次
```

### 程序化分析
```python
import json
from pathlib import Path

# 读取日志文件
log_file = Path("log/little_llm.20250804.log")
entries = []

with open(log_file, 'r') as f:
    for line in f:
        if line.strip():
            entries.append(json.loads(line))

# 分析成功率
responses = [e for e in entries if e['interaction_type'] == 'response']
requests = [e for e in entries if e['interaction_type'] == 'request']
success_rate = len(responses) / len(requests) * 100

print(f"成功率: {success_rate:.1f}%")
```

## 🔧 技术实现

### 核心组件
1. **LiteLLMLoggingHandler**: 专用日志处理器
2. **LiteLLMLoggingConfig**: 配置模型
3. **日志集成**: 在 LLM 提供商中集成日志调用

### 关键特性
- **线程安全**: 使用标准库的日志处理器
- **异步友好**: 不阻塞主要业务流程
- **错误容错**: 日志失败不影响 LLM 调用
- **性能优化**: 最小化对 LLM 调用性能的影响

## 📈 扩展计划

### 短期增强
- [ ] 日志压缩存储
- [ ] 成本分析功能
- [ ] 实时监控集成

### 长期规划
- [ ] 日志分析仪表板
- [ ] 异常检测和告警
- [ ] 性能优化建议

## 🎯 符合性检查

### .augmentrules 要求对照

| 要求 | 实现状态 | 说明 |
|------|----------|------|
| 遵循统一的日志级别 | ✅ | 支持配置日志级别，与系统日志级别一致 |
| 保存到指定目录下 | ✅ | 可配置目录，默认 `./log` |
| little_llm.YYYYMMDD.log 文件名 | ✅ | 严格按照要求的格式命名 |
| 记录所有 LLM 交互 | ✅ | 记录请求、响应、错误的完整信息 |

### 额外价值
- **结构化格式**: JSON 格式便于分析
- **完整元数据**: 包含性能和配置信息
- **工具支持**: 提供查看和分析工具
- **测试验证**: 完整的测试覆盖

## 🎉 总结

本实现完全满足 `.augmentrules` 的要求，并提供了额外的增强功能：

1. **✅ 完全合规**: 严格按照要求实现文件命名和存储
2. **🚀 功能增强**: 提供结构化日志和分析工具
3. **🛠️ 易于使用**: 自动记录，无需额外代码
4. **📊 便于分析**: JSON 格式和查看工具
5. **🔧 高度可配置**: 支持灵活的配置选项

通过这个实现，项目现在具备了完整的 LLM 交互日志记录能力，为后续的分析、监控和优化提供了坚实的基础。
