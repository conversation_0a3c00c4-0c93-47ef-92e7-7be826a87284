# LangGraph 0.6.2 函数式API迁移指南

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **文档类型**: 实现指南
- **相关决策**: [ADR-003: LangGraph 0.6.2 函数式API优化集成决策](../architecture/decisions/20250731.ADR-003.langgraph.functional.api.optimization.v1.0.md)

## 概述

本指南详细说明如何将现有的多智能体系统从StateGraph模式迁移到LangGraph 0.6.2的函数式API，实现代码简化、性能优化和开发效率提升。

## 迁移策略

### 总体原则
1. **渐进式迁移**: 分模块逐步迁移，保持系统稳定性
2. **向后兼容**: 保留StateGraph实现作为备选方案
3. **性能优先**: 优先迁移性能收益最大的模块
4. **测试驱动**: 每个迁移步骤都有完整的测试覆盖

### 迁移优先级
1. **高优先级**: 信息检索模块（最大性能收益）
2. **中优先级**: 需求分析模块、文档编写模块
3. **低优先级**: 质量审核模块、发布管理模块

## 核心概念对比

### StateGraph vs 函数式API

| 特性 | StateGraph模式 | 函数式API模式 |
|------|---------------|---------------|
| 代码复杂度 | 高（需要显式状态管理） | 低（自动状态管理） |
| 并行支持 | 需要手动实现 | 天然支持 |
| 检查点 | 需要手动配置 | 自动处理 |
| 调试难度 | 较高 | 较低 |
| 学习曲线 | 陡峭 | 平缓 |

### 代码对比示例

**StateGraph模式（当前）**:
```python
from langgraph.graph import StateGraph, MessagesState, START, END

def research_agent_node(state: MessagesState):
    result = research_agent.invoke(state["messages"])
    return {"messages": state["messages"] + [result]}

def analysis_agent_node(state: MessagesState):
    result = analysis_agent.invoke(state["messages"])
    return {"messages": state["messages"] + [result]}

# 构建图
builder = StateGraph(MessagesState)
builder.add_node("research", research_agent_node)
builder.add_node("analysis", analysis_agent_node)
builder.add_edge(START, "research")
builder.add_edge("research", "analysis")
builder.add_edge("analysis", END)
workflow = builder.compile()
```

**函数式API模式（目标）**:
```python
from langgraph.func import entrypoint, task
from langgraph.checkpoint.memory import InMemorySaver

@task
def research_task(topic: str):
    return research_agent.invoke(topic)

@task
def analysis_task(research_result: str):
    return analysis_agent.invoke(research_result)

@entrypoint(checkpointer=InMemorySaver())
def workflow(topic: str):
    research_result = research_task(topic)
    analysis_result = analysis_task(research_result.result())
    return analysis_result.result()
```

## 详细迁移步骤

### 第一阶段：信息检索模块并行化

#### 1.1 当前实现分析
```python
# 当前串行实现
class InformationRetrievalAgent(BaseAgent):
    def execute(self, state):
        academic_results = self.retrieve_academic_sources(state.keywords)
        industry_results = self.retrieve_industry_reports(state.keywords)
        news_results = self.retrieve_news_articles(state.keywords)
        return combine_results(academic_results, industry_results, news_results)
```

#### 1.2 函数式API改造
```python
from langgraph.func import entrypoint, task
from typing import List

@task
def retrieve_academic_sources(keywords: List[str]):
    """并行检索学术数据库"""
    # arXiv, PubMed, IEEE Xplore, ACM Digital Library
    return academic_retrieval_service.search(keywords)

@task
def retrieve_industry_reports(keywords: List[str]):
    """并行检索行业报告"""
    # Gartner, McKinsey, BCG, Deloitte
    return industry_report_service.search(keywords)

@task
def retrieve_news_articles(keywords: List[str]):
    """并行检索新闻资讯"""
    # 技术媒体、行业新闻网站
    return news_service.search(keywords)

@task
def retrieve_government_docs(keywords: List[str]):
    """并行检索政府文档"""
    # 政策文件、白皮书、统计数据
    return government_doc_service.search(keywords)

@task
def retrieve_opensource_projects(keywords: List[str]):
    """并行检索开源项目"""
    # GitLab, Gitea项目文档
    return opensource_service.search(keywords)

@entrypoint()
def parallel_information_retrieval(keywords: List[str]):
    """并行信息检索工作流"""
    # 启动所有并行任务
    academic_future = retrieve_academic_sources(keywords)
    industry_future = retrieve_industry_reports(keywords)
    news_future = retrieve_news_articles(keywords)
    government_future = retrieve_government_docs(keywords)
    opensource_future = retrieve_opensource_projects(keywords)
    
    # 等待所有任务完成并收集结果
    return {
        "academic": academic_future.result(),
        "industry": industry_future.result(),
        "news": news_future.result(),
        "government": government_future.result(),
        "opensource": opensource_future.result()
    }
```

#### 1.3 性能优化配置
```python
# 配置并发限制和超时
@task(timeout=300, retry=3)
def retrieve_academic_sources(keywords: List[str]):
    """配置超时和重试的检索任务"""
    return academic_retrieval_service.search(keywords)

# 配置缓存
@entrypoint(checkpointer=InMemorySaver())
def cached_information_retrieval(keywords: List[str]):
    """支持缓存的信息检索"""
    cache_key = hash(tuple(sorted(keywords)))
    
    # 自动缓存检查和结果复用
    return parallel_information_retrieval(keywords)
```

### 第二阶段：需求分析模块改造

#### 2.1 工作流分解
```python
@task
def parse_user_input(user_request: str):
    """解析用户输入"""
    return requirement_parser.parse(user_request)

@task
def analyze_user_intent(parsed_request: dict):
    """分析用户意图"""
    return intent_analyzer.analyze(parsed_request)

@task
def generate_search_keywords(user_intent: dict):
    """生成搜索关键词"""
    return keyword_generator.generate(user_intent)

@task
def define_research_scope(user_intent: dict, keywords: List[str]):
    """定义调研范围"""
    return scope_definer.define(user_intent, keywords)

@entrypoint()
def requirement_analysis_workflow(user_request: str):
    """需求分析工作流"""
    parsed_request = parse_user_input(user_request)
    user_intent = analyze_user_intent(parsed_request.result())
    keywords = generate_search_keywords(user_intent.result())
    scope = define_research_scope(user_intent.result(), keywords.result())
    
    return {
        "parsed_request": parsed_request.result(),
        "user_intent": user_intent.result(),
        "keywords": keywords.result(),
        "scope": scope.result()
    }
```

### 第三阶段：任务调度Send API集成

#### 3.1 动态任务分发
```python
from langgraph.types import Send

@task
def execute_research_task(task_data: dict):
    """执行单个调研任务"""
    task_type = task_data["type"]
    task_params = task_data["params"]
    
    if task_type == "academic_research":
        return academic_research_agent.invoke(task_params)
    elif task_type == "industry_analysis":
        return industry_analysis_agent.invoke(task_params)
    elif task_type == "trend_analysis":
        return trend_analysis_agent.invoke(task_params)
    else:
        raise ValueError(f"Unknown task type: {task_type}")

def assign_research_tasks(research_plan: dict):
    """动态任务分发函数"""
    tasks = research_plan["tasks"]
    return [Send("execute_research_task", {"task_data": task}) for task in tasks]

@entrypoint()
def orchestrated_research_workflow(research_plan: dict):
    """协调者-执行者模式的研究工作流"""
    # 任务分解和依赖分析
    task_graph = analyze_task_dependencies(research_plan)
    
    # 按依赖层次执行任务
    results = {}
    for level in task_graph.levels:
        level_futures = []
        for task in level.tasks:
            future = execute_research_task(task)
            level_futures.append(future)
        
        # 等待当前层次所有任务完成
        level_results = [future.result() for future in level_futures]
        results[level.name] = level_results
    
    return aggregate_research_results(results)
```

## 测试策略

### 单元测试
```python
import pytest
from langgraph.func import entrypoint, task

@pytest.mark.asyncio
async def test_parallel_information_retrieval():
    """测试并行信息检索"""
    keywords = ["artificial intelligence", "machine learning"]
    
    # 执行并行检索
    result = await parallel_information_retrieval(keywords)
    
    # 验证结果结构
    assert "academic" in result
    assert "industry" in result
    assert "news" in result
    
    # 验证并行执行（检查执行时间）
    import time
    start_time = time.time()
    result = await parallel_information_retrieval(keywords)
    execution_time = time.time() - start_time
    
    # 并行执行应该比串行快
    assert execution_time < 60  # 假设串行需要更长时间
```

### 性能测试
```python
@pytest.mark.performance
async def test_performance_improvement():
    """测试性能提升"""
    keywords = ["blockchain", "cryptocurrency", "DeFi"]
    
    # 测试并行版本
    start_time = time.time()
    parallel_result = await parallel_information_retrieval(keywords)
    parallel_time = time.time() - start_time
    
    # 测试串行版本（用于对比）
    start_time = time.time()
    serial_result = await serial_information_retrieval(keywords)
    serial_time = time.time() - start_time
    
    # 验证性能提升
    improvement_ratio = serial_time / parallel_time
    assert improvement_ratio >= 3.0  # 至少3倍性能提升
```

## 监控和调试

### 执行监控
```python
from langgraph.func import entrypoint, task
import structlog

logger = structlog.get_logger()

@task
def monitored_task(data):
    """带监控的任务"""
    logger.info("Task started", task_name="monitored_task", data_size=len(data))
    
    try:
        result = process_data(data)
        logger.info("Task completed", task_name="monitored_task", result_size=len(result))
        return result
    except Exception as e:
        logger.error("Task failed", task_name="monitored_task", error=str(e))
        raise

@entrypoint()
def monitored_workflow(input_data):
    """带监控的工作流"""
    logger.info("Workflow started", workflow_name="monitored_workflow")
    
    try:
        result = monitored_task(input_data).result()
        logger.info("Workflow completed", workflow_name="monitored_workflow")
        return result
    except Exception as e:
        logger.error("Workflow failed", workflow_name="monitored_workflow", error=str(e))
        raise
```

### 性能分析
```python
import time
from functools import wraps

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info("Function executed", 
                       function_name=func.__name__, 
                       execution_time=execution_time)
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error("Function failed", 
                        function_name=func.__name__, 
                        execution_time=execution_time,
                        error=str(e))
            raise
    return wrapper

@task
@performance_monitor
async def performance_monitored_task(data):
    """带性能监控的任务"""
    return await process_data(data)
```

## 故障排除

### 常见问题和解决方案

1. **任务超时问题**
   ```python
   @task(timeout=600)  # 增加超时时间
   def long_running_task(data):
       return process_large_data(data)
   ```

2. **内存使用过高**
   ```python
   @task(max_memory="2GB")  # 限制内存使用
   def memory_intensive_task(data):
       return process_data_efficiently(data)
   ```

3. **并发限制**
   ```python
   @entrypoint(max_concurrency=5)  # 限制并发数
   def controlled_workflow(data_list):
       futures = [process_item(item) for item in data_list]
       return [f.result() for f in futures]
   ```

## 最佳实践

1. **任务粒度**: 保持任务足够小，便于并行和缓存
2. **错误处理**: 在每个任务中实现适当的错误处理
3. **资源管理**: 合理配置超时、重试和并发限制
4. **监控日志**: 添加详细的执行日志和性能监控
5. **测试覆盖**: 确保每个迁移的模块都有完整的测试

---

*本实现指南遵循项目 `.augmentrules` 规范，保存在 `doc/dev/implementation/` 目录下。*
