# 网页内容处理技术实现指南

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-08-04
- **适用范围**: Sprint 2 网页内容抓取和处理
- **技术栈**: httpx, readabilipy, markdownify
- **相关文档**:
  - [Sprint 2 规划文档](../sprints/planning/20250804.sprint2.planning.v1.0.md)
  - [搜索引擎集成指南](20250804.search.engine.integration.guide.v1.0.md)

## 1. 技术架构概述

### 1.1 网页内容处理流程
```
搜索结果 URL → 网页抓取 → HTML 清洁 → Markdown 转换 → 质量评估 → 缓存存储
     ↓              ↓           ↓            ↓           ↓           ↓
   URL 列表      原始 HTML    清洁 HTML    Markdown    质量评分    结构化存储
```

### 1.2 核心组件
- **网页抓取器**: 基于 httpx 的异步网页抓取
- **HTML 清洁器**: 基于 readabilipy 的内容清洁
- **Markdown 转换器**: 基于 markdownify 的格式转换
- **质量评估器**: 内容质量评估和过滤
- **缓存管理器**: URL 状态缓存和内容缓存

## 2. 网页抓取实现

### 2.1 技术要求
```python
# 依赖库版本要求
httpx>=0.27.0  # 支持代理配置和异步请求
aiofiles>=23.2.0  # 异步文件操作
```

### 2.2 实现步骤

#### 步骤 1: 获取最新文档
**强制要求**: 开发前必须使用 Context 7 获取 httpx 最新文档
```bash
# 查询 httpx 文档和最佳实践
# 重点关注：代理配置、异步请求、错误处理、超时设置
```

#### 步骤 2: 基础抓取器实现
```python
# src/backend/agents/web_scraper.py

import httpx
import asyncio
import structlog
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin, urlparse
from datetime import datetime

logger = structlog.get_logger(__name__)

class WebScraper:
    """网页抓取器"""
    
    def __init__(self, proxy_config: Optional[Dict[str, str]] = None):
        """
        Initialize web scraper with proxy configuration
        
        Args:
            proxy_config: Proxy configuration for network requests
        """
        self.proxy_config = proxy_config or {
            "http://": "http://127.0.0.1:8118/",
            "https://": "http://127.0.0.1:8118/"
        }
        
        # Configure httpx client with proxy
        self.client = httpx.AsyncClient(
            proxies=self.proxy_config,
            timeout=30.0,
            follow_redirects=True,
            headers={
                "User-Agent": "Mozilla/5.0 (compatible; TechDocBot/1.0)"
            }
        )
        
    async def fetch_content(self, url: str) -> Dict[str, Any]:
        """
        Fetch content from URL
        
        Args:
            url: Target URL to fetch
            
        Returns:
            Dictionary containing content and metadata
        """
        try:
            logger.info("Starting web content fetch", url=url)
            start_time = datetime.now()
            
            response = await self.client.get(url)
            response.raise_for_status()
            
            # Calculate fetch metrics
            fetch_time = (datetime.now() - start_time).total_seconds()
            content_size = len(response.content)
            
            # Log fetch metrics
            logger.info("Web content fetch completed",
                       url=url,
                       status_code=response.status_code,
                       content_size_bytes=content_size,
                       fetch_time_seconds=fetch_time)
            
            return {
                "url": url,
                "content": response.text,
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content_size": content_size,
                "fetch_time": fetch_time,
                "timestamp": datetime.now().isoformat()
            }
            
        except httpx.HTTPStatusError as e:
            logger.error("HTTP error during fetch",
                        url=url,
                        status_code=e.response.status_code,
                        error=str(e))
            raise
            
        except httpx.RequestError as e:
            logger.error("Request error during fetch",
                        url=url,
                        error=str(e))
            raise
            
        except Exception as e:
            logger.error("Unexpected error during fetch",
                        url=url,
                        error=str(e))
            raise
    
    async def fetch_multiple(self, urls: List[str], 
                           max_concurrent: int = 5) -> List[Dict[str, Any]]:
        """
        Fetch content from multiple URLs concurrently
        
        Args:
            urls: List of URLs to fetch
            max_concurrent: Maximum concurrent requests
            
        Returns:
            List of fetch results
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def fetch_with_semaphore(url: str) -> Dict[str, Any]:
            async with semaphore:
                try:
                    return await self.fetch_content(url)
                except Exception as e:
                    logger.error("Failed to fetch URL", url=url, error=str(e))
                    return {
                        "url": url,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
        
        tasks = [fetch_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful results
        successful_results = []
        for result in results:
            if isinstance(result, dict) and "content" in result:
                successful_results.append(result)
        
        logger.info("Batch fetch completed",
                   total_urls=len(urls),
                   successful_fetches=len(successful_results),
                   failed_fetches=len(urls) - len(successful_results))
        
        return successful_results
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
```

## 3. HTML 内容清洁

### 3.1 实现步骤

#### 步骤 1: 获取最新文档
**强制要求**: 使用 Context 7 获取 readabilipy 最新文档

#### 步骤 2: HTML 清洁器实现
```python
# src/backend/agents/html_cleaner.py

from readabilipy import simple_json_from_html_string
import structlog
from typing import Dict, Any, Optional

logger = structlog.get_logger(__name__)

class HTMLCleaner:
    """HTML 内容清洁器"""
    
    def __init__(self):
        """Initialize HTML cleaner"""
        pass
    
    def clean_html(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Clean HTML content using readabilipy
        
        Args:
            html_content: Raw HTML content
            url: Source URL for context
            
        Returns:
            Cleaned content with metadata
        """
        try:
            logger.info("Starting HTML cleaning", url=url)
            
            # Use readabilipy to extract main content
            article = simple_json_from_html_string(
                html_content,
                use_readability=True
            )
            
            if not article or not article.get("content"):
                logger.warning("No content extracted from HTML", url=url)
                return {
                    "url": url,
                    "title": "",
                    "content": "",
                    "text_content": "",
                    "success": False,
                    "error": "No content extracted"
                }
            
            # Extract key information
            title = article.get("title", "")
            content = article.get("content", "")
            text_content = article.get("plain_text", "")
            
            # Calculate content metrics
            content_length = len(text_content)
            word_count = len(text_content.split()) if text_content else 0
            
            logger.info("HTML cleaning completed",
                       url=url,
                       title=title[:50] + "..." if len(title) > 50 else title,
                       content_length=content_length,
                       word_count=word_count)
            
            return {
                "url": url,
                "title": title,
                "content": content,
                "text_content": text_content,
                "content_length": content_length,
                "word_count": word_count,
                "success": True
            }
            
        except Exception as e:
            logger.error("HTML cleaning failed",
                        url=url,
                        error=str(e))
            return {
                "url": url,
                "title": "",
                "content": "",
                "text_content": "",
                "success": False,
                "error": str(e)
            }
```

## 4. Markdown 转换

### 4.1 实现步骤

#### 步骤 1: 获取最新文档
**强制要求**: 使用 Context 7 获取 markdownify 最新文档

#### 步骤 2: Markdown 转换器实现
```python
# src/backend/agents/markdown_converter.py

from markdownify import markdownify as md
import structlog
from typing import Dict, Any
import re

logger = structlog.get_logger(__name__)

class MarkdownConverter:
    """Markdown 转换器"""
    
    def __init__(self):
        """Initialize Markdown converter"""
        self.conversion_options = {
            "heading_style": "ATX",  # Use # for headings
            "bullets": "-",          # Use - for bullet points
            "strip": ["script", "style"],  # Remove script and style tags
            "convert": ["p", "h1", "h2", "h3", "h4", "h5", "h6", 
                       "ul", "ol", "li", "a", "strong", "em", "code", "pre"]
        }
    
    def convert_to_markdown(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Convert HTML content to Markdown
        
        Args:
            html_content: Cleaned HTML content
            url: Source URL for context
            
        Returns:
            Markdown content with metadata
        """
        try:
            logger.info("Starting Markdown conversion", url=url)
            
            # Convert HTML to Markdown
            markdown_content = md(
                html_content,
                **self.conversion_options
            )
            
            # Clean up the markdown
            cleaned_markdown = self._clean_markdown(markdown_content)
            
            # Calculate metrics
            line_count = len(cleaned_markdown.split('\n'))
            word_count = len(cleaned_markdown.split())
            
            logger.info("Markdown conversion completed",
                       url=url,
                       line_count=line_count,
                       word_count=word_count)
            
            return {
                "url": url,
                "markdown_content": cleaned_markdown,
                "line_count": line_count,
                "word_count": word_count,
                "success": True
            }
            
        except Exception as e:
            logger.error("Markdown conversion failed",
                        url=url,
                        error=str(e))
            return {
                "url": url,
                "markdown_content": "",
                "success": False,
                "error": str(e)
            }
    
    def _clean_markdown(self, markdown: str) -> str:
        """
        Clean up markdown content
        
        Args:
            markdown: Raw markdown content
            
        Returns:
            Cleaned markdown content
        """
        # Remove excessive whitespace
        markdown = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown)
        
        # Remove empty links
        markdown = re.sub(r'\[\]\([^)]*\)', '', markdown)
        
        # Clean up list formatting
        markdown = re.sub(r'\n\s*-\s*\n', '\n', markdown)
        
        # Remove trailing whitespace
        lines = [line.rstrip() for line in markdown.split('\n')]
        markdown = '\n'.join(lines)
        
        return markdown.strip()
```

## 5. 内容质量评估

### 5.1 质量评估器实现
```python
# src/backend/agents/content_quality_assessor.py

import structlog
from typing import Dict, Any, List
import re

logger = structlog.get_logger(__name__)

class ContentQualityAssessor:
    """内容质量评估器"""
    
    def __init__(self):
        """Initialize content quality assessor"""
        self.min_word_count = 50
        self.min_content_length = 200
        self.spam_keywords = [
            "click here", "buy now", "limited time", "act now",
            "free trial", "subscribe", "newsletter"
        ]
    
    def assess_quality(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Assess content quality
        
        Args:
            content_data: Content data with text and metadata
            
        Returns:
            Quality assessment results
        """
        try:
            url = content_data.get("url", "")
            text_content = content_data.get("text_content", "")
            word_count = content_data.get("word_count", 0)
            
            logger.info("Starting content quality assessment", url=url)
            
            # Calculate quality metrics
            quality_score = 0.0
            quality_factors = []
            
            # Word count check
            if word_count >= self.min_word_count:
                quality_score += 30
                quality_factors.append("sufficient_word_count")
            
            # Content length check
            if len(text_content) >= self.min_content_length:
                quality_score += 20
                quality_factors.append("sufficient_content_length")
            
            # Spam detection
            spam_score = self._detect_spam(text_content)
            if spam_score < 0.3:  # Low spam score is good
                quality_score += 25
                quality_factors.append("low_spam_score")
            
            # Structure quality
            structure_score = self._assess_structure(text_content)
            quality_score += structure_score * 25
            if structure_score > 0.5:
                quality_factors.append("good_structure")
            
            # Determine if content passes quality threshold
            passes_quality = quality_score >= 60
            
            logger.info("Content quality assessment completed",
                       url=url,
                       quality_score=quality_score,
                       passes_quality=passes_quality,
                       quality_factors=quality_factors)
            
            return {
                "url": url,
                "quality_score": quality_score,
                "passes_quality": passes_quality,
                "quality_factors": quality_factors,
                "spam_score": spam_score,
                "structure_score": structure_score
            }
            
        except Exception as e:
            logger.error("Content quality assessment failed",
                        url=content_data.get("url", ""),
                        error=str(e))
            return {
                "url": content_data.get("url", ""),
                "quality_score": 0.0,
                "passes_quality": False,
                "error": str(e)
            }
    
    def _detect_spam(self, text: str) -> float:
        """
        Detect spam content
        
        Args:
            text: Text content to analyze
            
        Returns:
            Spam score (0.0 = no spam, 1.0 = high spam)
        """
        if not text:
            return 1.0
        
        text_lower = text.lower()
        spam_count = sum(1 for keyword in self.spam_keywords 
                        if keyword in text_lower)
        
        # Calculate spam ratio
        spam_ratio = spam_count / len(self.spam_keywords)
        
        # Check for excessive capitalization
        caps_ratio = sum(1 for c in text if c.isupper()) / len(text)
        if caps_ratio > 0.3:
            spam_ratio += 0.2
        
        return min(spam_ratio, 1.0)
    
    def _assess_structure(self, text: str) -> float:
        """
        Assess content structure quality
        
        Args:
            text: Text content to analyze
            
        Returns:
            Structure score (0.0 = poor, 1.0 = excellent)
        """
        if not text:
            return 0.0
        
        structure_score = 0.0
        
        # Check for paragraphs
        paragraphs = text.split('\n\n')
        if len(paragraphs) > 1:
            structure_score += 0.3
        
        # Check for sentences
        sentences = re.split(r'[.!?]+', text)
        if len(sentences) > 3:
            structure_score += 0.3
        
        # Check average sentence length
        avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)
        if 10 <= avg_sentence_length <= 30:
            structure_score += 0.4
        
        return min(structure_score, 1.0)
```

## 6. URL 缓存管理

### 6.1 缓存管理器实现
```python
# src/backend/agents/url_cache_manager.py

import json
import aiofiles
import structlog
from typing import Dict, Any, Set, Optional
from pathlib import Path
from datetime import datetime
import hashlib

logger = structlog.get_logger(__name__)

class URLCacheManager:
    """URL 缓存管理器"""
    
    def __init__(self, cache_dir: str = "./data/cache"):
        """
        Initialize URL cache manager
        
        Args:
            cache_dir: Directory for cache files
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.url_status_file = self.cache_dir / "url_status.json"
        self.processed_urls: Set[str] = set()
        
        # Load existing cache
        asyncio.create_task(self._load_cache())
    
    async def _load_cache(self):
        """Load existing URL cache"""
        try:
            if self.url_status_file.exists():
                async with aiofiles.open(self.url_status_file, 'r') as f:
                    content = await f.read()
                    cache_data = json.loads(content)
                    self.processed_urls = set(cache_data.get("processed_urls", []))
                    
                logger.info("URL cache loaded",
                           cached_urls_count=len(self.processed_urls))
        except Exception as e:
            logger.error("Failed to load URL cache", error=str(e))
            self.processed_urls = set()
    
    async def is_url_processed(self, url: str) -> bool:
        """
        Check if URL has been processed
        
        Args:
            url: URL to check
            
        Returns:
            True if URL has been processed
        """
        return url in self.processed_urls
    
    async def mark_url_processed(self, url: str, success: bool = True):
        """
        Mark URL as processed
        
        Args:
            url: URL to mark as processed
            success: Whether processing was successful
        """
        if success:
            self.processed_urls.add(url)
            await self._save_cache()
            
            logger.info("URL marked as processed", url=url)
    
    async def _save_cache(self):
        """Save URL cache to file"""
        try:
            cache_data = {
                "processed_urls": list(self.processed_urls),
                "last_updated": datetime.now().isoformat(),
                "total_count": len(self.processed_urls)
            }
            
            # Atomic write
            temp_file = self.url_status_file.with_suffix('.tmp')
            async with aiofiles.open(temp_file, 'w') as f:
                await f.write(json.dumps(cache_data, indent=2))
            
            # Move temp file to final location
            temp_file.replace(self.url_status_file)
            
            logger.debug("URL cache saved",
                        cached_urls_count=len(self.processed_urls))
            
        except Exception as e:
            logger.error("Failed to save URL cache", error=str(e))
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics
        
        Returns:
            Cache statistics
        """
        return {
            "total_processed_urls": len(self.processed_urls),
            "cache_file_exists": self.url_status_file.exists(),
            "cache_directory": str(self.cache_dir)
        }
```

## 7. 集成处理管道

### 7.1 内容处理管道
```python
# src/backend/agents/content_processor.py

from typing import List, Dict, Any
import asyncio
import structlog

logger = structlog.get_logger(__name__)

class ContentProcessor:
    """内容处理管道"""
    
    def __init__(self):
        """Initialize content processor"""
        self.web_scraper = WebScraper()
        self.html_cleaner = HTMLCleaner()
        self.markdown_converter = MarkdownConverter()
        self.quality_assessor = ContentQualityAssessor()
        self.cache_manager = URLCacheManager()
    
    async def process_urls(self, urls: List[str]) -> List[Dict[str, Any]]:
        """
        Process multiple URLs through complete pipeline
        
        Args:
            urls: List of URLs to process
            
        Returns:
            List of processed content results
        """
        # Filter out already processed URLs
        unprocessed_urls = []
        for url in urls:
            if not await self.cache_manager.is_url_processed(url):
                unprocessed_urls.append(url)
        
        logger.info("Content processing started",
                   total_urls=len(urls),
                   unprocessed_urls=len(unprocessed_urls),
                   cached_urls=len(urls) - len(unprocessed_urls))
        
        if not unprocessed_urls:
            return []
        
        # Step 1: Fetch web content
        fetch_results = await self.web_scraper.fetch_multiple(unprocessed_urls)
        
        # Step 2: Process each fetched content
        processed_results = []
        for fetch_result in fetch_results:
            if "content" not in fetch_result:
                continue
                
            url = fetch_result["url"]
            
            try:
                # Clean HTML
                clean_result = self.html_cleaner.clean_html(
                    fetch_result["content"], url
                )
                
                if not clean_result["success"]:
                    continue
                
                # Convert to Markdown
                markdown_result = self.markdown_converter.convert_to_markdown(
                    clean_result["content"], url
                )
                
                if not markdown_result["success"]:
                    continue
                
                # Assess quality
                quality_result = self.quality_assessor.assess_quality(clean_result)
                
                # Combine results
                final_result = {
                    **fetch_result,
                    **clean_result,
                    **markdown_result,
                    **quality_result
                }
                
                # Mark as processed if quality passes
                if quality_result["passes_quality"]:
                    await self.cache_manager.mark_url_processed(url, True)
                    processed_results.append(final_result)
                else:
                    logger.info("Content filtered due to low quality",
                               url=url,
                               quality_score=quality_result["quality_score"])
                
            except Exception as e:
                logger.error("Content processing failed for URL",
                           url=url,
                           error=str(e))
                continue
        
        logger.info("Content processing completed",
                   processed_count=len(processed_results),
                   total_input_urls=len(urls))
        
        return processed_results
    
    async def cleanup(self):
        """Cleanup resources"""
        await self.web_scraper.close()
```

---

*本技术指南遵循项目 `.augmentrules` 规范，确保高质量的网页内容处理实现。*
