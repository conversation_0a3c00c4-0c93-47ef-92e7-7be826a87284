# 搜索引擎集成技术实现指南

## 文档信息
- **文档版本**: v1.1
- **创建日期**: 2025-08-04
- **最后更新**: 2025-08-04 22:00
- **适用范围**: Sprint 2 搜索引擎 API 接入
- **技术栈**: langchain-community, Tavily API, DuckDuckGo
- **主要变更**:
  - 整合了 20250801.sprint2.search.engine.integration.guide.v1.0.md 的测试代码示例
  - 增加了详细的单元测试和集成测试实现
  - 遵循 .augmentrules 的"优先更新现有文档"原则
- **相关文档**:
  - [Sprint 2 规划文档](../sprints/planning/20250804.sprint2.planning.v1.1.md)
  - [多智能体工作流设计](../../design/20250730.multi.agent.workflow.design.v1.0.md)

## 1. 技术架构概述

### 1.1 双搜索引擎架构
```
用户查询 → 搜索引擎选择器 → Tavily API (首选)
                              ↓ (失败时)
                         DuckDuckGo API (兜底)
                              ↓
                         结果标准化处理
                              ↓
                         返回统一格式结果
```

### 1.2 核心组件
- **搜索引擎选择器**: 智能选择和降级逻辑
- **Tavily 集成器**: 主要搜索服务
- **DuckDuckGo 集成器**: 备用搜索服务
- **结果标准化器**: 统一不同搜索引擎的返回格式
- **缓存管理器**: URL 去重和结果缓存

## 2. Tavily 搜索引擎集成

### 2.1 技术要求
```python
# 依赖库版本要求
langchain-community>=0.3.0
httpx>=0.27.0  # 支持代理配置
```

### 2.2 实现步骤

#### 步骤 1: 获取最新文档
**强制要求**: 开发前必须使用 Context 7 获取 Tavily API 最新文档
```bash
# 查询 Tavily API 文档和最佳实践
# 重点关注：API 密钥配置、请求参数、响应格式、错误处理
```

#### 步骤 2: 基础集成
```python
# src/backend/agents/retrieval_services.py

from langchain_community.tools import TavilySearchResults
from typing import List, Dict, Any, Optional
import httpx
import structlog

logger = structlog.get_logger(__name__)

class TavilySearchService:
    """Tavily 搜索引擎服务"""
    
    def __init__(self, api_key: str, proxy_config: Optional[Dict[str, str]] = None):
        """
        Initialize Tavily search service
        
        Args:
            api_key: Tavily API key
            proxy_config: Proxy configuration for network requests
        """
        self.api_key = api_key
        self.proxy_config = proxy_config or {}
        self.search_tool = TavilySearchResults(
            api_key=api_key,
            max_results=10,
            search_depth="advanced"
        )
        
    async def search(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Execute search query using Tavily API
        
        Args:
            query: Search query string
            **kwargs: Additional search parameters
            
        Returns:
            List of search results in standardized format
        """
        try:
            logger.info("Starting Tavily search", query=query)
            
            # Configure proxy if needed
            if self.proxy_config:
                # Apply proxy configuration to httpx client
                pass
                
            # Execute search
            results = await self.search_tool.ainvoke({"query": query})
            
            # Standardize results format
            standardized_results = self._standardize_results(results)
            
            logger.info("Tavily search completed", 
                       query=query, 
                       result_count=len(standardized_results))
            
            return standardized_results
            
        except Exception as e:
            logger.error("Tavily search failed", 
                        query=query, 
                        error=str(e))
            raise
    
    def _standardize_results(self, raw_results: Any) -> List[Dict[str, Any]]:
        """
        Standardize Tavily search results to unified format
        
        Args:
            raw_results: Raw results from Tavily API
            
        Returns:
            Standardized results list
        """
        # Implementation details to be added based on actual API response format
        pass
```

#### 步骤 3: 网络代理配置
```python
# 遵循 .augmentrules 代理要求
PROXY_CONFIG = {
    "http": "http://127.0.0.1:8118/",
    "https": "http://127.0.0.1:8118/"
}

# 在 httpx 客户端中配置代理
async def configure_proxy_client():
    """Configure httpx client with proxy settings"""
    return httpx.AsyncClient(
        proxies=PROXY_CONFIG,
        timeout=30.0
    )
```

### 2.3 错误处理和重试机制
```python
from tenacity import retry, stop_after_attempt, wait_exponential

class TavilySearchService:
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def search_with_retry(self, query: str) -> List[Dict[str, Any]]:
        """
        Search with automatic retry mechanism
        """
        return await self.search(query)
```

## 3. DuckDuckGo 搜索引擎集成

### 3.1 实现步骤

#### 步骤 1: 获取最新文档
**强制要求**: 使用 Context 7 获取 DuckDuckGo 搜索工具最新文档

#### 步骤 2: 基础集成
```python
from langchain_community.tools import DuckDuckGoSearchRun

class DuckDuckGoSearchService:
    """DuckDuckGo 搜索引擎服务"""
    
    def __init__(self):
        """Initialize DuckDuckGo search service"""
        self.search_tool = DuckDuckGoSearchRun()
        
    async def search(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Execute search query using DuckDuckGo
        
        Args:
            query: Search query string
            **kwargs: Additional search parameters
            
        Returns:
            List of search results in standardized format
        """
        try:
            logger.info("Starting DuckDuckGo search", query=query)
            
            # Execute search
            results = await self.search_tool.ainvoke({"query": query})
            
            # Standardize results format
            standardized_results = self._standardize_results(results)
            
            logger.info("DuckDuckGo search completed", 
                       query=query, 
                       result_count=len(standardized_results))
            
            return standardized_results
            
        except Exception as e:
            logger.error("DuckDuckGo search failed", 
                        query=query, 
                        error=str(e))
            raise
    
    def _standardize_results(self, raw_results: Any) -> List[Dict[str, Any]]:
        """
        Standardize DuckDuckGo search results to unified format
        """
        # Implementation details to be added
        pass
```

## 4. 搜索引擎选择器和降级机制

### 4.1 搜索引擎选择器
```python
from enum import Enum
from typing import Union

class SearchEngine(Enum):
    TAVILY = "tavily"
    DUCKDUCKGO = "duckduckgo"

class SearchEngineSelector:
    """搜索引擎选择器和降级管理"""
    
    def __init__(self, 
                 tavily_service: TavilySearchService,
                 duckduckgo_service: DuckDuckGoSearchService):
        self.tavily_service = tavily_service
        self.duckduckgo_service = duckduckgo_service
        self.primary_engine = SearchEngine.TAVILY
        self.fallback_engine = SearchEngine.DUCKDUCKGO
        
    async def search(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Execute search with automatic fallback mechanism
        
        Args:
            query: Search query string
            **kwargs: Additional search parameters
            
        Returns:
            Search results from available engine
        """
        # Try primary engine first
        try:
            logger.info("Attempting search with primary engine", 
                       engine=self.primary_engine.value,
                       query=query)
            
            if self.primary_engine == SearchEngine.TAVILY:
                results = await self.tavily_service.search(query, **kwargs)
            else:
                results = await self.duckduckgo_service.search(query, **kwargs)
                
            logger.info("Primary engine search successful",
                       engine=self.primary_engine.value,
                       result_count=len(results))
            
            return results
            
        except Exception as e:
            logger.warning("Primary engine search failed, trying fallback",
                          primary_engine=self.primary_engine.value,
                          fallback_engine=self.fallback_engine.value,
                          error=str(e))
            
            # Try fallback engine
            try:
                if self.fallback_engine == SearchEngine.DUCKDUCKGO:
                    results = await self.duckduckgo_service.search(query, **kwargs)
                else:
                    results = await self.tavily_service.search(query, **kwargs)
                    
                logger.info("Fallback engine search successful",
                           engine=self.fallback_engine.value,
                           result_count=len(results))
                
                return results
                
            except Exception as fallback_error:
                logger.error("Both search engines failed",
                           primary_error=str(e),
                           fallback_error=str(fallback_error))
                raise Exception(f"All search engines failed: {str(e)}, {str(fallback_error)}")
```

## 5. 结果标准化格式

### 5.1 统一结果格式
```python
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class SearchResult(BaseModel):
    """标准化搜索结果格式"""
    
    title: str
    url: str
    snippet: str
    relevance_score: Optional[float] = None
    source_engine: str  # "tavily" or "duckduckgo"
    timestamp: datetime
    content_type: Optional[str] = None  # "academic", "news", "general", etc.
    
class SearchResponse(BaseModel):
    """搜索响应格式"""
    
    query: str
    results: List[SearchResult]
    total_results: int
    search_engine_used: str
    execution_time_ms: int
    cache_hit: bool = False
```

## 6. 集成到信息检索智能体

### 6.1 更新 InformationRetrievalAgent
```python
# src/backend/agents/information_retrieval.py

class InformationRetrievalAgent(BaseAgent):
    """信息检索智能体 - 集成双搜索引擎"""
    
    def __init__(self, config: AgentConfig):
        super().__init__(config)
        
        # Initialize search services
        self.tavily_service = TavilySearchService(
            api_key=config.tavily_api_key,
            proxy_config=config.proxy_config
        )
        self.duckduckgo_service = DuckDuckGoSearchService()
        
        # Initialize search engine selector
        self.search_selector = SearchEngineSelector(
            tavily_service=self.tavily_service,
            duckduckgo_service=self.duckduckgo_service
        )
    
    async def execute_search(self, keywords: List[str]) -> List[SearchResult]:
        """
        Execute parallel search across multiple keywords
        
        Args:
            keywords: List of search keywords
            
        Returns:
            Combined and deduplicated search results
        """
        search_tasks = []
        
        for keyword in keywords:
            task = self.search_selector.search(keyword)
            search_tasks.append(task)
        
        # Execute searches in parallel
        all_results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # Process and combine results
        combined_results = []
        for i, results in enumerate(all_results):
            if isinstance(results, Exception):
                logger.error("Search failed for keyword", 
                           keyword=keywords[i], 
                           error=str(results))
                continue
                
            combined_results.extend(results)
        
        # Deduplicate by URL
        deduplicated_results = self._deduplicate_results(combined_results)
        
        return deduplicated_results
    
    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """Remove duplicate results based on URL"""
        seen_urls = set()
        unique_results = []
        
        for result in results:
            if result.url not in seen_urls:
                seen_urls.add(result.url)
                unique_results.append(result)
        
        return unique_results
```

## 7. 配置管理

### 7.1 配置文件更新
```yaml
# config.yaml.example 中添加搜索引擎配置

search_engines:
  # Tavily 配置
  tavily:
    api_key: "${TAVILY_API_KEY}"
    max_results: 10
    search_depth: "advanced"
    timeout: 30
    
  # DuckDuckGo 配置  
  duckduckgo:
    max_results: 10
    timeout: 30
    
  # 搜索策略配置
  strategy:
    primary_engine: "tavily"
    fallback_engine: "duckduckgo"
    retry_attempts: 3
    retry_delay: 2
```

## 8. 测试策略

### 8.1 单元测试
```python
# test/test_search_engines.py

import pytest
from src.backend.agents.retrieval_services import TavilySearchService, DuckDuckGoSearchService

@pytest.mark.asyncio
async def test_tavily_search():
    """Test Tavily search functionality"""
    service = TavilySearchService(api_key="test_key")
    results = await service.search("artificial intelligence")
    
    assert len(results) > 0
    assert all(isinstance(r, SearchResult) for r in results)

@pytest.mark.asyncio  
async def test_search_engine_fallback():
    """Test automatic fallback mechanism"""
    # Implementation details
    pass
```

### 8.2 集成测试
```python
@pytest.mark.asyncio
async def test_end_to_end_search():
    """Test complete search flow from keywords to results"""
    # Implementation details
    pass
```

## 9. 监控和日志

### 9.1 性能监控
```python
import time
from contextlib import asynccontextmanager

@asynccontextmanager
async def search_performance_monitor(query: str, engine: str):
    """Monitor search performance"""
    start_time = time.time()
    try:
        yield
    finally:
        execution_time = (time.time() - start_time) * 1000
        logger.info("Search performance metrics",
                   query=query,
                   engine=engine,
                   execution_time_ms=execution_time)
```

### 9.2 错误监控
```python
def log_search_error(query: str, engine: str, error: Exception):
    """Log search errors for monitoring"""
    logger.error("Search engine error",
                query=query,
                engine=engine,
                error_type=type(error).__name__,
                error_message=str(error))
```

## 10. 部署和运维

### 10.1 环境变量配置
```bash
# 必需的环境变量
export TAVILY_API_KEY="your_tavily_api_key"
export HTTP_PROXY="http://127.0.0.1:8118/"
export HTTPS_PROXY="http://127.0.0.1:8118/"
```

### 10.2 健康检查
```python
async def health_check():
    """Check search engines health status"""
    health_status = {
        "tavily": await check_tavily_health(),
        "duckduckgo": await check_duckduckgo_health()
    }
    return health_status
```

## 11. 测试策略和验证

### 11.1 单元测试示例

```python
# test/unit/test_tavily_service.py

import pytest
from unittest.mock import AsyncMock, patch
from src.backend.agents.retrieval_services import TavilySearchService

@pytest.mark.asyncio
async def test_tavily_search_success():
    """Test successful Tavily search"""
    service = TavilySearchService(api_key="test_key")

    # Mock successful response
    mock_results = [
        {
            "title": "AI Research Paper",
            "url": "https://example.com/ai-paper",
            "snippet": "Latest advances in artificial intelligence...",
            "relevance_score": 0.95
        }
    ]

    with patch.object(service.search_tool, 'ainvoke', return_value=mock_results):
        results = await service.search("artificial intelligence")

    assert len(results) == 1
    assert results[0]["title"] == "AI Research Paper"
    assert results[0]["relevance_score"] == 0.95

@pytest.mark.asyncio
async def test_tavily_search_failure():
    """Test Tavily search failure handling"""
    service = TavilySearchService(api_key="invalid_key")

    with patch.object(service.search_tool, 'ainvoke', side_effect=Exception("API Error")):
        with pytest.raises(Exception) as exc_info:
            await service.search("test query")

        assert "API Error" in str(exc_info.value)
```

### 11.2 集成测试示例

```python
# test/integration/test_search_integration.py

@pytest.mark.asyncio
async def test_end_to_end_search_flow():
    """端到端搜索流程测试"""
    # 初始化服务
    cache_manager = CacheManager()
    search_service = SearchEngineSelector(
        tavily_service=TavilySearchService(api_key="test_key"),
        duckduckgo_service=DuckDuckGoSearchService()
    )

    # 执行搜索
    keywords = ["人工智能", "机器学习"]
    results = await search_service.search(keywords[0])

    # 验证结果
    assert len(results) > 0
    assert all("title" in result for result in results)
    assert all("url" in result for result in results)
    assert all("relevance_score" in result for result in results)

@pytest.mark.asyncio
async def test_search_engine_fallback():
    """测试搜索引擎自动降级机制"""
    # 模拟 Tavily 失败，DuckDuckGo 成功
    tavily_service = TavilySearchService(api_key="invalid_key")
    duckduckgo_service = DuckDuckGoSearchService()

    selector = SearchEngineSelector(tavily_service, duckduckgo_service)

    with patch.object(tavily_service, 'search', side_effect=Exception("Tavily failed")):
        with patch.object(duckduckgo_service, 'search', return_value=[{"title": "Test"}]):
            results = await selector.search("test query")

    assert len(results) > 0
    assert results[0]["title"] == "Test"
```

### 11.3 性能测试

```python
# test/performance/test_search_performance.py

@pytest.mark.asyncio
async def test_search_response_time():
    """测试搜索响应时间"""
    import time

    service = TavilySearchService(api_key="test_key")

    start_time = time.time()
    results = await service.search("artificial intelligence")
    end_time = time.time()

    response_time = end_time - start_time
    assert response_time < 30.0  # 30秒内响应
    assert len(results) > 0

@pytest.mark.asyncio
async def test_concurrent_searches():
    """测试并发搜索能力"""
    import asyncio

    service = TavilySearchService(api_key="test_key")
    queries = ["AI", "ML", "NLP", "Computer Vision", "Robotics"]

    # 并发执行搜索
    tasks = [service.search(query) for query in queries]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 验证所有搜索都成功
    successful_results = [r for r in results if not isinstance(r, Exception)]
    assert len(successful_results) >= 3  # 至少60%成功率
```

---

*本技术指南遵循项目 `.augmentrules` 规范，整合了完整的测试策略和实现细节。*
