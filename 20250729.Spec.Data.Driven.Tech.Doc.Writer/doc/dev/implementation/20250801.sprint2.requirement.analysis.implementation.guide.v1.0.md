# Sprint 2 需求分析阶段实施指南

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-08-01
- **适用范围**: Sprint 2 需求分析阶段完整流程实现
- **业务重点**: 实现设计文档 2.2 需求分析阶段的 7 个步骤
- **相关文档**:
  - [多智能体工作流设计 2.2 需求分析阶段](../../design/20250730.multi.agent.workflow.design.v1.0.md#22-需求分析阶段)
  - [Sprint 2 规划](../sprints/planning/20250801.sprint2.planning.v1.0.md)

## 需求分析阶段业务流程

### 流程概览
```
用户输入 → 意图分析 → 关键词生成 → 初步调研 → 信息收集 → 覆盖度评估 → 提纲制定 → 行动规划
```

### 7 个核心步骤
1. **获得用户输入** (已实现)
2. **用户意图分析和需求拆解** (需完善)
3. **初步调研** (需实现真实搜索)
4. **收集原始信息** (需实现网页抓取)
5. **评估信息覆盖度** (需新实现)
6. **制定调研报告提纲** (需完善)
7. **制定调研行动规划** (需新实现)

## 核心实现

### 1. 初步调研功能实现

```python
# src/backend/agents/requirement_analyst/preliminary_research.py (新文件)

from typing import List, Dict, Any, Tuple
import structlog
from src.backend.models.request import SearchKeywords, RequirementAnalysis
from src.backend.agents.retrieval_services import RealSearchRetrievalService
from src.backend.models.cache import CacheManager

logger = structlog.get_logger(__name__)

class PreliminaryResearchService:
    """初步调研服务 - 实现设计文档 2.2.3 步骤3"""
    
    def __init__(self, cache_manager: CacheManager):
        self.search_service = RealSearchRetrievalService(cache_manager)
        self.max_sources_per_category = 5
        
    async def initialize(self) -> None:
        """初始化服务"""
        await self.search_service.initialize()
        
    async def conduct_preliminary_research(
        self, 
        keywords: SearchKeywords
    ) -> Dict[str, Any]:
        """
        执行初步调研
        
        输入：搜索关键词列表
        输出：相关信息摘要、信息源清单、调研范围界定
        """
        try:
            logger.info("Starting preliminary research", keywords_count=len(keywords.primary_keywords))
            
            # 1. 执行多维度搜索
            search_results = await self._execute_multi_dimensional_search(keywords)
            
            # 2. 分类信息源
            categorized_sources = self._categorize_information_sources(search_results)
            
            # 3. 生成信息摘要
            information_summary = await self._generate_information_summary(categorized_sources)
            
            # 4. 创建信息源清单（APA格式）
            source_list = self._create_apa_source_list(categorized_sources)
            
            # 5. 界定调研范围
            research_scope = self._define_research_scope(information_summary, keywords)
            
            result = {
                "information_summary": information_summary,
                "source_list": source_list,
                "research_scope": research_scope,
                "categorized_sources": categorized_sources,
                "total_sources": sum(len(sources) for sources in categorized_sources.values()),
                "research_timestamp": datetime.utcnow().isoformat()
            }
            
            logger.info(
                "Preliminary research completed",
                total_sources=result["total_sources"],
                categories=list(categorized_sources.keys())
            )
            
            return result
            
        except Exception as e:
            logger.error("Preliminary research failed", error=str(e))
            raise
    
    async def _execute_multi_dimensional_search(
        self, 
        keywords: SearchKeywords
    ) -> List[Dict[str, Any]]:
        """执行多维度搜索"""
        all_results = []
        
        # 主要关键词搜索
        if keywords.primary_keywords:
            primary_results = await self.search_service.search(keywords.primary_keywords)
            all_results.extend(primary_results)
        
        # 技术术语搜索
        if keywords.technical_terms:
            tech_results = await self.search_service.search(keywords.technical_terms[:3])
            all_results.extend(tech_results)
        
        # 英文关键词搜索（如果有）
        if keywords.english_keywords:
            english_results = await self.search_service.search(keywords.english_keywords[:3])
            all_results.extend(english_results)
        
        # 去重
        unique_results = self._deduplicate_search_results(all_results)
        return unique_results
    
    def _categorize_information_sources(
        self, 
        search_results: List[Dict[str, Any]]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """分类信息源"""
        categories = {
            "学术文献": [],
            "行业报告": [],
            "新闻资讯": [],
            "政府文档": [],
            "技术文档": [],
            "其他资源": []
        }
        
        for result in search_results:
            url = result.get("url", "").lower()
            title = result.get("title", "").lower()
            content = result.get("content", "").lower()
            
            # 分类逻辑
            if any(domain in url for domain in ["arxiv.org", "ieee.org", "acm.org", "scholar.google"]):
                categories["学术文献"].append(result)
            elif any(keyword in title + content for keyword in ["报告", "report", "白皮书", "whitepaper"]):
                categories["行业报告"].append(result)
            elif any(domain in url for domain in ["news", "新闻", "财经", "tech"]):
                categories["新闻资讯"].append(result)
            elif any(domain in url for domain in [".gov", "政府", "ministry"]):
                categories["政府文档"].append(result)
            elif any(keyword in title + content for keyword in ["api", "文档", "documentation", "技术", "开发"]):
                categories["技术文档"].append(result)
            else:
                categories["其他资源"].append(result)
        
        # 限制每个类别的数量
        for category in categories:
            categories[category] = categories[category][:self.max_sources_per_category]
        
        return categories
    
    async def _generate_information_summary(
        self, 
        categorized_sources: Dict[str, List[Dict[str, Any]]]
    ) -> Dict[str, str]:
        """生成信息摘要"""
        summaries = {}
        
        for category, sources in categorized_sources.items():
            if not sources:
                continue
                
            # 提取关键信息
            key_points = []
            for source in sources:
                title = source.get("title", "")
                content = source.get("content", "")
                
                # 提取关键句子（简化版）
                if content:
                    sentences = content.split('。')[:3]  # 取前3句
                    key_points.extend([s.strip() for s in sentences if len(s.strip()) > 10])
            
            # 生成摘要
            if key_points:
                summary = f"{category}共找到{len(sources)}个相关资源。主要观点包括：" + \
                         "；".join(key_points[:5]) + "。"
                summaries[category] = summary
        
        return summaries
    
    def _create_apa_source_list(
        self, 
        categorized_sources: Dict[str, List[Dict[str, Any]]]
    ) -> List[str]:
        """创建APA第七版格式的信息源清单"""
        apa_sources = []
        
        for category, sources in categorized_sources.items():
            for source in sources:
                apa_citation = self._format_apa_citation(source)
                if apa_citation:
                    apa_sources.append(apa_citation)
        
        return sorted(apa_sources)  # 按字母顺序排序
    
    def _format_apa_citation(self, source: Dict[str, Any]) -> str:
        """格式化APA引用"""
        try:
            title = source.get("title", "无标题")
            url = source.get("url", "")
            
            # 提取域名作为发布者
            if url:
                from urllib.parse import urlparse
                domain = urlparse(url).netloc
                publisher = domain.replace("www.", "").split(".")[0].title()
            else:
                publisher = "未知发布者"
            
            # 获取当前日期
            current_date = datetime.now().strftime("%Y年%m月%d日")
            
            # APA格式：作者. (年份). 标题. 发布者. URL
            apa_citation = f"{publisher}. ({current_date}). {title}. 检索自 {url}"
            
            return apa_citation
            
        except Exception as e:
            logger.warning("Failed to format APA citation", error=str(e))
            return f"引用格式错误: {source.get('title', '未知')}"
    
    def _define_research_scope(
        self, 
        information_summary: Dict[str, str], 
        keywords: SearchKeywords
    ) -> str:
        """界定调研范围"""
        scope_elements = []
        
        # 基于信息源类别确定范围
        if "学术文献" in information_summary:
            scope_elements.append("理论基础和学术研究")
        if "行业报告" in information_summary:
            scope_elements.append("行业现状和市场趋势")
        if "技术文档" in information_summary:
            scope_elements.append("技术实现和应用案例")
        if "政府文档" in information_summary:
            scope_elements.append("政策法规和标准规范")
        
        # 基于关键词确定深度
        if len(keywords.technical_terms) > 5:
            depth = "深入技术分析"
        elif len(keywords.primary_keywords) > 3:
            depth = "全面调研"
        else:
            depth = "概览性调研"
        
        scope = f"本次调研范围涵盖：{', '.join(scope_elements)}。调研深度：{depth}。"
        return scope
    
    def _deduplicate_search_results(
        self, 
        results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """搜索结果去重"""
        seen_urls = set()
        unique_results = []
        
        for result in results:
            url = result.get("url", "")
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
            elif not url:  # 保留没有URL的结果
                unique_results.append(result)
        
        return unique_results
```

### 2. 信息覆盖度评估

```python
# src/backend/agents/requirement_analyst/coverage_assessment.py (新文件)

from typing import Dict, Any, List, Tuple
import structlog
from src.backend.models.request import RequirementAnalysis

logger = structlog.get_logger(__name__)

class CoverageAssessmentService:
    """信息覆盖度评估服务 - 实现设计文档 2.2.3 步骤5"""
    
    def __init__(self):
        self.coverage_threshold = 0.7  # 覆盖度阈值
        
    async def assess_information_coverage(
        self,
        requirement_analysis: RequirementAnalysis,
        preliminary_research: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        评估信息覆盖度
        
        输入：用户意图、具体需求清单、相关信息摘要、调研范围界定、原始信息文件集合
        输出：需求覆盖度评估、信息缺口识别
        """
        try:
            logger.info("Starting coverage assessment")
            
            # 1. 分析需求覆盖度
            coverage_analysis = self._analyze_requirement_coverage(
                requirement_analysis, preliminary_research
            )
            
            # 2. 识别信息缺口
            information_gaps = self._identify_information_gaps(
                requirement_analysis, coverage_analysis
            )
            
            # 3. 生成补充调研建议
            supplementary_research = self._generate_supplementary_research_suggestions(
                information_gaps
            )
            
            # 4. 判断是否需要补充调研
            needs_supplementary = self._determine_supplementary_need(coverage_analysis)
            
            result = {
                "coverage_analysis": coverage_analysis,
                "information_gaps": information_gaps,
                "supplementary_research_suggestions": supplementary_research,
                "needs_supplementary_research": needs_supplementary,
                "overall_coverage_score": coverage_analysis.get("overall_score", 0.0),
                "assessment_timestamp": datetime.utcnow().isoformat()
            }
            
            logger.info(
                "Coverage assessment completed",
                overall_score=result["overall_coverage_score"],
                gaps_count=len(information_gaps),
                needs_supplementary=needs_supplementary
            )
            
            return result
            
        except Exception as e:
            logger.error("Coverage assessment failed", error=str(e))
            raise
    
    def _analyze_requirement_coverage(
        self,
        requirement_analysis: RequirementAnalysis,
        preliminary_research: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析需求覆盖度"""
        objectives = requirement_analysis.research_objectives
        information_summary = preliminary_research.get("information_summary", {})
        
        coverage_details = {}
        total_score = 0.0
        
        for i, objective in enumerate(objectives):
            objective_coverage = self._assess_objective_coverage(objective, information_summary)
            coverage_details[f"objective_{i+1}"] = {
                "objective": objective,
                "coverage_score": objective_coverage["score"],
                "covered_aspects": objective_coverage["covered_aspects"],
                "missing_aspects": objective_coverage["missing_aspects"]
            }
            total_score += objective_coverage["score"]
        
        overall_score = total_score / len(objectives) if objectives else 0.0
        
        return {
            "overall_score": overall_score,
            "objective_coverage": coverage_details,
            "coverage_level": self._determine_coverage_level(overall_score)
        }
    
    def _assess_objective_coverage(
        self, 
        objective: str, 
        information_summary: Dict[str, str]
    ) -> Dict[str, Any]:
        """评估单个目标的覆盖度"""
        objective_keywords = set(objective.lower().split())
        
        covered_aspects = []
        coverage_score = 0.0
        
        for category, summary in information_summary.items():
            summary_keywords = set(summary.lower().split())
            
            # 计算关键词重叠度
            overlap = len(objective_keywords.intersection(summary_keywords))
            if overlap > 0:
                covered_aspects.append({
                    "category": category,
                    "overlap_score": overlap / len(objective_keywords),
                    "relevant_content": summary[:100] + "..."
                })
                coverage_score += overlap / len(objective_keywords)
        
        # 标准化评分
        normalized_score = min(coverage_score / len(information_summary), 1.0) if information_summary else 0.0
        
        # 识别缺失方面
        missing_aspects = self._identify_missing_aspects(objective, covered_aspects)
        
        return {
            "score": normalized_score,
            "covered_aspects": covered_aspects,
            "missing_aspects": missing_aspects
        }
    
    def _identify_information_gaps(
        self,
        requirement_analysis: RequirementAnalysis,
        coverage_analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """识别信息缺口"""
        gaps = []
        
        objective_coverage = coverage_analysis.get("objective_coverage", {})
        
        for obj_key, obj_data in objective_coverage.items():
            if obj_data["coverage_score"] < self.coverage_threshold:
                gap = {
                    "objective": obj_data["objective"],
                    "coverage_score": obj_data["coverage_score"],
                    "missing_aspects": obj_data["missing_aspects"],
                    "gap_severity": self._assess_gap_severity(obj_data["coverage_score"]),
                    "recommended_sources": self._recommend_sources_for_gap(obj_data["objective"])
                }
                gaps.append(gap)
        
        return gaps
    
    def _generate_supplementary_research_suggestions(
        self, 
        information_gaps: List[Dict[str, Any]]
    ) -> List[str]:
        """生成补充调研建议"""
        suggestions = []
        
        for gap in information_gaps:
            objective = gap["objective"]
            missing_aspects = gap["missing_aspects"]
            
            if missing_aspects:
                suggestion = f"针对'{objective}'，建议补充调研：{', '.join(missing_aspects)}"
                suggestions.append(suggestion)
        
        # 添加通用建议
        if information_gaps:
            suggestions.append("建议扩大搜索关键词范围，包含更多相关术语")
            suggestions.append("建议查找更多权威信息源，如学术数据库和官方文档")
        
        return suggestions
    
    def _determine_supplementary_need(self, coverage_analysis: Dict[str, Any]) -> bool:
        """判断是否需要补充调研"""
        overall_score = coverage_analysis.get("overall_score", 0.0)
        return overall_score < self.coverage_threshold
    
    def _determine_coverage_level(self, score: float) -> str:
        """确定覆盖度等级"""
        if score >= 0.8:
            return "优秀"
        elif score >= 0.6:
            return "良好"
        elif score >= 0.4:
            return "一般"
        else:
            return "不足"
    
    def _assess_gap_severity(self, coverage_score: float) -> str:
        """评估缺口严重程度"""
        if coverage_score < 0.3:
            return "严重"
        elif coverage_score < 0.5:
            return "中等"
        else:
            return "轻微"
    
    def _identify_missing_aspects(
        self, 
        objective: str, 
        covered_aspects: List[Dict[str, Any]]
    ) -> List[str]:
        """识别缺失方面"""
        # 基于目标内容识别可能缺失的方面
        objective_lower = objective.lower()
        
        potential_aspects = {
            "技术原理": ["原理", "机制", "算法", "技术"],
            "应用场景": ["应用", "场景", "案例", "实践"],
            "发展趋势": ["趋势", "发展", "未来", "前景"],
            "优缺点分析": ["优势", "缺点", "问题", "挑战"],
            "实施方案": ["实施", "部署", "方案", "策略"]
        }
        
        missing_aspects = []
        covered_keywords = set()
        
        for aspect in covered_aspects:
            covered_keywords.update(aspect.get("relevant_content", "").lower().split())
        
        for aspect_name, keywords in potential_aspects.items():
            if any(keyword in objective_lower for keyword in keywords):
                if not any(keyword in covered_keywords for keyword in keywords):
                    missing_aspects.append(aspect_name)
        
        return missing_aspects
    
    def _recommend_sources_for_gap(self, objective: str) -> List[str]:
        """为缺口推荐信息源"""
        recommendations = []
        objective_lower = objective.lower()
        
        if any(keyword in objective_lower for keyword in ["技术", "算法", "系统"]):
            recommendations.extend(["技术文档", "学术论文", "开源项目"])
        
        if any(keyword in objective_lower for keyword in ["应用", "案例", "实践"]):
            recommendations.extend(["行业报告", "案例研究", "用户评价"])
        
        if any(keyword in objective_lower for keyword in ["趋势", "发展", "未来"]):
            recommendations.extend(["市场报告", "专家观点", "新闻资讯"])
        
        return recommendations or ["综合性资源", "权威机构报告"]
```

---

*本实施指南遵循 `.augmentrules` 规范和设计文档要求，专注于需求分析阶段的业务流程实现。*
