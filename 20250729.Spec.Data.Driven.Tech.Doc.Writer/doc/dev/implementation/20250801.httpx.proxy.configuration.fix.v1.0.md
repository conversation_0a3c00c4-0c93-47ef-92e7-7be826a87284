# httpx 代理配置修复指南

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-08-01
- **修复范围**: Sprint 2 及相关文档中的 httpx 代理配置错误
- **重要性**: 🚨 关键修复 - 错误配置会导致网络请求失败

## 问题描述

### ❌ 错误的配置方式
在多个文档中发现了错误的 httpx 代理配置，使用了 requests 库的配置方式：

```python
# ❌ 错误：这是 requests 库的方式，httpx 不支持
proxies = {"https://": proxy_url, "http://": proxy_url}
async with httpx.AsyncClient(proxies=proxies) as client:  # ❌ httpx 没有 proxies 参数
    response = await client.get(url)
```

### 🔍 错误原因
- httpx 的代理配置 API 与 requests 不同
- httpx 使用 `proxy` 参数，不是 `proxies`
- httpx 支持更灵活的代理配置方式

## ✅ 正确的 httpx 代理配置

### 方式 1：简单代理配置（推荐）
```python
# ✅ 正确：httpx 使用 proxy 参数
async with httpx.AsyncClient(proxy="http://127.0.0.1:8118/") as client:
    response = await client.get(url)
```

### 方式 2：环境变量方式（最简单）
```python
# ✅ 正确：httpx 自动读取环境变量
import os

# 设置环境变量
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:8118/'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:8118/'

# httpx 自动使用环境变量，无需额外配置
async with httpx.AsyncClient() as client:
    response = await client.get(url)
```

### 方式 3：使用 mounts 进行精细控制
```python
# ✅ 正确：使用 mounts 参数进行精细控制
proxy_mounts = {
    "http://": httpx.HTTPTransport(proxy="http://127.0.0.1:8118/"),
    "https://": httpx.HTTPTransport(proxy="http://127.0.0.1:8118/"),
}
async with httpx.AsyncClient(mounts=proxy_mounts) as client:
    response = await client.get(url)
```

### 方式 4：条件代理配置
```python
# ✅ 正确：根据配置动态设置代理
def get_proxy_url():
    return (
        os.getenv('HTTPS_PROXY') or 
        os.getenv('HTTP_PROXY') or 
        'http://127.0.0.1:8118/'
    )

proxy_url = get_proxy_url()
async with httpx.AsyncClient(proxy=proxy_url) as client:
    response = await client.get(url)
```

## 已修复的文件

### ✅ 已修复
1. **Sprint 2 搜索引擎集成指南**
   - 文件：`doc/dev/implementation/20250804.search.engine.integration.guide.v1.1.md`
   - 修复：将 `proxies=proxies` 改为 `proxy=proxy_url`
   - 状态：✅ 已完成

2. **ADR 技术决策文档**
   - 文件：`doc/dev/architecture/decisions/20250731.ADR-002.sprint1.technical.decisions.v1.0.md`
   - 修复：更新代理配置示例代码
   - 状态：✅ 已完成

### ⚠️ 需要注意的文件
以下文件在其他项目中，无法直接修复，但需要开发者注意：

1. **其他项目的 .augmentrules 文件**
   - 包含错误的 httpx 代理配置示例
   - 需要项目维护者自行修复

2. **其他项目的源代码文件**
   - 部分使用了正确的配置（`proxy=proxy_url`）
   - 部分可能仍有错误配置

## Sprint 2 开发指导

### 推荐的代理配置模式

```python
# Sprint 2 推荐的代理配置模式
class NetworkClient:
    """网络客户端基类"""
    
    def __init__(self, proxy_config: Optional[Dict[str, str]] = None):
        self.proxy_config = proxy_config or {}
    
    def get_proxy_url(self) -> Optional[str]:
        """获取代理 URL"""
        return (
            self.proxy_config.get('https_proxy') or 
            os.getenv('HTTPS_PROXY') or 
            os.getenv('https_proxy')
        )
    
    async def create_client(self, **kwargs) -> httpx.AsyncClient:
        """创建 httpx 客户端"""
        proxy_url = self.get_proxy_url()
        
        client_kwargs = {
            'timeout': kwargs.get('timeout', 30),
            'follow_redirects': kwargs.get('follow_redirects', True),
        }
        
        # 只有在有代理配置时才添加 proxy 参数
        if proxy_url:
            client_kwargs['proxy'] = proxy_url
        
        return httpx.AsyncClient(**client_kwargs)

# 使用示例
async def fetch_content(url: str):
    client = NetworkClient()
    async with await client.create_client() as http_client:
        response = await http_client.get(url)
        return response.text
```

### 配置验证

```python
# 代理配置验证函数
async def validate_proxy_config():
    """验证代理配置是否正确"""
    proxy_url = os.getenv('HTTPS_PROXY')
    
    if not proxy_url:
        logger.warning("No proxy configured, direct connection will be used")
        return True
    
    try:
        async with httpx.AsyncClient(proxy=proxy_url, timeout=5) as client:
            response = await client.get('https://httpbin.org/ip')
            response.raise_for_status()
            logger.info("Proxy configuration validated successfully", proxy=proxy_url)
            return True
    except Exception as e:
        logger.error("Proxy configuration validation failed", proxy=proxy_url, error=str(e))
        return False
```

## 测试验证

### 单元测试示例

```python
import pytest
import httpx
from unittest.mock import patch

@pytest.mark.asyncio
async def test_httpx_proxy_configuration():
    """测试 httpx 代理配置"""
    proxy_url = "http://127.0.0.1:8118/"
    
    # 测试正确的代理配置
    async with httpx.AsyncClient(proxy=proxy_url) as client:
        # 这里应该不会抛出异常
        assert client._transport._pool._proxy_url == proxy_url

@pytest.mark.asyncio
async def test_environment_variable_proxy():
    """测试环境变量代理配置"""
    with patch.dict('os.environ', {'HTTPS_PROXY': 'http://127.0.0.1:8118/'}):
        async with httpx.AsyncClient() as client:
            # httpx 应该自动使用环境变量
            # 这里可以通过实际请求来验证
            pass
```

## 最佳实践

### 1. 优先使用环境变量
```python
# ✅ 推荐：让 httpx 自动读取环境变量
async with httpx.AsyncClient() as client:
    response = await client.get(url)
```

### 2. 显式配置时使用 proxy 参数
```python
# ✅ 推荐：需要显式配置时使用 proxy 参数
proxy_url = get_proxy_from_config()
async with httpx.AsyncClient(proxy=proxy_url) as client:
    response = await client.get(url)
```

### 3. 避免使用 requests 风格的配置
```python
# ❌ 避免：不要使用 requests 风格的 proxies 字典
# proxies = {"http://": proxy_url, "https://": proxy_url}
# httpx.AsyncClient(proxies=proxies)  # 这会报错
```

### 4. 添加代理配置日志
```python
# ✅ 推荐：记录代理配置状态
proxy_url = os.getenv('HTTPS_PROXY')
if proxy_url:
    logger.info("Using proxy for HTTP requests", proxy=proxy_url)
else:
    logger.info("No proxy configured, using direct connection")
```

## 总结

这次修复解决了 Sprint 2 文档中的关键错误，确保：

1. ✅ **网络请求正常工作**：正确的代理配置确保在企业网络环境中能够正常访问外部资源
2. ✅ **代码示例准确**：文档中的代码示例可以直接使用，不会误导开发者
3. ✅ **符合 httpx 规范**：使用 httpx 官方推荐的代理配置方式
4. ✅ **向后兼容**：修复后的配置方式与现有代码兼容

**重要提醒**：在 Sprint 2 开发过程中，所有使用 httpx 的代码都应该遵循本文档的配置方式，避免使用 `proxies` 参数。

---

*本修复指南确保 Sprint 2 的网络功能能够在企业代理环境中正常工作。*
