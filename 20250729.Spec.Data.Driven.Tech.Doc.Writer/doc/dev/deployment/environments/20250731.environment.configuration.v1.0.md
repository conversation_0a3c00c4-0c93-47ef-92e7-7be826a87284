# 环境配置规范

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31

## 1. 环境类型定义

### 1.1 环境分类
- **开发环境** (development): 本地开发和调试
- **测试环境** (testing): 自动化测试和集成测试
- **生产环境** (production): 正式部署环境

### 1.2 部署方式对比

| 环境类型 | 部署方式 | 包管理 | 数据库 | 缓存 |
|---------|----------|--------|--------|------|
| 开发环境 | uv虚拟环境 | uv | SQLite | 本地文件 |
| 测试环境 | Docker容器 | uv | SQLite | 本地文件 |
| 生产环境 | Docker容器 | uv | SQLite/PostgreSQL | Redis+文件 |

## 2. 配置文件结构

### 2.1 主配置文件 (config.yaml)
```yaml
# 应用基础配置
app:
  name: "multi-agent-tech-doc-writer"
  version: "1.0.0"
  environment: "development"  # development|testing|production
  debug: true
  log_level: "INFO"

# 数据库配置
database:
  type: "sqlite"  # sqlite|postgresql
  path: "data/app.db"  # SQLite文件路径
  # PostgreSQL配置（生产环境）
  # host: "localhost"
  # port: 5432
  # name: "tech_doc_writer"
  # user: "app_user"
  # password: "${DB_PASSWORD}"

# 缓存配置
cache:
  type: "file"  # file|redis
  directory: "data/cache"
  max_memory_items: 1000
  # Redis配置（生产环境）
  # redis_url: "redis://localhost:6379/0"

# LLM服务配置
llm:
  default_provider: "openai"
  retry_attempts: 3
  timeout_seconds: 30
  
  providers:
    openai:
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      models:
        default: "gpt-4"
        fast: "gpt-3.5-turbo"
        
    anthropic:
      api_key: "${ANTHROPIC_API_KEY}"
      base_url: "https://api.anthropic.com"
      models:
        default: "claude-3-sonnet-20240229"
        fast: "claude-3-haiku-20240307"

# 智能体LLM配置策略
agents:
  RequirementAnalystAgent:
    scenarios:
      requirement_analysis: 
        provider: "openai"
        model: "gpt-4"
      keyword_generation:
        provider: "anthropic"
        model: "claude-3-haiku-20240307"
        
  InformationRetrievalAgent:
    scenarios:
      content_extraction:
        provider: "openai"
        model: "gpt-3.5-turbo"
      quality_assessment:
        provider: "anthropic"
        model: "claude-3-sonnet-20240229"

# 网络配置
network:
  proxy:
    http: "${HTTP_PROXY}"
    https: "${HTTPS_PROXY}"
  user_agents:
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
  request_delay:
    min_seconds: 1
    max_seconds: 3

# 日志配置
logging:
  level: "INFO"  # DEBUG|INFO|WARNING|ERROR
  format: "structured"  # simple|structured
  outputs:
    console: true
    file: true
  file_config:
    path: "log/app.log"
    max_size_mb: 100
    backup_count: 5
    rotation: "daily"
```

### 2.2 环境变量配置 (.env)
```bash
# 应用环境
APP_ENVIRONMENT=development
APP_DEBUG=true

# 数据库配置
DB_PATH=data/app.db

# LLM API密钥
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 网络代理
HTTP_PROXY=http://127.0.0.1:8118/
HTTPS_PROXY=http://127.0.0.1:8118/

# 缓存配置
CACHE_DIR=data/cache
CACHE_MAX_SIZE_GB=10

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=log
```

## 3. 开发环境配置

### 3.1 开发环境搭建步骤
```bash
# 1. 创建项目目录
cd 20250729.Spec.Data.Driven.Tech.Doc.Writer

# 2. 创建uv虚拟环境
uv venv --python 3.11

# 3. 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate   # Windows

# 4. 安装依赖
uv pip install -r requirements.txt

# 5. 复制配置文件模板
cp config.yaml.example config.yaml
cp .env.example .env

# 6. 编辑配置文件
# 设置API密钥和代理配置

# 7. 初始化数据库
python -m src.database.init_db

# 8. 创建必要目录
mkdir -p data/cache/{raw,converted,llm,metadata}
mkdir -p log
```

### 3.2 开发环境配置文件 (config.yaml.example)
```yaml
app:
  name: "multi-agent-tech-doc-writer"
  version: "1.0.0"
  environment: "development"
  debug: true
  log_level: "DEBUG"

database:
  type: "sqlite"
  path: "data/app.db"

cache:
  type: "file"
  directory: "data/cache"
  max_memory_items: 500

llm:
  default_provider: "openai"
  retry_attempts: 3
  timeout_seconds: 30
  
  providers:
    openai:
      api_key: "${OPENAI_API_KEY}"
      models:
        default: "gpt-4"
        fast: "gpt-3.5-turbo"

network:
  proxy:
    http: "${HTTP_PROXY}"
    https: "${HTTPS_PROXY}"
  request_delay:
    min_seconds: 1
    max_seconds: 2

logging:
  level: "DEBUG"
  outputs:
    console: true
    file: true
  file_config:
    path: "log/dev.log"
```

## 4. 生产环境配置

### 4.1 Docker配置 (Dockerfile)
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装uv
RUN pip install uv

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装Python依赖
RUN uv pip install --system -r pyproject.toml

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data/cache/{raw,converted,llm,metadata} log

# 设置权限
RUN chmod +x scripts/*.sh

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "src.main"]
```

### 4.2 Docker Compose配置 (docker-compose.yml)
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - APP_ENVIRONMENT=production
      - APP_DEBUG=false
    env_file:
      - .env.production
    volumes:
      - ./data:/app/data
      - ./log:/app/log
      - ./config.production.yaml:/app/config.yaml
    restart: unless-stopped
    
  # 可选：Redis缓存服务
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
```

## 5. 配置管理工具

### 5.1 配置加载器
```python
import yaml
import os
from pathlib import Path
from typing import Dict, Any

class ConfigManager:
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = Path(config_path)
        self.config = self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        # 环境变量替换
        return self.substitute_env_vars(config)
        
    def substitute_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """替换配置中的环境变量"""
        def replace_value(value):
            if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                env_var = value[2:-1]
                return os.getenv(env_var, value)
            elif isinstance(value, dict):
                return {k: replace_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [replace_value(item) for item in value]
            return value
            
        return replace_value(config)
        
    def get(self, key_path: str, default=None):
        """获取配置值，支持点号路径"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
                
        return value
```

## 6. 环境检查和验证

### 6.1 环境检查脚本
```python
def check_environment():
    """检查环境配置是否正确"""
    checks = []
    
    # 检查Python版本
    import sys
    if sys.version_info >= (3, 11):
        checks.append(("Python版本", "✓", f"Python {sys.version}"))
    else:
        checks.append(("Python版本", "✗", f"需要Python 3.11+，当前: {sys.version}"))
    
    # 检查必要目录
    required_dirs = ["data", "data/cache", "log"]
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            checks.append((f"目录 {dir_path}", "✓", "存在"))
        else:
            checks.append((f"目录 {dir_path}", "✗", "不存在"))
    
    # 检查配置文件
    if Path("config.yaml").exists():
        checks.append(("配置文件", "✓", "config.yaml存在"))
    else:
        checks.append(("配置文件", "✗", "config.yaml不存在"))
    
    # 检查环境变量
    required_env_vars = ["OPENAI_API_KEY", "HTTP_PROXY"]
    for env_var in required_env_vars:
        if os.getenv(env_var):
            checks.append((f"环境变量 {env_var}", "✓", "已设置"))
        else:
            checks.append((f"环境变量 {env_var}", "✗", "未设置"))
    
    return checks
```

## 7. 部署检查清单

### 7.1 开发环境检查清单
- [ ] Python 3.11+ 已安装
- [ ] uv 包管理器已安装
- [ ] 虚拟环境已创建并激活
- [ ] 依赖包已安装
- [ ] 配置文件已复制并编辑
- [ ] 环境变量已设置
- [ ] 必要目录已创建
- [ ] 数据库已初始化
- [ ] 网络代理配置正确

### 7.2 生产环境检查清单
- [ ] Docker 已安装
- [ ] 生产配置文件已准备
- [ ] 环境变量已设置
- [ ] 数据卷已挂载
- [ ] 网络配置正确
- [ ] 日志目录可写
- [ ] 健康检查配置
- [ ] 监控和告警配置
