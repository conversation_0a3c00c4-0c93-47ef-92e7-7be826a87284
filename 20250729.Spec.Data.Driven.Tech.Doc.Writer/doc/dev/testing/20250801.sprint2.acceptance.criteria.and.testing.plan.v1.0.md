# Sprint 2 验收标准和测试计划

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-08-01
- **适用范围**: Sprint 2 搜索引擎接入和需求分析阶段验收
- **测试重点**: 端到端业务流程验证、API 集成稳定性、性能指标达成
- **相关文档**:
  - [Sprint 2 规划](../sprints/planning/20250804.sprint2.planning.v1.1.md)
  - [搜索引擎集成指南](../implementation/20250804.search.engine.integration.guide.v1.1.md)

## 验收标准总览

### 核心业务目标
- ✅ **需求分析阶段完整流程**: 7 个步骤全部实现并可正常运行
- ✅ **双搜索引擎 API 接入**: Tavily + DuckDuckGo 搜索正常工作，支持真实网络搜索
- ✅ **初步调研功能**: 从模拟数据升级到真实信息检索
- ✅ **端到端验证**: 从用户输入到调研提纲生成的完整流程可用

### 技术质量目标
- ✅ **性能指标**: 以原定目标为努力方向（搜索 < 30秒，抓取 > 90%，缓存 > 40%），URL 去重缓存正常工作
- ✅ **性能监控**: 所有关键操作记录性能指标，为后续优化提供数据基础
- ✅ **稳定性指标**: 搜索引擎自动降级机制正常，简单错误处理有效
- ✅ **代码质量**: 覆盖率 > 80%，符合 .augmentrules 规范

## 详细验收标准

### 1. 搜索引擎 API 接入验收标准

#### 1.1 双搜索引擎功能
**验收条件**:
- [ ] Tavily API 正常初始化和连接（首选）
- [ ] DuckDuckGo API 正常初始化和连接（兜底）
- [ ] 支持中英文关键词搜索
- [ ] 搜索结果包含必需字段：title, content, url, relevance_score
- [ ] 网络代理配置正确（HTTP_PROXY/HTTPS_PROXY，包括 Tavily API）
- [ ] 自动降级机制正常工作（Tavily 不可用时使用 DuckDuckGo）
- [ ] 性能监控正常工作（记录搜索时间、使用的搜索引擎、结果数量）

**测试用例**:
```python
# 测试用例 1: Tavily 搜索功能
keywords = ["人工智能", "机器学习"]
results = await dual_search_service.search(keywords)
assert len(results) > 0
assert all(required_field in result for result in results
          for required_field in ["title", "url", "relevance_score"])

# 测试用例 2: 自动降级机制
# 模拟 Tavily 不可用，验证自动使用 DuckDuckGo
with mock.patch.object(dual_search_service, 'tavily_available', False):
    results = await dual_search_service.search(keywords)
    assert len(results) > 0

# 测试用例 3: 代理网络支持
# 验证 Tavily API 和 DuckDuckGo 都通过代理访问

# 测试用例 4: httpx 代理配置验证
async def test_httpx_proxy_configuration():
    """验证 httpx 代理配置正确"""
    from src.backend.agents.retrieval_services import WebContentProcessor

    processor = WebContentProcessor()

    # 验证代理 URL 获取
    proxy_url = processor.get_proxy_url()
    assert proxy_url is not None, "Proxy URL should be configured"

    # 验证 httpx 客户端创建时使用正确的 proxy 参数
    async with processor.create_client() as client:
        # 验证客户端配置了代理
        assert hasattr(client, '_transport'), "Client should have transport"
        # 注意：这里不使用 proxies 参数，而是 proxy 参数
```

#### 1.2 网页内容抓取和处理
**验收条件**:
- [ ] 网页内容能够正确抓取和解析
- [ ] HTML 内容清洁效果良好，去除广告和无关信息
- [ ] Markdown 转换格式规范，保留核心信息
- [ ] 内容质量评估准确，过滤低质量内容
- [ ] 所有 httpx 请求记录到日志（URL/字节数/状态码/请求时间）
- [ ] 网页抓取性能监控正常（记录抓取时间、成功率）

**测试用例**:
```python
# 测试用例 1: 网页内容抓取
url = "https://example.com/article"
content = await content_processor.fetch_and_process(url)
assert content is not None
assert "title" in content
assert "content" in content
assert content["quality_score"] > 0.0

# 测试用例 2: Markdown 转换质量
assert "##" in content["content"]  # 包含标题结构
assert len(content["content"]) > 100  # 内容长度合理
```

#### 1.3 URL 内容缓存机制
**验收条件**:
- [ ] URL 状态缓存正常工作（内存 + 文件系统）
- [ ] 同样的 URL 不会重复处理
- [ ] url_status.json 格式简洁清晰
- [ ] 缓存文件原子写入，避免数据损坏
- [ ] 缓存命中率监控正常（记录但不作为硬性要求）

#### 1.4 性能监控机制
**验收条件**:
- [ ] 所有搜索操作记录性能指标（时间、引擎、结果数）
- [ ] 所有网页抓取记录性能指标（时间、大小、状态）
- [ ] 缓存操作记录命中率统计
- [ ] 性能日志格式结构化，便于分析
- [ ] 性能指标与目标值对比记录（30秒、90%、40%）

#### 1.5 httpx 代理配置验证
**验收条件**:
- [ ] 所有 httpx 客户端使用正确的代理配置（proxy 参数，不是 proxies）
- [ ] 代理配置支持环境变量自动读取
- [ ] 网络请求在代理环境下正常工作
- [ ] 代理配置错误时有清晰的错误提示

**测试用例**:
```python
# 测试用例 1: URL 去重测试
url = "https://example.com/article"
# 第一次处理
await url_cache.process_url(url)
assert await url_cache.is_url_processed(url) == True

# 第二次处理（应该跳过）
processed = await url_cache.process_url(url)
assert processed == False  # 跳过处理

# 测试用例 2: 缓存文件完整性
# 验证 url_status.json 文件格式正确
```

### 2. 需求分析阶段流程验收标准

#### 2.1 初步调研功能
**验收条件**:
- [ ] 基于搜索关键词进行真实网络搜索
- [ ] 信息源按类型正确分类（学术、行业、新闻、政府、技术）
- [ ] 信息摘要质量良好，包含关键观点
- [ ] 引用格式符合 APA 第七版标准
- [ ] 与现有需求分析智能体无缝集成

**测试用例**:
```python
# 测试用例 1: 端到端初步调研
keywords = SearchKeywords(
    primary_keywords=["区块链", "技术"],
    technical_terms=["分布式", "共识算法"]
)
research_result = await preliminary_research.conduct_preliminary_research(keywords)

assert "information_summary" in research_result
assert "source_list" in research_result
assert "research_scope" in research_result
assert research_result["total_sources"] > 0

# 测试用例 2: 信息源分类
categorized = research_result["categorized_sources"]
assert isinstance(categorized, dict)
assert len(categorized) > 0
```

#### 2.2 信息覆盖度评估
**验收条件**:
- [ ] 覆盖度分析准确，能识别已覆盖和未覆盖的需求
- [ ] 信息缺口识别精确，提供具体的缺失内容描述
- [ ] 补充调研建议实用，包含具体的搜索方向
- [ ] 迭代机制工作正常，支持多轮调研优化

**测试用例**:
```python
# 测试用例 1: 覆盖度评估
requirement_analysis = RequirementAnalysis(...)
preliminary_research = {...}
coverage_result = await coverage_service.assess_information_coverage(
    requirement_analysis, preliminary_research
)

assert "coverage_analysis" in coverage_result
assert "information_gaps" in coverage_result
assert 0.0 <= coverage_result["overall_coverage_score"] <= 1.0

# 测试用例 2: 缺口识别
if coverage_result["overall_coverage_score"] < 0.7:
    assert len(coverage_result["information_gaps"]) > 0
    assert coverage_result["needs_supplementary_research"] is True
```

#### 2.3 调研报告提纲生成
**验收条件**:
- [ ] 提纲结构清晰，拆解到三级标题
- [ ] 内容组织逻辑合理，符合调研报告规范
- [ ] 与用户意图高度匹配，实现用户需求
- [ ] 支持用户反馈流程，可迭代优化

#### 2.4 调研行动规划制定
**验收条件**:
- [ ] 调研任务拆解合理，粒度适中
- [ ] 信息源描述具体，包含获取方法举例
- [ ] 优先级排序科学，考虑依赖关系
- [ ] 行动计划可执行，为深入调研阶段做准备

### 3. 端到端集成验收标准

#### 3.1 完整流程验证
**验收条件**:
- [ ] 从用户输入到提纲生成的完整流程运行稳定
- [ ] 无阻塞性错误，支持异常情况的优雅处理
- [ ] 支持多种调研主题类型（技术、商业、学术）
- [ ] 输出格式符合设计文档要求

**端到端测试场景**:
```python
# 场景 1: 技术调研主题
user_request = UserRequest(
    topic="边缘计算在物联网中的应用",
    scope="技术原理、应用场景、发展趋势",
    format="技术调研报告"
)

# 执行完整流程
result = await requirement_analysis_workflow(user_request)

# 验证输出
assert "analysis" in result
assert "keywords" in result
assert "preliminary_research" in result
assert "coverage_assessment" in result
assert "document_outline" in result
assert "action_plan" in result

# 场景 2: 商业分析主题
user_request = UserRequest(
    topic="人工智能在金融风控中的商业价值",
    scope="市场现状、商业模式、投资回报",
    format="商业分析报告"
)
# 执行并验证...

# 场景 3: 学术研究主题
user_request = UserRequest(
    topic="深度学习在医学影像诊断中的最新进展",
    scope="算法创新、临床应用、研究趋势",
    format="学术综述"
)
# 执行并验证...
```

## 性能测试计划

### 1. 响应时间测试
**目标**: 搜索响应时间 < 30秒（尽力而为，非硬性要求）

**测试方法**:
```python
import time

async def test_search_response_time():
    start_time = time.time()
    results = await search_service.search(["人工智能", "深度学习"])
    end_time = time.time()

    response_time = end_time - start_time
    # 记录性能数据，但不作为硬性验收标准
    logger.info(f"Search response time: {response_time}s (target: <30s)")

    # 硬性要求：搜索必须成功返回结果
    assert len(results) > 0, "Search must return results"
```

### 2. 并发性能测试
**目标**: 支持 5+ 并发搜索任务（尽力而为）

**测试方法**:
```python
import asyncio

async def test_concurrent_search():
    start_time = time.time()
    tasks = []
    for i in range(5):
        task = search_service.search([f"关键词{i}", "测试"])
        tasks.append(task)

    results = await asyncio.gather(*tasks, return_exceptions=True)
    total_time = time.time() - start_time

    # 验证所有任务都成功完成（硬性要求）
    successful_results = [r for r in results if not isinstance(r, Exception)]
    assert len(successful_results) >= 4, "At least 4 out of 5 concurrent searches should succeed"

    # 记录并发性能（尽力而为指标）
    logger.info(f"Concurrent search performance: {total_time}s for 5 tasks")
```

### 3. 缓存性能测试
**目标**: URL 去重缓存正常工作，缓存命中率 > 40%（尽力而为）

**测试方法**:
```python
async def test_url_cache_functionality():
    # 测试 URL 去重功能（硬性要求）
    url = "https://example.com/test"

    # 第一次处理
    processed1 = await url_cache.process_url(url)
    assert processed1 == True, "First URL processing should succeed"

    # 第二次处理（应该跳过）
    processed2 = await url_cache.process_url(url)
    assert processed2 == False, "Second URL processing should be skipped (cached)"

    # 验证缓存状态
    assert await url_cache.is_url_processed(url) == True

    # 记录缓存命中率（尽力而为指标）
    hit_rate = await url_cache.get_hit_rate()
    logger.info(f"URL cache hit rate: {hit_rate} (target: >40%)")
```

### 4. 性能监控验证测试
**目标**: 验证性能监控机制正常工作

**测试方法**:
```python
async def test_performance_monitoring():
    # 执行一次完整的搜索和内容抓取流程
    keywords = ["性能测试", "监控"]

    # 捕获日志输出
    with LogCapture() as log_capture:
        results = await search_service.search(keywords)

        # 验证搜索性能日志
        search_logs = [log for log in log_capture.records if "Search completed" in log.getMessage()]
        assert len(search_logs) > 0, "Search performance logs should be recorded"

        # 验证性能指标字段
        search_log = search_logs[0]
        assert hasattr(search_log, 'total_time_seconds'), "Should record total search time"
        assert hasattr(search_log, 'search_engine_used'), "Should record search engine used"
        assert hasattr(search_log, 'performance_target_30s'), "Should record target comparison"

        # 如果有网页抓取，验证抓取性能日志
        fetch_logs = [log for log in log_capture.records if "HTTP request completed" in log.getMessage()]
        for fetch_log in fetch_logs:
            assert hasattr(fetch_log, 'request_time_seconds'), "Should record request time"
            assert hasattr(fetch_log, 'content_length'), "Should record content size"

        logger.info("Performance monitoring validation completed successfully")
```

## 质量保证检查清单

### 代码质量检查
- [ ] 每个文件不超过300行（遵循 .augmentrules）
- [ ] 每个函数不超过50行
- [ ] 100% 类型注解覆盖
- [ ] 完整的错误处理和日志记录
- [ ] 英文注释和日志消息
- [ ] 通过 mypy 静态类型检查
- [ ] 通过 black 代码格式化检查

### 功能完整性检查
- [ ] 所有 Sprint 2 规划的功能点都已实现
- [ ] 所有验收标准都已满足
- [ ] 端到端测试通过
- [ ] 性能指标达成
- [ ] 错误处理完善

### 文档完整性检查
- [ ] 所有新增功能都有对应的文档
- [ ] API 文档更新完整
- [ ] 用户使用指南更新
- [ ] 开发者指南更新

## 测试执行计划

### 第1天: 单元测试
- 搜索引擎 API 集成单元测试
- 网页内容处理单元测试
- 缓存机制单元测试
- 需求分析组件单元测试

### 第2天: 集成测试
- 搜索引擎与缓存系统集成测试
- 需求分析阶段各步骤集成测试
- 错误处理和容错机制测试

### 第3天: 端到端测试
- 完整业务流程端到端测试
- 多种调研主题类型测试
- 用户界面和体验测试

### 第4天: 性能测试
- 响应时间性能测试
- 并发性能测试
- 缓存性能测试
- 资源使用监控

### 第5天: 验收测试
- 所有验收标准验证
- 用户验收测试
- 文档完整性检查
- 发布准备

## 风险和应对措施

### 技术风险
1. **搜索 API 限制**: DuckDuckGo 可能有频率限制
   - **应对**: 实现请求间隔控制，添加备用搜索引擎

2. **网页抓取失败**: 部分网站可能阻止访问
   - **应对**: 实现优雅降级，提供备用信息源

3. **缓存系统故障**: Redis 或文件系统可能不可用
   - **应对**: 实现缓存降级，直接执行搜索

### 质量风险
1. **测试覆盖不足**: 可能遗漏边界情况
   - **应对**: 增加边界条件测试，进行代码审查

2. **性能不达标**: 响应时间可能超过预期
   - **应对**: 优化搜索策略，增加并发处理

---

*本测试计划遵循 `.augmentrules` 规范，确保 Sprint 2 交付质量符合项目标准。*
