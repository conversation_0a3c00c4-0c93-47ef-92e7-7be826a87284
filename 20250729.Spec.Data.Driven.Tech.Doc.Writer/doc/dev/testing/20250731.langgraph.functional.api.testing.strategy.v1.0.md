# LangGraph 0.6.2 函数式API测试策略

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **文档类型**: 测试策略
- **相关文档**: 
  - [ADR-003: LangGraph 0.6.2 函数式API优化集成决策](../architecture/decisions/20250731.ADR-003.langgraph.functional.api.optimization.v1.0.md)
  - [LangGraph 0.6.2 函数式API迁移指南](../implementation/20250731.langgraph.functional.api.migration.guide.v1.0.md)

## 概述

本文档定义了LangGraph 0.6.2函数式API的全面测试策略，确保迁移过程中的质量保证和性能验证。测试策略涵盖单元测试、集成测试、性能测试和端到端测试。

## 测试目标

### 功能性目标
- ✅ 验证函数式API的正确性
- ✅ 确保并行执行的准确性
- ✅ 验证检查点和恢复机制
- ✅ 确保向后兼容性

### 非功能性目标
- ✅ 验证3-5倍性能提升
- ✅ 确保10+并发任务支持
- ✅ 验证缓存命中率目标（LLM>60%, 数据>40%）
- ✅ 确保平均响应时间<30分钟

## 测试架构

### 测试层次结构
```
测试金字塔
├── 端到端测试 (10%)
│   ├── 完整工作流测试
│   └── 用户场景测试
├── 集成测试 (20%)
│   ├── 智能体协作测试
│   ├── 并行执行测试
│   └── 检查点恢复测试
├── 单元测试 (70%)
│   ├── 任务函数测试
│   ├── 工作流逻辑测试
│   └── 错误处理测试
```

### 测试环境配置
```python
# conftest.py - 测试配置
import pytest
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.func import entrypoint, task

@pytest.fixture
def test_checkpointer():
    """测试用检查点器"""
    return InMemorySaver()

@pytest.fixture
def mock_llm_service():
    """模拟LLM服务"""
    class MockLLMService:
        def invoke(self, prompt):
            return f"Mock response for: {prompt[:50]}..."
    return MockLLMService()

@pytest.fixture
def test_config():
    """测试配置"""
    return {
        "llm_api_key": "test_key",
        "cache_enabled": True,
        "max_concurrency": 5,
        "timeout": 300
    }
```

## 单元测试策略

### 任务函数测试
```python
import pytest
from unittest.mock import Mock, patch
from langgraph.func import task

class TestInformationRetrievalTasks:
    """信息检索任务单元测试"""
    
    @pytest.mark.asyncio
    async def test_retrieve_academic_sources(self, mock_llm_service):
        """测试学术资源检索任务"""
        keywords = ["artificial intelligence", "machine learning"]
        
        with patch('academic_retrieval_service.search') as mock_search:
            mock_search.return_value = {"results": ["paper1", "paper2"]}
            
            result = await retrieve_academic_sources(keywords)
            
            assert result["results"] == ["paper1", "paper2"]
            mock_search.assert_called_once_with(keywords)
    
    @pytest.mark.asyncio
    async def test_retrieve_industry_reports(self):
        """测试行业报告检索任务"""
        keywords = ["blockchain", "cryptocurrency"]
        
        with patch('industry_report_service.search') as mock_search:
            mock_search.return_value = {"reports": ["report1", "report2"]}
            
            result = await retrieve_industry_reports(keywords)
            
            assert result["reports"] == ["report1", "report2"]
    
    @pytest.mark.asyncio
    async def test_task_error_handling(self):
        """测试任务错误处理"""
        keywords = ["invalid_keyword"]
        
        with patch('academic_retrieval_service.search') as mock_search:
            mock_search.side_effect = Exception("Service unavailable")
            
            with pytest.raises(Exception, match="Service unavailable"):
                await retrieve_academic_sources(keywords)

class TestRequirementAnalysisTasks:
    """需求分析任务单元测试"""
    
    @pytest.mark.asyncio
    async def test_parse_user_input(self):
        """测试用户输入解析"""
        user_request = "我需要了解人工智能在医疗领域的应用"
        
        with patch('requirement_parser.parse') as mock_parse:
            mock_parse.return_value = {
                "domain": "医疗",
                "technology": "人工智能",
                "focus": "应用"
            }
            
            result = await parse_user_input(user_request)
            
            assert result["domain"] == "医疗"
            assert result["technology"] == "人工智能"
    
    @pytest.mark.asyncio
    async def test_generate_search_keywords(self):
        """测试搜索关键词生成"""
        user_intent = {
            "domain": "医疗",
            "technology": "人工智能"
        }
        
        with patch('keyword_generator.generate') as mock_generate:
            mock_generate.return_value = [
                "AI in healthcare", "医疗人工智能", 
                "machine learning medicine"
            ]
            
            result = await generate_search_keywords(user_intent)
            
            assert len(result) == 3
            assert "AI in healthcare" in result
```

### 工作流逻辑测试
```python
class TestWorkflowLogic:
    """工作流逻辑测试"""
    
    @pytest.mark.asyncio
    async def test_parallel_information_retrieval(self, test_checkpointer):
        """测试并行信息检索工作流"""
        keywords = ["AI", "ML"]
        
        # 模拟各个检索服务
        with patch.multiple(
            'retrieval_services',
            academic_retrieval_service=Mock(),
            industry_report_service=Mock(),
            news_service=Mock()
        ) as mocks:
            # 设置模拟返回值
            mocks['academic_retrieval_service'].search.return_value = {"academic": "data"}
            mocks['industry_report_service'].search.return_value = {"industry": "data"}
            mocks['news_service'].search.return_value = {"news": "data"}
            
            # 执行工作流
            result = await parallel_information_retrieval(keywords)
            
            # 验证结果
            assert "academic" in result
            assert "industry" in result
            assert "news" in result
            
            # 验证所有服务都被调用
            for mock_service in mocks.values():
                mock_service.search.assert_called_once_with(keywords)
    
    @pytest.mark.asyncio
    async def test_requirement_analysis_workflow(self):
        """测试需求分析工作流"""
        user_request = "分析区块链技术发展趋势"
        
        with patch.multiple(
            'analysis_services',
            requirement_parser=Mock(),
            intent_analyzer=Mock(),
            keyword_generator=Mock(),
            scope_definer=Mock()
        ) as mocks:
            # 设置模拟返回值
            mocks['requirement_parser'].parse.return_value = {"parsed": True}
            mocks['intent_analyzer'].analyze.return_value = {"intent": "trend_analysis"}
            mocks['keyword_generator'].generate.return_value = ["blockchain", "DeFi"]
            mocks['scope_definer'].define.return_value = {"scope": "technology_trend"}
            
            # 执行工作流
            result = await requirement_analysis_workflow(user_request)
            
            # 验证结果结构
            assert "parsed_request" in result
            assert "user_intent" in result
            assert "keywords" in result
            assert "scope" in result
```

## 集成测试策略

### 智能体协作测试
```python
class TestAgentCollaboration:
    """智能体协作集成测试"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_requirement_to_retrieval_integration(self):
        """测试需求分析到信息检索的集成"""
        user_request = "研究量子计算在密码学中的应用"
        
        # 执行需求分析
        analysis_result = await requirement_analysis_workflow(user_request)
        
        # 使用分析结果进行信息检索
        keywords = analysis_result["keywords"]
        retrieval_result = await parallel_information_retrieval(keywords)
        
        # 验证数据流
        assert len(keywords) > 0
        assert all(source in retrieval_result for source in 
                  ["academic", "industry", "news"])
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_full_research_pipeline(self):
        """测试完整研究流水线"""
        user_request = "分析边缘计算技术现状和发展前景"
        
        # 执行完整流水线
        analysis_result = await requirement_analysis_workflow(user_request)
        retrieval_result = await parallel_information_retrieval(
            analysis_result["keywords"]
        )
        
        # 验证流水线完整性
        assert analysis_result["user_intent"] is not None
        assert len(retrieval_result) >= 3  # 至少3个数据源
```

### 并行执行测试
```python
class TestParallelExecution:
    """并行执行测试"""
    
    @pytest.mark.asyncio
    async def test_concurrent_task_execution(self):
        """测试并发任务执行"""
        import time
        import asyncio
        
        # 创建多个并行任务
        keywords_list = [
            ["AI", "healthcare"],
            ["blockchain", "finance"],
            ["IoT", "smart_city"],
            ["5G", "telecommunications"],
            ["quantum", "computing"]
        ]
        
        start_time = time.time()
        
        # 并行执行多个检索任务
        tasks = [parallel_information_retrieval(keywords) 
                for keywords in keywords_list]
        results = await asyncio.gather(*tasks)
        
        execution_time = time.time() - start_time
        
        # 验证并行执行效果
        assert len(results) == 5
        assert execution_time < 120  # 并行执行应该在2分钟内完成
        
        # 验证每个结果的完整性
        for result in results:
            assert all(source in result for source in 
                      ["academic", "industry", "news"])
    
    @pytest.mark.asyncio
    async def test_load_balancing(self):
        """测试负载均衡"""
        # 创建大量任务测试负载均衡
        large_keyword_list = [f"keyword_{i}" for i in range(20)]
        
        start_time = time.time()
        
        # 执行大量并行任务
        tasks = [retrieve_academic_sources([keyword]) 
                for keyword in large_keyword_list]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        execution_time = time.time() - start_time
        
        # 验证负载均衡效果
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) >= 15  # 至少75%成功率
        assert execution_time < 300  # 5分钟内完成
```

## 性能测试策略

### 性能基准测试
```python
class TestPerformanceBenchmarks:
    """性能基准测试"""
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_parallel_vs_serial_performance(self):
        """测试并行vs串行性能对比"""
        keywords = ["artificial intelligence", "machine learning", "deep learning"]
        
        # 测试并行版本
        start_time = time.time()
        parallel_result = await parallel_information_retrieval(keywords)
        parallel_time = time.time() - start_time
        
        # 测试串行版本（模拟）
        start_time = time.time()
        serial_result = await serial_information_retrieval_simulation(keywords)
        serial_time = time.time() - start_time
        
        # 验证性能提升
        improvement_ratio = serial_time / parallel_time
        assert improvement_ratio >= 3.0, f"性能提升不足，仅为{improvement_ratio:.2f}倍"
        
        # 验证结果一致性
        assert set(parallel_result.keys()) == set(serial_result.keys())
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_cache_performance(self):
        """测试缓存性能"""
        keywords = ["blockchain", "cryptocurrency"]
        
        # 第一次执行（无缓存）
        start_time = time.time()
        first_result = await parallel_information_retrieval(keywords)
        first_time = time.time() - start_time
        
        # 第二次执行（有缓存）
        start_time = time.time()
        second_result = await parallel_information_retrieval(keywords)
        second_time = time.time() - start_time
        
        # 验证缓存效果
        cache_speedup = first_time / second_time
        assert cache_speedup >= 2.0, f"缓存加速不足，仅为{cache_speedup:.2f}倍"
        
        # 验证结果一致性
        assert first_result == second_result
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_concurrent_user_simulation(self):
        """测试并发用户模拟"""
        import asyncio
        
        # 模拟10个并发用户
        user_requests = [
            f"研究主题{i}的技术发展趋势" for i in range(10)
        ]
        
        start_time = time.time()
        
        # 并发执行用户请求
        tasks = [requirement_analysis_workflow(request) 
                for request in user_requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # 验证并发处理能力
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) >= 8  # 至少80%成功率
        assert total_time < 600  # 10分钟内完成
        
        # 验证平均响应时间
        avg_response_time = total_time / len(successful_results)
        assert avg_response_time < 60  # 平均响应时间小于1分钟
```

## 端到端测试策略

### 用户场景测试
```python
class TestEndToEndScenarios:
    """端到端用户场景测试"""
    
    @pytest.mark.e2e
    @pytest.mark.asyncio
    async def test_technical_researcher_scenario(self):
        """测试技术研究人员使用场景"""
        # 用户故事：技术研究人员需要了解AI在医疗领域的应用
        user_request = "我需要全面了解人工智能在医疗诊断中的最新应用和发展趋势"
        
        # 执行完整工作流
        analysis_result = await requirement_analysis_workflow(user_request)
        retrieval_result = await parallel_information_retrieval(
            analysis_result["keywords"]
        )
        
        # 验证输出质量
        assert "医疗" in str(analysis_result["user_intent"]).lower()
        assert "人工智能" in str(analysis_result["keywords"]).lower()
        assert len(retrieval_result["academic"]) > 0
        assert len(retrieval_result["industry"]) > 0
    
    @pytest.mark.e2e
    @pytest.mark.asyncio
    async def test_product_manager_scenario(self):
        """测试产品经理使用场景"""
        # 用户故事：产品经理需要技术可行性分析
        user_request = "分析区块链技术在供应链管理中的商业化应用前景"
        
        # 执行完整工作流
        analysis_result = await requirement_analysis_workflow(user_request)
        retrieval_result = await parallel_information_retrieval(
            analysis_result["keywords"]
        )
        
        # 验证商业化关注点
        assert "商业" in str(analysis_result["user_intent"]).lower() or \
               "应用" in str(analysis_result["user_intent"]).lower()
        assert len(retrieval_result["industry"]) > 0  # 行业报告很重要
```

## 测试执行和报告

### 测试执行配置
```python
# pytest.ini
[tool:pytest]
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    e2e: 端到端测试
    slow: 慢速测试

testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 并行执行配置
addopts = 
    --strict-markers
    --strict-config
    --verbose
    -ra
    --cov=src
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
```

### 持续集成配置
```yaml
# .github/workflows/test.yml
name: Test LangGraph Functional API

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11, 3.12]
    
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        pip install uv
        uv sync
    
    - name: Run unit tests
      run: uv run pytest tests/unit -m "not slow"
    
    - name: Run integration tests
      run: uv run pytest tests/integration
    
    - name: Run performance tests
      run: uv run pytest tests/performance -m "performance"
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
```

## 质量门控

### 测试覆盖率要求
- 单元测试覆盖率：≥80%
- 集成测试覆盖率：≥60%
- 关键路径覆盖率：100%

### 性能要求
- 并行执行性能提升：≥3倍
- 缓存命中率：LLM≥60%, 数据≥40%
- 并发用户支持：≥10个
- 平均响应时间：≤30分钟

### 质量检查清单
- [ ] 所有单元测试通过
- [ ] 集成测试通过
- [ ] 性能基准达标
- [ ] 端到端场景验证
- [ ] 代码覆盖率达标
- [ ] 性能监控正常
- [ ] 错误处理完善
- [ ] 文档更新完整

---

*本测试策略遵循项目 `.augmentrules` 规范，保存在 `doc/dev/testing/` 目录下。*
