# Sprint 1 规划文档 (统一版)

## 文档信息
- **文档版本**: v2.0 (统一版)
- **创建日期**: 2025-07-31
- **最后更新**: 2025-08-01
- **Sprint 周期**: 2025-07-31 至 2025-08-01 (实际执行周期)
- **文档状态**: 统一整理版本，合并了所有规划相关内容
- **相关文档**:
  - [Sprint 1 开发记录](../../implementation/20250801.sprint1.unified.development.record.v1.0.md)
  - [Sprint 1 总结报告](../reviews/20250801.sprint1.unified.summary.v1.0.md)
  - [Sprint 1 技术架构](../../implementation/20250801.sprint1.unified.technical.architecture.v1.0.md)

## Sprint 目标

### 主要目标
**函数式API优先策略**：基于LangGraph 0.6函数式API搭建基础架构，实现高性能多智能体系统的技术基础。

### 具体目标
1. **函数式API迁移**: 完成核心智能体从StateGraph到@task/@entrypoint的迁移
2. **并行执行架构**: 实现Send API动态任务分发和3层并行执行架构
3. **基础架构搭建**: 建立符合函数式API的项目结构和基础框架
4. **缓存系统建设**: 实现支持并行执行的Redis + 文件系统二层缓存
5. **CLI 界面**: 建立支持函数式API的命令行界面框架

## Sprint Backlog

### 核心技术任务 (42 SP) - 函数式API优先

#### TECH-001: 项目架构搭建 (5 SP)
**目标**: 建立符合 `.augmentrules` 的目录结构和基础框架

**任务分解**:
- [ ] 创建标准目录结构
- [ ] 配置 Python 环境和依赖管理 (uv)
- [ ] 建立基础配置系统
- [ ] 设置开发工具链 (linting, formatting, type checking)

**验收标准**:
- [ ] 目录结构符合 `.augmentrules` 规范
- [ ] `uv` 包管理正常工作
- [ ] 基础配置文件 `config.yaml.example` 创建完成
- [ ] 开发工具链配置完成并通过检查

**负责人**: [待分配]
**预计完成时间**: Sprint 1 Week 1

#### TECH-002: 数据库设计 (3 SP)
**目标**: 设计核心数据模型，支持 SQLite/PostgreSQL

**任务分解**:
- [ ] 设计智能体状态数据模型
- [ ] 设计缓存元数据模型
- [ ] 设计任务执行记录模型
- [ ] 实现数据库迁移脚本

**验收标准**:
- [ ] 数据模型设计文档完成
- [ ] SQLite 数据库正常工作
- [ ] 支持 PostgreSQL 切换
- [ ] 数据库迁移脚本测试通过

**负责人**: [待分配]
**预计完成时间**: Sprint 1 Week 1

#### TECH-003: API框架搭建 (5 SP)
**目标**: 基于 FastAPI 的 RESTful API 基础框架

**任务分解**:
- [ ] 建立 FastAPI 应用结构
- [ ] 实现基础路由和中间件
- [ ] 集成 Pydantic 数据验证
- [ ] 建立 API 文档自动生成

**验收标准**:
- [ ] FastAPI 应用正常启动
- [ ] 基础健康检查接口工作
- [ ] API 文档自动生成
- [ ] 请求/响应数据验证正常

**负责人**: [待分配]
**预计完成时间**: Sprint 1 Week 1

#### TECH-006: 二层缓存基础框架 (5 SP)
**目标**: 建立 Redis 内存缓存 + 文件系统缓存的二层架构

**任务分解**:
- [ ] 实现 Redis 连接和基础操作
- [ ] 实现文件系统缓存管理
- [ ] 建立统一缓存接口
- [ ] 实现缓存策略和 TTL 管理

**验收标准**:
- [ ] Redis 缓存正常工作
- [ ] 文件系统缓存目录结构正确
- [ ] 统一接口可以透明访问二层缓存
- [ ] TTL 策略正确执行

**负责人**: [待分配]
**预计完成时间**: Sprint 1 Week 2

#### TECH-007: 原始文件缓存 (8 SP)
**目标**: 实现网络获取文件的缓存系统

**任务分解**:
- [ ] 实现网页内容抓取和缓存
- [ ] 实现文档文件下载和缓存
- [ ] 建立文件类型识别和分类
- [ ] 实现缓存清理和维护机制

**验收标准**:
- [ ] 网页内容能够正确抓取和缓存
- [ ] 支持多种文档格式的缓存
- [ ] 文件按类型正确分类存储
- [ ] 缓存清理机制正常工作

**负责人**: [待分配]
**预计完成时间**: Sprint 1 Week 2

#### TECH-029: LangGraph 0.6 函数式API迁移 (13 SP) ⭐ 核心任务
**目标**: 将核心智能体从StateGraph迁移到LangGraph 0.6函数式API

**任务分解**:
- [ ] BaseAgent基类函数式API重构 (4 SP)
- [ ] 信息检索模块并行化改造 (4 SP)
- [ ] 需求分析模块@task装饰器改造 (3 SP)
- [ ] 基础测试框架搭建 (2 SP)

**验收标准**:
- [ ] BaseAgent完全基于@task装饰器实现
- [ ] 信息检索支持并行执行，性能提升≥3倍
- [ ] 需求分析工作流使用@task和@entrypoint
- [ ] 单元测试覆盖率≥80%，集成测试通过

**负责人**: [待分配]
**预计完成时间**: Sprint 1 Week 1-2

#### TECH-030: 并行执行架构实现 (8 SP) ⭐ 核心任务
**目标**: 实现基于@task的三层并行架构

**任务分解**:
- [ ] Send API动态任务分发机制 (3 SP)
- [ ] 并发限制和超时配置 (2 SP)
- [ ] 性能监控和日志 (2 SP)
- [ ] 错误处理和恢复机制 (1 SP)

**验收标准**:
- [ ] 支持10+并发任务处理
- [ ] Send API动态分发和负载均衡正常工作
- [ ] 完整的性能监控和错误日志
- [ ] 自动检查点和恢复机制正常工作

**负责人**: [待分配]
**预计完成时间**: Sprint 1 Week 2-3

#### TECH-010: 命令行界面框架 (5 SP)
**目标**: CLI 基础框架和命令解析

**任务分解**:
- [ ] 建立 CLI 应用结构
- [ ] 实现基础命令解析
- [ ] 建立配置文件加载机制
- [ ] 实现基础错误处理和用户反馈

**验收标准**:
- [ ] CLI 应用正常启动
- [ ] 基础命令解析工作正常
- [ ] 配置文件正确加载
- [ ] 错误信息用户友好

**负责人**: [待分配]
**预计完成时间**: Sprint 1 Week 2

### 支撑任务 (3 SP)

#### PBI-001: 用户需求输入界面 (3 SP)
**目标**: 实现基于函数式API的自然语言需求输入功能

**任务分解**:
- [ ] 实现基于@task的CLI需求输入命令
- [ ] 建立需求文本验证
- [ ] 实现需求结构化存储
- [ ] 集成函数式API工作流

**验收标准**:
- [ ] 支持自然语言输入调研需求
- [ ] 输入验证正常工作
- [ ] 需求能够正确存储并触发@task工作流
- [ ] 用户交互体验良好

**负责人**: [待分配]
**预计完成时间**: Sprint 1 Week 3

## 技术实现细节

### BaseAgent 基类实现 - 函数式API优先

基于LangGraph 0.6函数式API的@task装饰器实现：

```python
# src/agents/base.py
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List, Annotated
from pydantic import BaseModel
from langgraph.graph import MessagesState
from langgraph.types import Send
from langgraph import task, entrypoint

class AgentConfig(BaseModel):
    agent_id: str
    agent_type: str
    llm_config: Dict[str, Any]
    tools: Optional[List[str]] = None
    max_retries: int = 3
    timeout: int = 300
    parallel_limit: int = 10

class BaseAgent(ABC):
    """
    基于LangGraph 0.6函数式API的智能体基类
    使用@task装饰器实现并行执行和自动检查点
    """

    def __init__(self, config: AgentConfig):
        self.config = config
        self.status = "idle"

    # === 函数式API核心方法 ===
    @abstractmethod
    @task
    async def execute_task(self, state: MessagesState) -> MessagesState:
        """
        使用@task装饰器的核心执行方法
        自动支持并行执行和检查点机制
        """
        pass

    @abstractmethod
    @task
    async def validate_input(self, state: MessagesState) -> bool:
        """输入验证任务"""
        pass

    @abstractmethod
    @task
    async def process_parallel_tasks(self, state: MessagesState) -> List[Send]:
        """
        生成并行任务的Send对象列表
        支持动态任务分发和负载均衡
        """
        pass

    # === 工作流组织方法 ===
    @entrypoint
    async def run_workflow(self, state: MessagesState) -> MessagesState:
        """
        使用@entrypoint装饰器的主工作流
        自动管理任务依赖和并行执行
        """
        # 1. 输入验证
        is_valid = await self.validate_input(state)
        if not is_valid:
            raise ValueError("Input validation failed")

        # 2. 执行核心任务
        result = await self.execute_task(state)

        # 3. 处理并行任务（如果需要）
        parallel_sends = await self.process_parallel_tasks(result)
        if parallel_sends:
            # Send API动态分发并行任务
            return Send(parallel_sends)

        return result
```

### 二层缓存系统实现

```python
# src/cache/manager.py
import redis
import json
from pathlib import Path
from typing import Optional, Any

class TwoLayerCacheManager:
    def __init__(self, redis_url: str = "redis://localhost:6379", 
                 cache_dir: str = "./cache"):
        self.memory_cache = redis.from_url(redis_url, decode_responses=True)
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        (self.cache_dir / "raw_files").mkdir(exist_ok=True)
        (self.cache_dir / "processed").mkdir(exist_ok=True)
        (self.cache_dir / "llm_responses").mkdir(exist_ok=True)
    
    async def get(self, key: str) -> Optional[Any]:
        # L1: 内存缓存
        try:
            result = self.memory_cache.get(key)
            if result:
                return json.loads(result)
        except Exception:
            pass
        
        # L2: 文件系统缓存
        file_path = self._get_file_path(key)
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = json.load(f)
                
            # 热点数据缓存到内存
            try:
                self.memory_cache.setex(key, 86400, json.dumps(content))
            except Exception:
                pass
                
            return content
        
        return None
    
    async def set(self, key: str, value: Any, content_type: str = 'general'):
        # 存储到文件系统
        file_path = self._get_file_path(key, content_type)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(value, f, ensure_ascii=False, indent=2)
        
        # 存储到内存缓存
        try:
            self.memory_cache.setex(key, 86400, json.dumps(value))
        except Exception:
            pass
    
    def _get_file_path(self, key: str, content_type: str = 'general') -> Path:
        # 根据内容类型确定存储路径
        if content_type.startswith('raw_'):
            base_dir = self.cache_dir / "raw_files"
        elif content_type.startswith('processed_'):
            base_dir = self.cache_dir / "processed"
        elif content_type.startswith('llm_'):
            base_dir = self.cache_dir / "llm_responses"
        else:
            base_dir = self.cache_dir / "general"
        
        # 使用 key 的 hash 作为文件名
        import hashlib
        filename = hashlib.md5(key.encode()).hexdigest() + '.json'
        return base_dir / filename
```

### CLI 界面框架实现

```python
# src/cli/main.py
import argparse
import asyncio
from pathlib import Path
from src.config.manager import ConfigManager

class CLI:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.parser = self._setup_parser()
    
    def _setup_parser(self):
        parser = argparse.ArgumentParser(
            description="多智能体技术调研报告编写系统"
        )
        
        # 全局参数
        parser.add_argument('--config', type=str, help='配置文件路径')
        parser.add_argument('--verbose', action='store_true', help='详细输出')
        
        # 子命令
        subparsers = parser.add_subparsers(dest='command', help='可用命令')
        
        # 需求分析命令
        analyze_parser = subparsers.add_parser('analyze', help='分析需求')
        analyze_parser.add_argument('input', help='需求描述')
        analyze_parser.add_argument('--output', help='输出文件路径')
        
        # 状态查询命令
        status_parser = subparsers.add_parser('status', help='查询状态')
        status_parser.add_argument('--task-id', help='任务ID')
        
        return parser
    
    async def run(self, args=None):
        parsed_args = self.parser.parse_args(args)
        
        # 加载配置
        config = await self.config_manager.load_config(parsed_args.config)
        
        # 执行命令
        if parsed_args.command == 'analyze':
            await self._handle_analyze(parsed_args, config)
        elif parsed_args.command == 'status':
            await self._handle_status(parsed_args, config)
        else:
            self.parser.print_help()
    
    async def _handle_analyze(self, args, config):
        print(f"分析需求: {args.input}")
        # TODO: 实现需求分析逻辑
    
    async def _handle_status(self, args, config):
        print("查询系统状态")
        # TODO: 实现状态查询逻辑

def main():
    cli = CLI()
    asyncio.run(cli.run())

if __name__ == "__main__":
    main()
```

## 风险和缓解措施

### 技术风险
1. **LangGraph 0.6 学习曲线**: 团队需要时间熟悉新特性
   - **缓解**: 提供技术培训和示例代码
   
2. **缓存系统复杂性**: 二层缓存可能增加调试难度
   - **缓解**: 建立详细的日志和监控

3. **异步编程挑战**: 异步代码可能增加开发复杂度
   - **缓解**: 建立异步编程最佳实践指南

### 进度风险
1. **任务估算偏差**: 可能低估某些任务的复杂度
   - **缓解**: 每日站会跟踪进度，及时调整

2. **依赖阻塞**: 某些任务可能相互依赖
   - **缓解**: 明确任务依赖关系，并行开发

## 成功标准

### 技术指标
- [ ] 所有技术任务完成并通过验收标准
- [ ] 代码覆盖率达到 80%（函数式API专项测试）
- [ ] 所有代码通过静态类型检查
- [ ] 性能基准测试通过：并行执行性能提升≥3倍
- [ ] LangGraph 0.6函数式API迁移完成核心模块
- [ ] BaseAgent基类完全基于@task装饰器实现
- [ ] Send API动态任务分发机制正常工作
- [ ] 支持10+并发任务处理

### 质量指标
- [ ] 零 P0/P1 级别的 bug
- [ ] 代码审查通过率 100%
- [ ] 文档覆盖所有公共接口

### 用户体验指标
- [ ] CLI 界面响应时间 < 1 秒
- [ ] 错误信息清晰易懂
- [ ] 配置系统支持零配置启动

## 每日站会安排

**时间**: 每日上午 9:30-9:45
**参与人员**: 全体开发团队成员
**议程**:
1. 昨日完成的工作
2. 今日计划的工作
3. 遇到的阻碍和需要的帮助

## Sprint 回顾安排

**时间**: Sprint 结束后的周五下午
**议程**:
1. Sprint 目标达成情况回顾
2. 技术实现经验总结
3. 流程改进建议
4. 下个 Sprint 的准备工作

---

*本规划文档遵循项目 `.augmentrules` 规范，保存在 `doc/dev/sprints/planning/` 目录下。*
