# Sprint 2 规划文档

## 文档信息
- **文档版本**: v1.1
- **创建日期**: 2025-08-04
- **最后更新**: 2025-08-04 22:00
- **Sprint 周期**: 2025-08-04 至 2025-08-11 (7天)
- **Sprint 目标**: 优先跑通需求分析阶段业务流程，完成搜索引擎 API 真实接入
- **主要变更**:
  - 整合了 20250801.sprint2.planning.v1.0.md 的详细验收标准和风险缓解措施
  - 增加了完整的成功标准分类和 Sprint 回顾安排
  - 遵循 .augmentrules 的"优先更新现有文档"原则
- **相关文档**:
  - [多智能体工作流设计](../../design/20250730.multi.agent.workflow.design.v1.0.md)
  - [PRD文档](../../requirements/user-stories/20250731.multi.agent.tech.research.system.prd.v1.0.md)
  - [Sprint 1 统一总结](../reviews/20250801.sprint1.unified.summary.v1.0.md)

## Sprint 目标

### 主要目标
**业务流程优先策略**：优先跑通设计文档中需求分析阶段的完整业务流程，实现从"模拟数据"到"真实调研"的关键跃升。

### 具体目标
1. **搜索引擎 API 接入**: 完成 Tavily（首选）和 DuckDuckGo（兜底）双搜索引擎真实接入
2. **初步调研功能**: 实现设计文档中"初步调研"阶段的完整流程
3. **需求分析阶段**: 跑通从用户输入到调研提纲生成的端到端流程
4. **内容处理管道**: 实现网页抓取、清洁、转换的完整处理链路
5. **质量保证机制**: 实现信息覆盖度评估和缺口识别功能

## Sprint Backlog

### 阶段 1：搜索引擎 API 接入 (2天) - P0 核心任务

#### TECH-041: Tavily 搜索引擎集成 (6 SP) ⭐ 最高优先级
**目标**: 集成 Tavily 搜索引擎作为主要搜索服务

**任务分解**:
- [ ] 使用 Context 7 获取 Tavily API 最新文档和最佳实践
- [ ] 集成 langchain-community 中的 TavilySearchResults 工具
- [ ] 实现网络代理支持（遵循 .augmentrules 代理要求）
- [ ] 实现搜索结果解析和数据清洁
- [ ] 添加错误处理和重试机制
- [ ] 集成性能监控（记录搜索时间、结果质量）

**验收标准**:
- [ ] Tavily 搜索 API 正常工作
- [ ] 支持中英文关键词搜索
- [ ] 搜索结果包含标题、摘要、URL、相关性评分
- [ ] 网络代理配置正确（HTTP_PROXY/HTTPS_PROXY）
- [ ] 错误处理机制工作正常

**技术要求**:
```python
# 基于现有依赖 langchain-community>=0.3.0
from langchain_community.tools import TavilySearchResults
# 集成到 src/backend/agents/retrieval_services.py
```

#### TECH-042: DuckDuckGo 搜索引擎集成 (4 SP)
**目标**: 集成 DuckDuckGo 搜索引擎作为备用搜索服务

**任务分解**:
- [ ] 使用 Context 7 获取 DuckDuckGo 搜索工具最新文档
- [ ] 集成 langchain-community 中的 DuckDuckGoSearchRun 工具
- [ ] 实现搜索结果标准化处理
- [ ] 添加简单错误处理（失败跳过 + 日志记录）

**验收标准**:
- [ ] DuckDuckGo 搜索 API 正常工作
- [ ] 搜索结果格式与 Tavily 保持一致
- [ ] 支持中英文搜索
- [ ] 错误处理机制完善

#### TECH-043: 搜索引擎自动降级机制 (4 SP)
**目标**: 实现 Tavily → DuckDuckGo 的自动降级机制

**任务分解**:
- [ ] 设计搜索引擎选择策略
- [ ] 实现自动降级逻辑
- [ ] 添加降级事件监控和日志
- [ ] 集成到信息检索智能体

**验收标准**:
- [ ] Tavily 失败时自动切换到 DuckDuckGo
- [ ] 降级过程对用户透明
- [ ] 降级事件被正确记录和监控

### 阶段 2：网页内容处理 (2天) - P0 核心任务

#### TECH-044: 网页内容抓取和处理 (8 SP)
**目标**: 实现网页内容的抓取、清洁和 Markdown 转换

**任务分解**:
- [ ] 使用 Context 7 获取 httpx、readabilipy、markdownify 最新文档
- [ ] 使用 httpx 进行网页抓取（支持代理）
- [ ] 使用 readabilipy 清洁 HTML 内容
- [ ] 使用 markdownify 转换为 Markdown 格式
- [ ] 实现内容质量评估和过滤机制
- [ ] 添加请求日志记录（INFO 级别）

**验收标准**:
- [ ] 网页内容能够正确抓取和解析
- [ ] HTML 内容清洁效果良好，去除广告和无关信息
- [ ] Markdown 转换格式规范，保留核心信息
- [ ] 内容质量评估准确，过滤低质量内容
- [ ] 所有 httpx 请求记录到日志（URL/字节数/状态码）

#### TECH-045: URL 内容缓存机制 (6 SP)
**目标**: 实现 URL 内容的简化缓存机制，避免重复网页抓取和处理

**任务分解**:
- [ ] 实现简化版 URL 状态缓存（内存 + 文件持久化）
- [ ] 设计 url_status.json 文件格式（仅记录成功处理的 URL）
- [ ] 实现 URL 去重检查机制
- [ ] 集成到网页内容处理流程
- [ ] 添加缓存命中率监控（尽力而为指标）

**验收标准**:
- [ ] URL 状态缓存正常工作（内存 + 文件系统）
- [ ] 同样 URL 不会重复处理
- [ ] url_status.json 格式简洁清晰
- [ ] 缓存文件原子写入，避免数据损坏

### 阶段 3：需求分析阶段完整流程 (2天) - P0 核心业务

#### TECH-046: 初步调研功能实现 (10 SP) ⭐ 核心业务流程
**目标**: 实现设计文档中的"初步调研"步骤

**任务分解**:
- [ ] 基于搜索关键词进行真实网络搜索
- [ ] 收集和分类信息源（学术、行业、新闻、政府、开源）
- [ ] 生成信息摘要和源清单
- [ ] 实现 APA 第七版格式的引用管理
- [ ] 集成到需求分析工作流

**验收标准**:
- [ ] 搜索关键词能够触发真实的网络搜索
- [ ] 信息源按类型正确分类和存储
- [ ] 信息摘要质量良好，包含关键观点
- [ ] 引用格式符合 APA 第七版标准
- [ ] 与现有需求分析智能体无缝集成

#### TECH-047: 信息覆盖度评估 (6 SP)
**目标**: 实现需求覆盖度分析和信息缺口识别

**任务分解**:
- [ ] 对比需求清单和收集信息的覆盖度
- [ ] 识别信息缺口和不足领域
- [ ] 生成补充调研建议
- [ ] 实现迭代调研机制

**验收标准**:
- [ ] 覆盖度分析准确，能识别已覆盖和未覆盖的需求
- [ ] 信息缺口识别精确，提供具体的缺失内容描述
- [ ] 补充调研建议实用，包含具体的搜索方向
- [ ] 迭代机制工作正常，支持多轮调研优化

### 阶段 4：集成测试和优化 (1天) - P1 质量保证

#### TECH-048: 需求分析阶段集成测试 (6 SP)
**目标**: 对完整的需求分析阶段进行端到端测试

**任务分解**:
- [ ] 从用户输入到提纲生成的完整流程测试
- [ ] 验证搜索引擎 API 的稳定性和性能
- [ ] 测试不同类型的调研主题（技术、商业、学术）
- [ ] 验证输出质量和格式规范性

**验收标准**:
- [ ] 端到端流程运行稳定，无阻塞性错误
- [ ] 搜索引擎 API 响应时间 < 30秒，成功率 > 95%
- [ ] 支持多种调研主题类型，输出质量一致
- [ ] 输出格式符合设计文档要求

## 技术实现约束

### 遵循 .augmentrules 规范
- **网络代理**: 所有网络访问使用 HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/
- **包管理**: 使用 uv 管理依赖，禁止手动编辑 pyproject.toml
- **代码质量**: 每个文件不超过300行，每个函数不超过50行
- **日志规范**: 代码注释和日志使用英文，用户界面使用中文
- **强制 LiteLLM**: 必须使用 LiteLLM 库，禁止直接调用 LLM 大模型

### 技术栈要求
- **搜索集成**: langchain-community (Tavily, DuckDuckGo)
- **内容处理**: readabilipy (HTML清洁), markdownify (HTML转Markdown)
- **网络请求**: httpx (支持代理配置)
- **缓存系统**: 内存缓存 + 文件系统 (持久化缓存)

### 开源库文档获取规范
- **强制要求**: 开发搜索引擎相关功能时，必须先使用 Context 7 或 DeepWiki 获取最新文档
- **问题导向**: 结合具体要解决的问题查询相关文档和最佳实践
- **版本兼容**: 确保查询的文档版本与项目使用的库版本兼容

## 风险和缓解措施

### 技术风险
1. **Tavily API 可用性**: Tavily API 可能不稳定或配额限制
   - **缓解**: 实现自动降级到 DuckDuckGo，确保服务连续性

2. **网页抓取反爬虫**: 部分网站可能阻止自动化访问
   - **缓解**: 简单策略，失败跳过并记录日志

3. **网络代理稳定性**: 代理服务可能不稳定
   - **缓解**: 添加代理连通性检测，所有网络请求统一使用代理

### 进度风险
1. **双搜索引擎集成复杂度**: 可能低估集成难度
   - **缓解**: 优先实现 Tavily，DuckDuckGo 作为简单兜底

2. **缓存机制简化**: 简化版缓存可能功能不足
   - **缓解**: 先实现基础功能，后续 Sprint 增强

## 成功标准

### 功能指标
- [ ] Tavily + DuckDuckGo 双搜索引擎集成完成并稳定运行
- [ ] 需求分析阶段完整流程实现
- [ ] 端到端流程测试通过，无阻塞性错误
- [ ] 支持至少 3 种不同类型的调研主题

### 性能指标
- [ ] 搜索响应时间尽力而为（目标 < 30秒，非硬性要求）
- [ ] 网页抓取成功率尽力而为（目标 > 90%，非硬性要求）
- [ ] 缓存命中率尽力而为（目标 > 40%，非硬性要求）
- [ ] URL 去重缓存正常工作（硬性要求）
- [ ] 系统并发支持 5+ 任务（目标值，尽力而为）

### 容错性验收标准
- [ ] 网络异常时自动重试，最多3次
- [ ] LLM 服务不可用时启用备用提供者
- [ ] 部分搜索失败时系统继续运行
- [ ] 错误信息清晰，提供解决建议

### 质量指标
- [ ] 代码覆盖率 > 80%
- [ ] 所有代码通过静态类型检查
- [ ] 零 P0/P1 级别的 bug
- [ ] 文档覆盖所有新增功能

### 用户体验指标
- [ ] CLI 界面响应流畅，进度显示清晰
- [ ] 错误信息友好，提供解决建议
- [ ] 输出格式规范，符合用户期望

## Sprint 回顾安排

**时间**: Sprint 结束后的周五下午
**议程**:
1. Sprint 目标达成情况回顾
2. 搜索引擎 API 集成经验总结
3. 需求分析阶段流程优化建议
4. Sprint 3 深入调研阶段准备工作

---

*本规划文档遵循项目 `.augmentrules` 规范，专注于业务流程优先和搜索引擎 API 接入的核心目标。*
