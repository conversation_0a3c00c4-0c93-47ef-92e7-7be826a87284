# 数据模型规范

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31

## 1. 核心数据模型

### 1.1 基础模型类
```python
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class BaseDataModel(BaseModel):
    """所有数据模型的基类"""
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None
```

### 1.2 智能体输入输出模型

#### 需求分析智能体
```python
class RequirementAnalysisInput(BaseModel):
    user_request: str = Field(description="用户原始需求描述")
    context: Optional[str] = None
    constraints: Optional[Dict[str, Any]] = None

class RequirementAnalysisOutput(BaseModel):
    user_intent: str = Field(description="用户意图分析")
    requirement_list: List[str] = Field(description="具体需求清单")
    search_keywords: List[str] = Field(description="搜索关键词列表")
    scope_definition: str = Field(description="调研范围界定")
```

#### 信息检索智能体
```python
class InformationRetrievalInput(BaseModel):
    search_keywords: List[str]
    source_types: List[str] = Field(default=["web", "academic", "industry"])
    language_preferences: List[str] = Field(default=["zh", "en"])
    max_results: int = Field(default=50)

class InformationRetrievalOutput(BaseModel):
    raw_documents: List[Dict[str, Any]]
    source_summary: Dict[str, int]
    retrieval_metadata: Dict[str, Any]
    total_found: int
```

#### 内容分析智能体
```python
class ContentAnalysisInput(BaseModel):
    requirement_list: List[str]
    raw_documents: List[Dict[str, Any]]
    analysis_criteria: Dict[str, Any]

class ContentAnalysisOutput(BaseModel):
    coverage_analysis: Dict[str, float]
    quality_assessment: Dict[str, Any]
    information_gaps: List[str]
    content_summary: str
```

#### 文档编写智能体
```python
class DocumentWritingInput(BaseModel):
    document_outline: Dict[str, Any]
    research_results: List[Dict[str, Any]]
    writing_style: str = Field(default="academic")
    format_requirements: Dict[str, Any]

class DocumentWritingOutput(BaseModel):
    document_content: str
    section_contents: Dict[str, str]
    citations: List[Dict[str, str]]
    word_count: int
```

## 2. 工作流状态模型

### 2.1 工作流状态
```python
class WorkflowStatus(str, Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class WorkflowState(BaseModel):
    workflow_id: str
    current_stage: str
    status: WorkflowStatus
    progress: float = Field(ge=0.0, le=1.0)
    stage_results: Dict[str, Any] = Field(default_factory=dict)
    error_info: Optional[Dict[str, Any]] = None
```

### 2.2 任务状态模型
```python
class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    RETRY = "retry"

class TaskState(BaseModel):
    task_id: str
    task_type: str
    agent_name: str
    status: TaskStatus
    input_data: Dict[str, Any]
    output_data: Optional[Dict[str, Any]] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    retry_count: int = 0
```

## 3. 缓存数据模型

### 3.1 缓存元数据
```python
class CacheMetadata(BaseModel):
    cache_key: str
    cache_type: str = Field(description="raw|converted|llm|metadata")
    file_path: Optional[str] = None
    content_hash: str
    size_bytes: int
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl_seconds: Optional[int] = None

class CacheEntry(BaseModel):
    metadata: CacheMetadata
    content: Optional[str] = None  # 小内容直接存储
    is_file_based: bool = False    # 是否基于文件存储
```

### 3.2 文件缓存模型
```python
class RawFileCache(BaseModel):
    url: str
    file_path: str
    content_type: str
    file_size: int
    download_time: datetime
    last_modified: Optional[datetime] = None
    etag: Optional[str] = None

class ConvertedFileCache(BaseModel):
    source_file_path: str
    converted_file_path: str
    conversion_type: str = Field(description="html_to_md|pdf_to_text")
    conversion_time: datetime
    conversion_config: Dict[str, Any]
```

## 4. LLM调用模型

### 4.1 LLM配置模型
```python
class LLMProvider(str, Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    LOCAL = "local"

class LLMConfig(BaseModel):
    provider: LLMProvider
    model_name: str
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(default=4000, gt=0)
    timeout_seconds: int = Field(default=30, gt=0)

class LLMRequest(BaseModel):
    agent_name: str
    task_type: str
    prompt: str
    config: LLMConfig
    context: Optional[Dict[str, Any]] = None

class LLMResponse(BaseModel):
    request_id: str
    provider: LLMProvider
    model_name: str
    response_text: str
    tokens_used: int
    response_time: float
    cached: bool = False
```

## 5. 用户反馈模型

### 5.1 反馈数据模型
```python
class FeedbackType(str, Enum):
    REQUIREMENT_CHANGE = "requirement_change"
    ADDITIONAL_RESEARCH = "additional_research"
    TEXT_MODIFICATION = "text_modification"

class UserFeedback(BaseModel):
    feedback_id: str
    workflow_id: str
    stage_name: str
    feedback_type: FeedbackType
    feedback_content: str
    specific_sections: Optional[List[str]] = None
    priority: int = Field(default=1, ge=1, le=3)

class FeedbackAnalysis(BaseModel):
    feedback_id: str
    analysis_result: Dict[str, Any]
    action_items: List[str]
    estimated_effort: str
    requires_restart: bool
```

## 6. 数据库表结构

### 6.1 SQLite表定义
```sql
-- 工作流状态表
CREATE TABLE workflow_states (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    workflow_id TEXT UNIQUE NOT NULL,
    current_stage TEXT NOT NULL,
    status TEXT NOT NULL,
    progress REAL DEFAULT 0.0,
    stage_results TEXT, -- JSON格式
    error_info TEXT,    -- JSON格式
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 任务状态表
CREATE TABLE task_states (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT UNIQUE NOT NULL,
    workflow_id TEXT NOT NULL,
    task_type TEXT NOT NULL,
    agent_name TEXT NOT NULL,
    status TEXT NOT NULL,
    input_data TEXT,    -- JSON格式
    output_data TEXT,   -- JSON格式
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflow_states(workflow_id)
);

-- 缓存元数据表
CREATE TABLE cache_metadata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cache_key TEXT UNIQUE NOT NULL,
    cache_type TEXT NOT NULL,
    file_path TEXT,
    content_hash TEXT NOT NULL,
    size_bytes INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    access_count INTEGER DEFAULT 0,
    ttl_seconds INTEGER
);

-- LLM调用记录表
CREATE TABLE llm_calls (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_id TEXT UNIQUE NOT NULL,
    agent_name TEXT NOT NULL,
    task_type TEXT NOT NULL,
    provider TEXT NOT NULL,
    model_name TEXT NOT NULL,
    tokens_used INTEGER NOT NULL,
    response_time REAL NOT NULL,
    cached BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 7. 数据验证规则

### 7.1 通用验证规则
- 所有ID字段使用UUID4格式
- 时间戳使用ISO 8601格式
- 文件路径使用相对路径
- JSON字段大小限制为10MB

### 7.2 业务验证规则
- 工作流ID在系统中唯一
- 任务ID在工作流中唯一
- 缓存键值全局唯一
- LLM调用记录保留30天
