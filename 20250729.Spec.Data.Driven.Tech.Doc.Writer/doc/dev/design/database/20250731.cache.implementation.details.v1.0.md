# 缓存系统实现细节

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31

## 1. 缓存架构概述

### 1.1 二层缓存设计
- **L1缓存**: 内存字典（快速访问）
- **L2缓存**: 本地文件系统（持久化存储）
- **元数据**: SQLite数据库（检索和管理）

### 1.2 缓存类型分类
```
data/cache/
├── raw/         # 原始文件缓存（网页、PDF等）
├── converted/   # 转换文件缓存（Markdown、文本等）
├── llm/         # LLM响应缓存
└── metadata/    # 元数据缓存（文件路径、摘要等）
```

## 2. 缓存实现策略

### 2.1 存储策略

| 缓存类型 | 存储位置 | 存储内容 | 持久化 |
|---------|----------|----------|--------|
| 原始文件 | L2文件系统 | 完整文件内容 | 是 |
| 转换文件 | L2文件系统 | 转换后内容 | 是 |
| LLM响应 | L1内存+L2文件 | 响应文本 | 是 |
| 元数据 | L1内存+SQLite | 路径、摘要、索引 | 是 |

### 2.2 缓存键值设计

#### 原始文件缓存键
```python
def generate_raw_cache_key(url: str, headers: Dict[str, str]) -> str:
    """生成原始文件缓存键"""
    content = f"{url}:{sorted(headers.items())}"
    return f"raw:{hashlib.sha256(content.encode()).hexdigest()[:16]}"
```

#### LLM响应缓存键
```python
def generate_llm_cache_key(prompt: str, config: LLMConfig) -> str:
    """生成LLM响应缓存键"""
    content = f"{prompt}:{config.model_dump_json()}"
    return f"llm:{hashlib.sha256(content.encode()).hexdigest()[:16]}"
```

### 2.3 文件路径规则

#### 原始文件路径
```python
def get_raw_file_path(cache_key: str, content_type: str) -> str:
    """获取原始文件存储路径"""
    ext = get_extension_by_content_type(content_type)
    return f"data/cache/raw/{cache_key[:2]}/{cache_key}{ext}"
```

#### 转换文件路径
```python
def get_converted_file_path(source_key: str, conversion_type: str) -> str:
    """获取转换文件存储路径"""
    ext = get_extension_by_conversion_type(conversion_type)
    return f"data/cache/converted/{source_key[:2]}/{source_key}_{conversion_type}{ext}"
```

## 3. 缓存管理器实现

### 3.1 核心缓存管理器
```python
class CacheManager:
    def __init__(self, cache_dir: str = "data/cache"):
        self.cache_dir = Path(cache_dir)
        self.memory_cache: Dict[str, Any] = {}
        self.cache_stats = CacheStats()
        
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存内容"""
        # 1. 检查L1内存缓存
        if key in self.memory_cache:
            self.cache_stats.hit_count += 1
            return self.memory_cache[key]
            
        # 2. 检查L2文件缓存
        metadata = await self.get_cache_metadata(key)
        if metadata and metadata.file_path:
            content = await self.load_from_file(metadata.file_path)
            # 更新L1缓存
            self.memory_cache[key] = content
            self.cache_stats.hit_count += 1
            return content
            
        self.cache_stats.miss_count += 1
        return None
        
    async def set(self, key: str, content: Any, ttl: Optional[int] = None) -> None:
        """设置缓存内容"""
        # 1. 存储到L1内存
        self.memory_cache[key] = content
        
        # 2. 存储到L2文件系统
        file_path = await self.save_to_file(key, content)
        
        # 3. 更新元数据
        metadata = CacheMetadata(
            cache_key=key,
            cache_type=self.get_cache_type(key),
            file_path=file_path,
            content_hash=self.calculate_hash(content),
            size_bytes=len(str(content)),
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            ttl_seconds=ttl
        )
        await self.save_cache_metadata(metadata)
```

### 3.2 专用缓存管理器

#### 原始文件缓存管理器
```python
class RawFileCacheManager(CacheManager):
    async def cache_web_content(self, url: str, content: bytes, 
                              content_type: str) -> str:
        """缓存网页内容"""
        cache_key = self.generate_raw_cache_key(url, {"content-type": content_type})
        file_path = self.get_raw_file_path(cache_key, content_type)
        
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(content)
            
        # 更新元数据
        metadata = RawFileCache(
            url=url,
            file_path=str(file_path),
            content_type=content_type,
            file_size=len(content),
            download_time=datetime.now()
        )
        await self.save_raw_file_metadata(metadata)
        
        return cache_key
```

#### LLM响应缓存管理器
```python
class LLMCacheManager(CacheManager):
    def __init__(self, max_memory_items: int = 1000):
        super().__init__()
        self.max_memory_items = max_memory_items
        
    async def cache_llm_response(self, request: LLMRequest, 
                               response: LLMResponse) -> None:
        """缓存LLM响应"""
        cache_key = self.generate_llm_cache_key(request.prompt, request.config)
        
        # 存储响应
        await self.set(cache_key, response.response_text, ttl=86400)  # 24小时TTL
        
        # 记录调用统计
        await self.save_llm_call_record(request, response, cache_key)
        
        # 内存缓存大小控制
        if len(self.memory_cache) > self.max_memory_items:
            await self.evict_lru_items()
```

## 4. 缓存清理和维护

### 4.1 TTL过期清理
```python
class CacheCleaner:
    async def clean_expired_cache(self) -> None:
        """清理过期缓存"""
        expired_keys = await self.find_expired_cache_keys()
        
        for key in expired_keys:
            metadata = await self.get_cache_metadata(key)
            if metadata and metadata.file_path:
                # 删除文件
                Path(metadata.file_path).unlink(missing_ok=True)
                
            # 删除元数据
            await self.delete_cache_metadata(key)
            
            # 从内存缓存中移除
            self.memory_cache.pop(key, None)
```

### 4.2 LRU淘汰策略
```python
class LRUEviction:
    async def evict_lru_items(self, target_count: int = 100) -> None:
        """淘汰最少使用的缓存项"""
        # 获取访问统计
        access_stats = await self.get_cache_access_stats()
        
        # 按最后访问时间排序
        sorted_items = sorted(access_stats, key=lambda x: x.last_accessed)
        
        # 淘汰最旧的项目
        for item in sorted_items[:target_count]:
            await self.evict_cache_item(item.cache_key)
```

## 5. 缓存性能监控

### 5.1 性能指标
```python
class CacheStats:
    def __init__(self):
        self.hit_count: int = 0
        self.miss_count: int = 0
        self.eviction_count: int = 0
        self.total_size_bytes: int = 0
        
    @property
    def hit_rate(self) -> float:
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "hit_rate": self.hit_rate,
            "eviction_count": self.eviction_count,
            "total_size_mb": self.total_size_bytes / (1024 * 1024)
        }
```

### 5.2 监控报告
```python
class CacheMonitor:
    async def generate_cache_report(self) -> Dict[str, Any]:
        """生成缓存使用报告"""
        return {
            "cache_stats": self.cache_stats.to_dict(),
            "cache_distribution": await self.get_cache_type_distribution(),
            "top_accessed_items": await self.get_top_accessed_items(10),
            "disk_usage": await self.get_disk_usage_stats(),
            "memory_usage": len(self.memory_cache)
        }
```

## 6. 配置参数

### 6.1 缓存配置
```yaml
cache:
  # 基础配置
  cache_dir: "data/cache"
  max_memory_items: 1000
  
  # TTL配置（秒）
  ttl:
    raw_files: 604800      # 7天
    converted_files: 259200 # 3天
    llm_responses: 86400    # 1天
    metadata: 2592000      # 30天
  
  # 清理配置
  cleanup:
    interval_hours: 6
    max_disk_usage_gb: 10
    keep_recent_days: 7
  
  # 性能配置
  performance:
    max_file_size_mb: 100
    compression_enabled: true
    async_write: true
```

### 6.2 实现要点
- 所有文件操作使用异步IO（aiofiles）
- 缓存键值使用SHA256哈希确保唯一性
- 支持内容压缩减少存储空间
- 实现优雅的错误处理和恢复机制
- 提供详细的性能监控和统计信息
