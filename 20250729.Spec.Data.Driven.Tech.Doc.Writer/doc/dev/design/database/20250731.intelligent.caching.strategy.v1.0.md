# 二层智能缓存策略设计

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31
- **相关PRD**: 20250731.multi.agent.tech.research.system.prd.v1.0.md

## 1. 缓存策略概述

### 1.1 设计目标
- **避免重复网络请求**: 所有网络获取的内容都持久化缓存
- **避免重复格式转换**: 所有抽取转换的内容都持久化缓存
- **性能提升**: 响应时间减少 50%+
- **成本控制**: LLM token消耗减少 50%+，网络请求减少 70%+
- **用户体验**: 常见查询秒级响应
- **系统稳定**: 减少外部依赖，提高可用性

### 1.2 二层缓存架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        应用层                                    │
│              统一缓存接口 (CacheManager)                          │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
┌───────────────▼─────┐ ┌──────▼──────────────────┐
│   内存缓存层 (L1)    │ │   文件系统缓存层 (L2)     │
│     Redis           │ │     Local Files         │
├─────────────────────┤ ├─────────────────────────┤
│• LLM响应结果        │ │• 原始网络文件            │
│• API响应元数据      │ │  - HTML页面             │
│• 用户会话数据       │ │  - PDF文档              │
│• 系统配置信息       │ │  - 图片/视频            │
│• 热点查询结果       │ │  - JSON/XML数据         │
│• 临时计算结果       │ │• 转换后文件             │
│                     │ │  - Markdown文档         │
│                     │ │  - 提取的纯文本         │
│                     │ │  - 结构化数据           │
└─────────────────────┘ └─────────────────────────┘
        │                           │
        └───────────┬───────────────┘
                    │
        ┌───────────▼──────────┐
        │     缓存管理器        │
        │  • 访问频率统计      │
        │  • TTL管理          │
        │  • 自动清理         │
        │  • 性能监控         │
        └─────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                    PostgreSQL 数据库                            │
│                   (业务数据，非缓存)                             │
├─────────────────────────────────────────────────────────────────┤
│• 用户数据           │• 项目数据           │• 系统配置           │
│• 调研任务记录       │• 工作流状态         │• 访问日志           │
│• 文档元数据         │• 用户行为统计       │• 性能指标           │
└─────────────────────────────────────────────────────────────────┘
```

### 1.3 缓存策略原则

#### 1.3.1 所有网络内容必须缓存
- **原始文件缓存**: 从网络获取的所有文件都保存到文件系统
  - HTML页面、PDF文档、图片、视频等
  - 保持原始格式，避免重复下载
- **API响应缓存**: 所有API调用结果都缓存
  - 学术数据库查询结果
  - 搜索引擎返回结果
  - 第三方服务响应

#### 1.3.2 所有转换内容必须缓存
- **格式转换结果**: 从原始文件转换的所有格式都保存
  - HTML转Markdown
  - PDF提取的纯文本
  - 图片OCR识别文本
- **处理结果缓存**: 所有数据处理结果都保存
  - 内容清理和标准化结果
  - 关键词提取结果
  - 摘要生成结果

#### 1.3.3 内存缓存热点数据
- **高频访问数据**: 经常访问的数据缓存到内存
  - LLM响应结果
  - 用户会话状态
  - 系统配置信息
- **临时计算结果**: 计算密集型操作结果缓存
  - 语义相似度计算结果
  - 文本分析结果

### 1.4 实现优先级
- **P0 (Sprint 1-2)**: 二层缓存基础架构 + 文件系统缓存
- **P1 (Sprint 3-4)**: LLM响应缓存 + 智能缓存管理
- **P2 (Sprint 5-6)**: 预缓存 + 性能优化

## 2. LLM调用缓存策略

### 2.1 缓存粒度设计

#### 2.1.1 智能体级别缓存 (P0 - Sprint 1)
**缓存键设计**:
```python
cache_key = f"llm:{agent_type}:{hash(input_content)}:{model_version}"

# 示例
"llm:requirement_analyst:a1b2c3d4:gpt-4-1106"
"llm:content_analyzer:e5f6g7h8:claude-3-sonnet"
```

**缓存内容**:
- LLM原始响应
- 结构化解析结果
- 置信度评分
- 生成时间戳

#### 2.1.2 任务级别缓存 (P1 - Sprint 3)
**缓存键设计**:
```python
cache_key = f"task:{task_type}:{hash(normalized_input)}:{version}"

# 示例
"task:intent_analysis:x9y8z7w6:v1.0"
"task:keyword_extraction:m5n4o3p2:v1.0"
```

### 2.2 缓存失效策略

#### 2.2.1 统一TTL配置
```yaml
# 缓存TTL配置 (秒)
cache_ttl:
  # LLM响应缓存
  llm_responses: 86400        # 24小时
  requirement_analysis: 86400 # 24小时
  content_analysis: 21600     # 6小时
  keyword_extraction: 43200   # 12小时
  document_generation: 3600   # 1小时

  # 网络内容缓存
  web_content: 21600          # 6小时
  api_responses: 43200        # 12小时
  search_results: 21600       # 6小时

  # 用户会话缓存
  user_sessions: 7200         # 2小时
  system_config: 3600         # 1小时

  # 默认TTL
  default: 3600               # 1小时
```

#### 2.2.2 热点数据判断标准
```yaml
# 热点数据阈值配置
hot_data_thresholds:
  access_count_threshold: 5   # 访问次数超过5次
  access_frequency: 3600      # 1小时内访问频率
  cache_to_memory_types:      # 优先缓存到内存的类型
    - llm_response
    - api_metadata
    - user_session
    - system_config
```

#### 2.2.3 语义相似度阈值
```yaml
# 语义相似度配置
similarity_thresholds:
  exact_match: 1.0           # 精确匹配
  semantic_match: 0.95       # 语义匹配
  fuzzy_match: 0.85          # 模糊匹配
  min_threshold: 0.80        # 最低阈值
```

#### 2.2.4 版本失效
- 模型版本更新时自动失效
- 智能体逻辑更新时失效
- 用户反馈导致的主动失效

#### 2.2.5 内容失效
- 输入内容相似度 < 0.9 时失效
- 上下文变化时失效

### 2.3 缓存命中优化

#### 2.3.1 输入标准化 (P0 - Sprint 1)
```python
class InputNormalizer:
    def normalize_text(self, text: str) -> str:
        # 1. 去除多余空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 2. 统一标点符号
        text = self.normalize_punctuation(text)
        
        # 3. 同义词替换
        text = self.replace_synonyms(text)
        
        # 4. 大小写标准化
        text = text.lower()
        
        return text
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        # 使用语义相似度计算
        return self.semantic_similarity(text1, text2)
```

#### 2.3.2 语义相似度匹配 (P1 - Sprint 3)
- 使用embedding计算输入相似度
- 相似度 > 0.95 直接命中缓存
- 相似度 0.85-0.95 提示用户确认
- 相似度 < 0.85 重新计算

## 4. 文件系统缓存策略 (L2)

### 4.1 缓存目录结构设计

#### 4.1.1 目录组织结构 (P0 - Sprint 2)
```
cache/
├── raw/                       # 原始网络文件
│   ├── html/                  # HTML页面
│   │   ├── 2025/01/           # 按年月分目录
│   │   └── domains/           # 按域名分目录
│   ├── pdf/                   # PDF文档
│   ├── images/                # 图片文件
│   ├── media/                 # 音视频文件
│   ├── json/                  # API响应JSON
│   └── xml/                   # XML数据
├── processed/                 # 转换后文件
│   ├── markdown/              # 转换的Markdown
│   │   ├── academic/          # 学术论文
│   │   ├── news/              # 新闻文章
│   │   └── general/           # 通用内容
│   ├── text/                  # 提取的纯文本
│   ├── structured/            # 结构化数据
│   └── summaries/             # 摘要文件
├── temp/                      # 临时文件
└── index/                     # 本地索引文件
```

#### 4.1.2 索引文件格式 (P0 - Sprint 2)
```json
{
  "key": "https://example.com/article.html",
  "file_path": "cache/raw/html/2025/01/a1b2c3d4.html",
  "content_type": "text/html",
  "file_size": 102400,
  "created_at": "2025-01-31T10:00:00Z",
  "last_accessed": "2025-01-31T15:30:00Z",
  "access_count": 5,
  "metadata": {
    "source": "web_scraper",
    "encoding": "utf-8",
    "language": "zh-CN"
  }
}
```

### 4.2 文件系统缓存管理

#### 4.2.1 文件索引管理 (P0 - Sprint 2)
```python
class FileIndexManager:
    def __init__(self):
        self.index_path = "cache/index"
        self.ensure_index_directory()

    def ensure_index_directory(self):
        """确保索引目录存在"""
        os.makedirs(self.index_path, exist_ok=True)

    async def store_file_record(self, key: str, file_info: Dict):
        """存储文件索引记录"""
        index_file = self.get_index_file_path(key)

        index_data = {
            'key': key,
            'file_path': file_info['file_path'],
            'content_type': file_info['content_type'],
            'file_size': file_info['file_size'],
            'created_at': datetime.now().isoformat(),
            'last_accessed': datetime.now().isoformat(),
            'access_count': 1,
            'metadata': file_info.get('metadata', {})
        }

        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, ensure_ascii=False, indent=2)

    async def get_file_record(self, key: str) -> Optional[Dict]:
        """获取文件索引记录"""
        index_file = self.get_index_file_path(key)

        if os.path.exists(index_file):
            with open(index_file, 'r', encoding='utf-8') as f:
                index_data = json.load(f)

            # 更新访问时间和计数
            await self.update_access_time(key)
            return index_data

        return None

    async def update_access_time(self, key: str):
        """更新访问时间和计数"""
        index_file = self.get_index_file_path(key)

        if os.path.exists(index_file):
            with open(index_file, 'r', encoding='utf-8') as f:
                index_data = json.load(f)

            index_data['last_accessed'] = datetime.now().isoformat()
            index_data['access_count'] = index_data.get('access_count', 0) + 1

            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, ensure_ascii=False, indent=2)

    def get_index_file_path(self, key: str) -> str:
        """生成索引文件路径"""
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return os.path.join(self.index_path, f"{key_hash}.json")

    async def delete_file_record(self, key: str):
        """删除文件索引记录"""
        index_file = self.get_index_file_path(key)
        if os.path.exists(index_file):
            os.remove(index_file)

    async def get_all_file_records(self) -> List[Dict]:
        """获取所有文件记录"""
        records = []

        for index_file in os.listdir(self.index_path):
            if index_file.endswith('.json'):
                index_path = os.path.join(self.index_path, index_file)
                with open(index_path, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                records.append(index_data)

        return records
```

#### 4.2.2 缓存统计管理 (P1 - Sprint 3)
```python
class CacheStatisticsManager:
    def __init__(self):
        self.index_manager = FileIndexManager()

    async def get_cache_statistics(self) -> Dict:
        """获取缓存统计信息"""
        all_records = await self.index_manager.get_all_file_records()

        total_files = len(all_records)
        total_size = sum(record['file_size'] for record in all_records)

        # 按内容类型统计
        size_by_type = {}
        count_by_type = {}

        for record in all_records:
            content_type = record['content_type']
            size_by_type[content_type] = size_by_type.get(content_type, 0) + record['file_size']
            count_by_type[content_type] = count_by_type.get(content_type, 0) + 1

        # 访问统计
        total_access_count = sum(record.get('access_count', 0) for record in all_records)
        avg_access_count = total_access_count / total_files if total_files > 0 else 0

        # 时间统计
        if all_records:
            created_times = [record['created_at'] for record in all_records]
            oldest_file = min(created_times)
            newest_file = max(created_times)
        else:
            oldest_file = newest_file = None

        return {
            'total_files': total_files,
            'total_size': total_size,
            'size_by_type': size_by_type,
            'count_by_type': count_by_type,
            'total_access_count': total_access_count,
            'avg_access_count': avg_access_count,
            'oldest_file': oldest_file,
            'newest_file': newest_file
        }

    async def get_hot_files(self, limit: int = 10) -> List[Dict]:
        """获取热门文件列表"""
        all_records = await self.index_manager.get_all_file_records()

        # 按访问次数排序
        hot_files = sorted(all_records,
                          key=lambda x: x.get('access_count', 0),
                          reverse=True)

        return hot_files[:limit]

    async def get_large_files(self, min_size: int = 1024*1024) -> List[Dict]:
        """获取大文件列表"""
        all_records = await self.index_manager.get_all_file_records()

        large_files = [record for record in all_records
                      if record['file_size'] >= min_size]

        # 按文件大小排序
        large_files.sort(key=lambda x: x['file_size'], reverse=True)

        return large_files
```

### 4.3 缓存清理和维护

#### 4.3.1 过期数据清理 (P1 - Sprint 4)
```python
class CacheMaintenanceManager:
    def __init__(self):
        self.index_manager = FileIndexManager()
        self.cache_base_path = "cache"

    async def cleanup_expired_cache(self):
        """清理过期的缓存文件"""
        all_records = await self.index_manager.get_all_file_records()
        current_time = datetime.now()

        for record in all_records:
            # 检查文件是否过期（超过30天未访问）
            last_accessed = datetime.fromisoformat(record['last_accessed'])
            if (current_time - last_accessed).days > 30:
                # 删除实际文件
                if os.path.exists(record['file_path']):
                    os.remove(record['file_path'])

                # 删除索引记录
                await self.index_manager.delete_file_record(record['key'])

    async def cleanup_temp_files(self):
        """清理临时文件"""
        temp_path = os.path.join(self.cache_base_path, "temp")
        if os.path.exists(temp_path):
            current_time = time.time()
            cutoff_time = current_time - 24 * 3600  # 24小时前

            for root, dirs, files in os.walk(temp_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.getmtime(file_path) < cutoff_time:
                        os.remove(file_path)

    async def cleanup_least_used_files(self, target_size: int):
        # 按访问频率清理文件，直到达到目标大小
        query = """
        SELECT url, file_path, file_size
        FROM file_cache_index
        ORDER BY last_accessed ASC, access_count ASC
        """

        files = await self.db.fetch(query)
        current_size = sum(f['file_size'] for f in files)

        for file_record in files:
            if current_size <= target_size:
                break

            # 删除文件
            if os.path.exists(file_record['file_path']):
                os.remove(file_record['file_path'])
                current_size -= file_record['file_size']

            # 删除数据库记录
            await self.db.execute(
                "DELETE FROM file_cache_index WHERE url = $1",
                file_record['url']
            )
```

## 3. 文件系统缓存策略 (L2)

### 3.1 文件存储结构设计

#### 3.1.1 目录结构 (P0 - Sprint 1)
```
cache/
├── raw/                       # 原始网络文件
│   ├── html/                  # HTML页面
│   │   ├── 2025/01/           # 按年月分目录
│   │   └── domains/           # 按域名分目录
│   ├── pdf/                   # PDF文档
│   ├── images/                # 图片文件
│   ├── media/                 # 音视频文件
│   ├── json/                  # API响应JSON
│   └── xml/                   # XML数据
├── processed/                 # 转换后文件
│   ├── markdown/              # 转换的Markdown
│   │   ├── academic/          # 学术论文
│   │   ├── news/              # 新闻文章
│   │   └── general/           # 通用内容
│   ├── text/                  # 提取的纯文本
│   ├── structured/            # 结构化数据
│   └── summaries/             # 摘要文件
├── temp/                      # 临时文件
└── index/                     # 本地索引文件
```

#### 3.1.2 文件命名规范
```python
class FileSystemCache:
    def generate_file_path(self, content_type: str, source_url: str,
                          file_extension: str) -> str:
        # 生成文件哈希
        url_hash = hashlib.md5(source_url.encode()).hexdigest()

        # 按日期和类型组织目录
        date_path = datetime.now().strftime("%Y/%m")
        type_path = self.get_type_path(content_type)

        # 生成完整路径
        file_path = f"cache/content/{type_path}/{date_path}/{url_hash}.{file_extension}"

        return file_path

    def get_type_path(self, content_type: str) -> str:
        type_mapping = {
            'academic_paper': 'markdown/academic',
            'news_article': 'markdown/news',
            'blog_post': 'markdown/blog',
            'pdf_document': 'pdf',
            'image': 'images',
            'video': 'media'
        }
        return type_mapping.get(content_type, 'markdown/general')
```

### 3.2 大文件内容缓存

#### 3.2.1 Markdown内容缓存 (P0 - Sprint 2)
```python
class MarkdownFileCache:
    def __init__(self):
        self.base_path = "cache/processed/markdown"
        self.file_cache = FileSystemCache()

    async def cache_markdown_content(self, url: str, content: str,
                                   content_type: str, metadata: Dict) -> str:
        # 生成文件路径
        file_path = self.generate_file_path(content_type, url, 'md')

        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        # 创建索引文件
        await self.file_cache.create_file_index(
            f"{url}:markdown",
            file_path,
            content_type,
            len(content.encode('utf-8')),
            metadata
        )

        return file_path

    async def get_cached_content(self, url: str) -> Optional[str]:
        # 从索引获取文件路径
        file_path = await self.file_cache.get_file_path_from_index(f"{url}:markdown")

        if file_path and os.path.exists(file_path):
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()

        return None

    def generate_file_path(self, content_type: str, url: str, extension: str) -> str:
        """生成文件路径"""
        url_hash = hashlib.md5(url.encode()).hexdigest()
        date_path = datetime.now().strftime("%Y/%m")

        type_mapping = {
            'academic_paper': 'academic',
            'news_article': 'news',
            'blog_post': 'general',
            'technical_doc': 'general'
        }

        type_dir = type_mapping.get(content_type, 'general')

        return f"{self.base_path}/{type_dir}/{date_path}/{url_hash}.{extension}"
```

#### 3.2.2 PDF文档缓存 (P1 - Sprint 3)
```python
class PDFFileCache:
    def __init__(self):
        self.base_path = "cache/raw/pdf"
        self.processed_path = "cache/processed/text"
        self.file_cache = FileSystemCache()

    async def cache_pdf_file(self, url: str, pdf_data: bytes,
                           extracted_text: str, metadata: Dict) -> Dict[str, str]:
        # 生成文件路径
        pdf_path = self.generate_file_path('pdf', url, 'pdf')
        text_path = self.generate_processed_path('text', url, 'txt')

        # 确保目录存在
        os.makedirs(os.path.dirname(pdf_path), exist_ok=True)
        os.makedirs(os.path.dirname(text_path), exist_ok=True)

        # 保存PDF原文件
        with open(pdf_path, 'wb') as f:
            f.write(pdf_data)

        # 保存提取的文本
        with open(text_path, 'w', encoding='utf-8') as f:
            f.write(extracted_text)

        # 创建原始文件索引
        await self.file_cache.create_file_index(
            url, pdf_path, 'application/pdf', len(pdf_data), metadata
        )

        # 创建转换文件索引
        await self.file_cache.create_file_index(
            f"{url}:text", text_path, 'text/plain',
            len(extracted_text.encode('utf-8')), metadata
        )

        return {
            'pdf_path': pdf_path,
            'text_path': text_path
        }

    def generate_file_path(self, content_type: str, url: str, extension: str) -> str:
        """生成原始文件路径"""
        url_hash = hashlib.md5(url.encode()).hexdigest()
        date_path = datetime.now().strftime("%Y/%m")
        return f"{self.base_path}/{date_path}/{url_hash}.{extension}"

    def generate_processed_path(self, content_type: str, url: str, extension: str) -> str:
        """生成转换文件路径"""
        url_hash = hashlib.md5(url.encode()).hexdigest()
        date_path = datetime.now().strftime("%Y/%m")
        return f"{self.processed_path}/{date_path}/{url_hash}.{extension}"
```

### 3.3 文件系统缓存管理

#### 3.3.1 缓存清理策略 (P1 - Sprint 4)
```python
class FileSystemCacheManager:
    def __init__(self):
        self.max_cache_size = 10 * 1024 * 1024 * 1024  # 10GB
        self.cache_base_path = "cache"
        self.index_path = "cache/index"

    async def cleanup_old_files(self):
        # 获取所有索引文件，按访问时间排序
        index_files = []
        for index_file in os.listdir(self.index_path):
            if index_file.endswith('.json'):
                index_path = os.path.join(self.index_path, index_file)
                with open(index_path, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                index_files.append(index_data)

        # 按最后访问时间排序
        index_files.sort(key=lambda x: x['last_accessed'])

        # 计算总大小
        total_size = sum(record['file_size'] for record in index_files)

        if total_size > self.max_cache_size:
            # 删除最久未访问的文件
            for record in index_files:
                if total_size <= self.max_cache_size * 0.8:  # 清理到80%
                    break

                # 删除实际文件
                if os.path.exists(record['file_path']):
                    os.remove(record['file_path'])
                    total_size -= record['file_size']

                # 删除索引文件
                index_file_path = f"{self.index_path}/{hashlib.md5(record['key'].encode()).hexdigest()}.json"
                if os.path.exists(index_file_path):
                    os.remove(index_file_path)

    async def get_cache_statistics(self) -> Dict:
        total_files = 0
        total_size = 0
        size_by_type = {}
        oldest_file = None
        newest_file = None

        # 遍历所有索引文件
        for index_file in os.listdir(self.index_path):
            if index_file.endswith('.json'):
                index_path = os.path.join(self.index_path, index_file)
                with open(index_path, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)

                total_files += 1
                total_size += index_data['file_size']

                content_type = index_data['content_type']
                size_by_type[content_type] = size_by_type.get(content_type, 0) + index_data['file_size']

                created_at = index_data['created_at']
                if oldest_file is None or created_at < oldest_file:
                    oldest_file = created_at
                if newest_file is None or created_at > newest_file:
                    newest_file = created_at

        return {
            'total_files': total_files,
            'total_size': total_size,
            'size_by_type': size_by_type,
            'oldest_file': oldest_file,
            'newest_file': newest_file
        }

    async def compress_old_files(self):
        """压缩旧文件以节省空间"""
        cutoff_time = datetime.now() - timedelta(days=7)

        for root, dirs, files in os.walk(self.cache_base_path):
            for file in files:
                file_path = os.path.join(root, file)
                if os.path.getmtime(file_path) < cutoff_time.timestamp():
                    # 压缩大于1MB的文件
                    if os.path.getsize(file_path) > 1024 * 1024:
                        await self.compress_file(file_path)

    async def compress_file(self, file_path: str):
        """压缩单个文件"""
        import gzip

        compressed_path = f"{file_path}.gz"

        with open(file_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                f_out.writelines(f_in)

        # 删除原文件
        os.remove(file_path)

        # 更新索引中的文件路径
        # 这里需要更新相应的索引文件
```

### 3.4 跨层缓存协调

#### 3.4.1 统一缓存接口 (P0 - Sprint 2)
```python
class TwoLayerCacheManager:
    def __init__(self):
        self.memory_cache = RedisCache()        # L1: Redis
        self.file_cache = FileSystemCache()     # L2: Local Files

    async def get_content(self, url: str) -> Optional[str]:
        # L1: 尝试从内存缓存获取热点数据
        content = await self.memory_cache.get(url)
        if content:
            return content

        # L2: 从文件系统缓存获取
        content = await self.file_cache.get_raw_file(url)
        if content:
            # 如果是热点数据，缓存到内存
            if await self.is_hot_content(url):
                await self.memory_cache.set(url, content, ttl=3600)
            return content

        return None

    async def store_content(self, url: str, content: str,
                          content_type: str, metadata: Dict = None):
        # 所有内容都存储到文件系统
        file_path = await self.file_cache.store_raw_file(url, content, content_type)

        # 热点数据同时缓存到内存
        if await self.should_cache_to_memory(url, content_type):
            await self.memory_cache.set(url, content, ttl=3600)

        return file_path

    async def store_processed_file(self, source_url: str, content: str,
                                 process_type: str) -> str:
        """存储转换后的文件"""
        return await self.file_cache.store_processed_file(
            source_url, content, process_type
        )

    async def is_hot_content(self, url: str) -> bool:
        """判断是否为热点内容"""
        # 基于访问频率判断 (使用统一配置)
        access_count = await self.memory_cache.get(f"access_count:{url}")
        threshold = self.config.get('hot_data_thresholds', {}).get('access_count_threshold', 5)
        return access_count and int(access_count) > threshold

    async def should_cache_to_memory(self, url: str, content_type: str) -> bool:
        """判断是否应该缓存到内存"""
        # 使用统一配置的内存缓存类型
        memory_types = self.config.get('hot_data_thresholds', {}).get('cache_to_memory_types', [
            'llm_response', 'api_metadata', 'user_session', 'system_config'
        ])
        return content_type in memory_types

    def get_ttl(self, content_type: str) -> int:
        """获取内容类型对应的TTL"""
        ttl_config = self.config.get('cache_ttl', {})
        return ttl_config.get(content_type, ttl_config.get('default', 3600))
```

## 4. 调研结果缓存策略

### 4.1 中间结果缓存 (P1 - Sprint 3)

#### 4.1.1 需求分析结果缓存
```python
class RequirementAnalysisCache:
    def cache_analysis_result(self, user_input: str, result: RequirementAnalysisResult):
        cache_key = f"req_analysis:{hash(user_input)}"
        
        # 缓存24小时
        self.redis_client.setex(
            cache_key,
            24 * 3600,
            result.to_json()
        )
```

#### 4.1.2 调研任务结果缓存
```python
class ResearchTaskCache:
    def cache_task_result(self, task_id: str, result: ResearchTaskResult):
        cache_key = f"research_task:{task_id}"
        
        # 根据任务类型设置不同TTL
        ttl = self.get_ttl_by_task_type(result.task_type)
        
        self.redis_client.setex(cache_key, ttl, result.to_json())
```

### 4.2 最终报告缓存 (P1 - Sprint 4)

#### 4.2.1 报告版本缓存
```python
class ReportCache:
    def cache_report_version(self, report_id: str, version: str, content: str):
        cache_key = f"report:{report_id}:{version}"
        
        # 报告缓存7天
        self.redis_client.setex(cache_key, 7 * 24 * 3600, content)
        
        # 同时存储到持久化存储
        self.db.store_report_cache(cache_key, content)
```

## 5. 缓存预热策略

### 5.1 热门主题预缓存 (P2 - Sprint 5)

#### 5.1.1 主题识别
```python
class TopicPreloader:
    def identify_hot_topics(self) -> List[str]:
        # 基于用户查询历史识别热门主题
        query_stats = self.analyze_query_patterns()
        
        # 提取高频关键词和主题
        hot_topics = self.extract_trending_topics(query_stats)
        
        return hot_topics
    
    async def preload_topic_data(self, topic: str):
        # 预加载基础搜索结果
        await self.preload_search_results(topic)
        
        # 预加载学术论文
        await self.preload_academic_papers(topic)
        
        # 预生成基础分析结果
        await self.preload_analysis_results(topic)
```

### 5.2 用户行为预测 (P2 - Sprint 6)

#### 5.2.1 个性化预缓存
```python
class PersonalizedPreloader:
    def predict_user_interests(self, user_id: str) -> List[str]:
        # 基于用户历史行为预测兴趣
        user_history = self.get_user_history(user_id)
        
        # 使用机器学习模型预测
        predicted_topics = self.ml_model.predict_interests(user_history)
        
        return predicted_topics
```

## 6. 缓存管理和监控

### 6.1 缓存监控指标 (P1 - Sprint 3)

#### 6.1.1 性能指标
```python
class CacheMetrics:
    def __init__(self):
        self.metrics = {
            'hit_rate': 0.0,
            'miss_rate': 0.0,
            'avg_response_time': 0.0,
            'cache_size': 0,
            'eviction_count': 0
        }
    
    def record_cache_hit(self, cache_type: str, response_time: float):
        # 记录缓存命中
        self.update_hit_rate(cache_type)
        self.update_response_time(cache_type, response_time)
    
    def record_cache_miss(self, cache_type: str, response_time: float):
        # 记录缓存未命中
        self.update_miss_rate(cache_type)
        self.update_response_time(cache_type, response_time)
```

#### 6.1.2 监控告警
- 缓存命中率 < 40% 告警
- 缓存响应时间 > 100ms 告警
- 缓存空间使用率 > 80% 告警
- 缓存失效率异常告警

### 6.2 缓存清理策略 (P1 - Sprint 4)

#### 6.2.1 LRU + TTL 混合策略
```python
class CacheEvictionPolicy:
    def __init__(self):
        self.policies = {
            'llm_responses': 'LRU',
            'web_content': 'TTL',
            'api_responses': 'LRU_TTL',
            'user_sessions': 'TTL'
        }
    
    def should_evict(self, cache_item: CacheItem) -> bool:
        policy = self.policies.get(cache_item.type)
        
        if policy == 'TTL':
            return self.is_expired(cache_item)
        elif policy == 'LRU':
            return self.is_least_recently_used(cache_item)
        elif policy == 'LRU_TTL':
            return self.is_expired(cache_item) or self.is_least_recently_used(cache_item)
```

## 7. 实施计划

### 7.1 Phase 1: 二层缓存基础架构 (Sprint 1-2)
**目标**: 建立完整的二层缓存基础设施
**交付物**:
- Redis内存缓存基础设施 (L1)
- 文件系统缓存目录结构 (L2)
- 统一缓存接口和管理逻辑
- 原始文件缓存系统
- LLM响应缓存
- 基础性能监控

### 7.2 Phase 2: 转换文件缓存和智能管理 (Sprint 3-4)
**目标**: 完善转换文件缓存和智能缓存管理
**交付物**:
- 转换文件缓存系统
- 智能热点数据识别
- 缓存清理和维护机制
- 语义相似度匹配
- 缓存监控和统计系统

### 7.3 Phase 3: 高级优化和维护 (Sprint 5-6)
**目标**: 实现预缓存和自动维护
**交付物**:
- 热门主题预缓存
- 自动缓存清理和维护
- 缓存性能优化
- 完整监控告警
- 用户行为分析和个性化缓存

## 8. 技术实现

### 8.1 缓存架构代码示例
```python
class IntelligentCacheManager:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        self.file_cache = FileSystemCache()
        self.normalizer = InputNormalizer()
        self.metrics = CacheMetrics()
    
    async def get_or_compute(self, 
                           cache_key: str, 
                           compute_func: Callable,
                           ttl: int = 3600,
                           similarity_threshold: float = 0.95) -> Any:
        
        # 1. 尝试精确匹配
        cached_result = await self.get_exact_match(cache_key)
        if cached_result:
            self.metrics.record_cache_hit('exact', 0.001)
            return cached_result
        
        # 2. 尝试语义相似匹配
        similar_result = await self.get_similar_match(cache_key, similarity_threshold)
        if similar_result:
            self.metrics.record_cache_hit('similar', 0.005)
            return similar_result
        
        # 3. 计算新结果并缓存
        start_time = time.time()
        result = await compute_func()
        compute_time = time.time() - start_time
        
        await self.cache_result(cache_key, result, ttl)
        self.metrics.record_cache_miss('compute', compute_time)
        
        return result
```

### 8.2 缓存配置管理
```python
class CacheConfig:
    def __init__(self):
        self.config = {
            'redis': {
                'host': 'localhost',
                'port': 6379,
                'max_connections': 100
            },
            'ttl_settings': {
                'llm_responses': 24 * 3600,
                'web_content': 6 * 3600,
                'api_responses': 12 * 3600
            },
            'size_limits': {
                'max_memory': '2GB',
                'max_items': 100000
            }
        }
```

## 9. 性能预期

### 9.1 缓存命中率目标
- **LLM调用缓存**: 60%+ (相同/相似查询)
- **数据获取缓存**: 40%+ (重复URL/API调用)
- **调研结果缓存**: 30%+ (相似主题调研)

### 9.2 性能提升预期
- **响应时间**: 减少 50%+
- **Token消耗**: 减少 50%+
- **网络请求**: 减少 70%+
- **系统负载**: 减少 40%+

### 9.3 成本节约预期
- **LLM API成本**: 节约 50%+
- **第三方API成本**: 节约 60%+
- **服务器资源**: 节约 30%+
- **网络带宽**: 节约 40%+
