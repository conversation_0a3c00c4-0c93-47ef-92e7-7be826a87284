# 二层缓存架构设计

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31
- **相关文档**: 20250731.intelligent.caching.strategy.v1.0.md

## 1. 架构概述

### 1.1 设计原则
- **避免重复网络请求**: 所有网络获取的内容都持久化到文件系统
- **避免重复格式转换**: 所有转换结果都持久化到文件系统
- **热点数据内存缓存**: 高频访问数据缓存到内存提升性能
- **统一接口**: 透明的缓存访问，自动管理两层缓存
- **简化架构**: 减少复杂性，提高可维护性

### 1.2 二层架构设计

```
┌─────────────────────────────────────────────────────────────────┐
│                        应用层                                    │
│                 统一缓存接口 (CacheManager)                       │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
┌───────────────▼─────┐ ┌──────▼──────────────────┐
│   L1: 内存缓存层     │ │   L2: 文件系统缓存层     │
│     Redis           │ │     Local Files         │
├─────────────────────┤ ├─────────────────────────┤
│• LLM响应结果        │ │• 原始网络文件            │
│• API响应元数据      │ │  - HTML页面             │
│• 用户会话数据       │ │  - PDF文档              │
│• 系统配置信息       │ │  - 图片/视频            │
│• 热点查询结果       │ │  - JSON/XML数据         │
│• 临时计算结果       │ │• 转换后文件             │
│                     │ │  - Markdown文档         │
│                     │ │  - 提取的纯文本         │
│                     │ │  - 结构化数据           │
└─────────────────────┘ └─────────────────────────┘
```

## 2. 各层详细设计

### 2.1 L1: 内存缓存层 (Redis)

#### 2.1.1 存储对象
- **LLM响应结果**: JSON格式，快速访问
- **API响应元数据**: 搜索结果列表、论文信息
- **用户会话数据**: 当前用户状态和偏好
- **系统配置信息**: 热门关键词、系统设置
- **热点查询结果**: 高频访问的计算结果
- **临时计算结果**: 语义相似度、文本分析结果

#### 2.1.2 键命名规范
```
格式: {类型}:{子类型}:{标识符}

示例:
- llm:requirement_analyst:a1b2c3d4
- api:arxiv_search:e5f6g7h8
- session:user_12345:current
- config:hot_keywords:latest
- temp:similarity:x9y8z7w6
```

#### 2.1.3 TTL策略
```python
REDIS_TTL_CONFIG = {
    'llm_response': 24 * 3600,      # 24小时
    'api_metadata': 6 * 3600,       # 6小时
    'user_session': 2 * 3600,       # 2小时
    'system_config': 12 * 3600,     # 12小时
    'hot_query': 1 * 3600,          # 1小时
    'temp_result': 30 * 60          # 30分钟
}
```

### 2.2 L2: 文件系统缓存层

#### 2.2.1 目录结构设计
```
cache/
├── raw/                       # 原始网络文件
│   ├── html/                  # HTML页面
│   │   ├── 2025/01/           # 按年月分目录
│   │   └── domains/           # 按域名分目录
│   ├── pdf/                   # PDF文档
│   ├── images/                # 图片文件
│   ├── media/                 # 音视频文件
│   ├── json/                  # API响应JSON
│   └── xml/                   # XML数据
├── processed/                 # 转换后文件
│   ├── markdown/              # 转换的Markdown
│   │   ├── academic/          # 学术论文
│   │   ├── news/              # 新闻文章
│   │   └── general/           # 通用内容
│   ├── text/                  # 提取的纯文本
│   ├── structured/            # 结构化数据
│   └── summaries/             # 摘要文件
├── temp/                      # 临时文件
└── index/                     # 本地索引文件
```

#### 2.2.2 文件命名规范
```python
def generate_file_path(url: str, content_type: str, is_raw: bool = True) -> str:
    """
    生成文件路径
    原始文件: cache/raw/{type}/{date}/{url_hash}.{ext}
    转换文件: cache/processed/{type}/{date}/{url_hash}_{process_type}.{ext}
    """
    url_hash = hashlib.md5(url.encode()).hexdigest()
    date_path = datetime.now().strftime("%Y/%m")
    
    if is_raw:
        type_mapping = {
            'html': 'html',
            'pdf': 'pdf',
            'image': 'images',
            'video': 'media',
            'json': 'json',
            'xml': 'xml'
        }
        type_dir = type_mapping.get(content_type, 'html')
        return f"cache/raw/{type_dir}/{date_path}/{url_hash}.{get_extension(content_type)}"
    else:
        type_mapping = {
            'markdown': 'markdown/general',
            'academic_markdown': 'markdown/academic',
            'news_markdown': 'markdown/news',
            'extracted_text': 'text',
            'structured_data': 'structured',
            'summary': 'summaries'
        }
        type_dir = type_mapping.get(content_type, 'markdown/general')
        return f"cache/processed/{type_dir}/{date_path}/{url_hash}.{get_extension(content_type)}"
```

## 3. 统一缓存接口设计

### 3.1 核心接口定义
```python
class TwoLayerCacheManager:
    """二层缓存管理器 - 统一缓存入口"""
    
    def __init__(self):
        self.memory_cache = RedisCache()          # L1: 内存缓存
        self.file_cache = FileSystemCache()       # L2: 文件系统缓存
        self.monitor = CacheMonitor()             # 性能监控
    
    async def get_raw_file(self, url: str) -> Optional[bytes]:
        """获取原始网络文件"""
        # 直接从文件系统获取原始文件
        return await self.file_cache.get_raw_file(url)
    
    async def store_raw_file(self, url: str, content: bytes, 
                           content_type: str) -> str:
        """存储原始网络文件"""
        return await self.file_cache.store_raw_file(url, content, content_type)
    
    async def get_processed_file(self, source_url: str, 
                               process_type: str) -> Optional[str]:
        """获取转换后的文件"""
        return await self.file_cache.get_processed_file(source_url, process_type)
    
    async def store_processed_file(self, source_url: str, content: str,
                                 process_type: str) -> str:
        """存储转换后的文件"""
        return await self.file_cache.store_processed_file(
            source_url, content, process_type
        )
    
    async def get_hot_data(self, key: str) -> Optional[Any]:
        """获取热点数据（优先从内存）"""
        # L1: 尝试从内存缓存获取
        result = await self.memory_cache.get(key)
        if result is not None:
            self.monitor.record_hit('L1')
            return result
        
        # L2: 从文件系统获取并缓存到内存
        content = await self.file_cache.get_content(key)
        if content is not None:
            # 缓存到内存
            await self.memory_cache.set(key, content, 
                                      ttl=self.get_ttl(key))
            self.monitor.record_hit('L2')
            return content
        
        self.monitor.record_miss()
        return None
    
    async def store_hot_data(self, key: str, value: Any, 
                           cache_type: str = 'general') -> bool:
        """存储热点数据（同时存储到两层）"""
        try:
            # L1: 存储到内存缓存
            await self.memory_cache.set(key, value, 
                                      ttl=self.get_ttl(cache_type))
            
            # L2: 存储到文件系统（持久化）
            await self.file_cache.store_content(key, value, cache_type)
            
            self.monitor.record_store()
            return True
            
        except Exception as e:
            logger.error(f"Cache store error for key {key}: {e}")
            self.monitor.record_error()
            return False
```

### 3.2 文件系统缓存管理
```python
class FileSystemCache:
    """文件系统缓存管理器"""
    
    def __init__(self):
        self.base_path = "cache"
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保缓存目录存在"""
        directories = [
            "cache/raw/html", "cache/raw/pdf", "cache/raw/images",
            "cache/raw/media", "cache/raw/json", "cache/raw/xml",
            "cache/processed/markdown/academic",
            "cache/processed/markdown/news",
            "cache/processed/markdown/general",
            "cache/processed/text",
            "cache/processed/structured",
            "cache/processed/summaries",
            "cache/temp", "cache/index"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    async def store_raw_file(self, url: str, content: bytes, 
                           content_type: str) -> str:
        """存储原始网络文件"""
        file_path = self.generate_raw_file_path(url, content_type)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 写入文件
        with open(file_path, 'wb') as f:
            f.write(content)
        
        logger.info(f"Stored raw file: {url} -> {file_path}")
        return file_path
    
    async def store_processed_file(self, source_url: str, content: str,
                                 process_type: str) -> str:
        """存储转换后的文件"""
        file_path = self.generate_processed_file_path(source_url, process_type)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"Stored processed file: {source_url} -> {file_path}")
        return file_path
    
    async def get_raw_file(self, url: str) -> Optional[bytes]:
        """获取原始文件"""
        # 尝试不同的文件类型
        for content_type in ['html', 'pdf', 'json', 'xml']:
            file_path = self.generate_raw_file_path(url, content_type)
            if os.path.exists(file_path):
                with open(file_path, 'rb') as f:
                    return f.read()
        return None
    
    async def get_processed_file(self, source_url: str, 
                               process_type: str) -> Optional[str]:
        """获取转换后的文件"""
        file_path = self.generate_processed_file_path(source_url, process_type)
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        return None
```

## 4. 缓存策略实施

### 4.1 网络内容缓存策略
```python
class NetworkContentCacher:
    """网络内容缓存器"""
    
    async def fetch_and_cache(self, url: str) -> Optional[bytes]:
        """获取网络内容并缓存"""
        # 1. 检查是否已缓存
        cached_content = await self.cache_manager.get_raw_file(url)
        if cached_content:
            logger.info(f"Cache hit for URL: {url}")
            return cached_content
        
        # 2. 从网络获取
        try:
            content, content_type = await self.fetch_from_network(url)
            
            # 3. 缓存到文件系统
            await self.cache_manager.store_raw_file(url, content, content_type)
            
            logger.info(f"Fetched and cached: {url}")
            return content
            
        except Exception as e:
            logger.error(f"Failed to fetch {url}: {e}")
            return None
```

### 4.2 格式转换缓存策略
```python
class ContentProcessor:
    """内容处理器"""
    
    async def convert_to_markdown(self, source_url: str) -> Optional[str]:
        """转换为Markdown并缓存"""
        # 1. 检查是否已有转换结果
        cached_markdown = await self.cache_manager.get_processed_file(
            source_url, 'markdown'
        )
        if cached_markdown:
            logger.info(f"Cache hit for markdown: {source_url}")
            return cached_markdown
        
        # 2. 获取原始文件
        raw_content = await self.cache_manager.get_raw_file(source_url)
        if not raw_content:
            return None
        
        # 3. 执行转换
        try:
            markdown_content = await self.html_to_markdown(raw_content)
            
            # 4. 缓存转换结果
            await self.cache_manager.store_processed_file(
                source_url, markdown_content, 'markdown'
            )
            
            logger.info(f"Converted and cached markdown: {source_url}")
            return markdown_content
            
        except Exception as e:
            logger.error(f"Failed to convert {source_url}: {e}")
            return None
```

## 5. 性能监控和维护

### 5.1 缓存监控
```python
class CacheMonitor:
    """缓存性能监控"""
    
    def __init__(self):
        self.metrics = {
            'l1_hits': 0, 'l1_misses': 0,
            'l2_hits': 0, 'l2_misses': 0,
            'total_stores': 0,
            'error_count': 0
        }
    
    def get_hit_rate(self) -> Dict[str, float]:
        """获取缓存命中率"""
        total_l1 = self.metrics['l1_hits'] + self.metrics['l1_misses']
        total_l2 = self.metrics['l2_hits'] + self.metrics['l2_misses']
        
        return {
            'l1_hit_rate': self.metrics['l1_hits'] / total_l1 if total_l1 > 0 else 0.0,
            'l2_hit_rate': self.metrics['l2_hits'] / total_l2 if total_l2 > 0 else 0.0,
            'overall_hit_rate': (self.metrics['l1_hits'] + self.metrics['l2_hits']) / 
                               (total_l1 + total_l2) if (total_l1 + total_l2) > 0 else 0.0
        }
```

### 5.2 自动维护
```python
class CacheMaintenance:
    """缓存自动维护"""
    
    async def cleanup_old_files(self):
        """清理过期文件"""
        # 清理超过30天的临时文件
        temp_path = "cache/temp"
        cutoff_time = time.time() - 30 * 24 * 3600
        
        for root, dirs, files in os.walk(temp_path):
            for file in files:
                file_path = os.path.join(root, file)
                if os.path.getmtime(file_path) < cutoff_time:
                    os.remove(file_path)
    
    async def compress_old_files(self):
        """压缩旧文件"""
        # 压缩超过7天的大文件
        for content_type in ['pdf', 'media']:
            content_path = f"cache/raw/{content_type}"
            await self.compress_files_in_directory(content_path, days=7)
```

这个二层缓存架构设计确保了：
1. **避免重复网络请求**: 所有网络内容都持久化缓存
2. **避免重复格式转换**: 所有转换结果都持久化缓存
3. **高性能访问**: 热点数据内存缓存，快速响应
4. **简化架构**: 减少复杂性，提高可维护性
5. **统一管理**: 透明的缓存接口，自动管理两层缓存
