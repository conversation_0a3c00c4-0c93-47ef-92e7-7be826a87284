# BaseAgent 基类设计文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31
- **相关文档**:
  - [ADR-002: Sprint 1 技术决策](../../architecture/decisions/20250731.ADR-002.sprint1.technical.decisions.v1.0.md)
  - [ADR-003: LangGraph 0.6 函数式API优化集成决策](../../architecture/decisions/20250731.ADR-003.langgraph.functional.api.optimization.v1.0.md)
  - [多智能体工作流设计](../../../design/20250730.multi.agent.workflow.design.v1.0.md)

## 设计概述

BaseAgent 基类是多智能体系统的核心抽象，为所有智能体提供统一的生命周期管理、错误处理、状态管理和 LangGraph 集成能力。

### 设计目标
1. **LangGraph 0.6 完全兼容**: 支持函数式API、Command对象和新的路由机制
2. **函数式API优先**: 支持@task和@entrypoint装饰器，简化代码结构
3. **协调者-执行者模式**: 支持智能体间的高效协作
4. **异步执行**: 支持并发处理以提高系统吞吐量
5. **错误恢复**: 内置检查点机制和故障恢复能力
6. **监控集成**: 支持实时监控和性能评估
7. **向后兼容**: 同时支持StateGraph和函数式API两种模式

## 核心接口设计

### 数据模型

```python
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, Union, List, Literal, Callable
from pydantic import BaseModel, Field
from datetime import datetime
from langgraph.types import Command
from langgraph.graph import MessagesState
from langgraph.func import task, entrypoint
from langgraph.checkpoint.memory import InMemorySaver

class AgentConfig(BaseModel):
    """智能体配置"""
    agent_id: str = Field(..., description="智能体唯一标识")
    agent_type: str = Field(..., description="智能体类型")
    llm_config: Dict[str, Any] = Field(..., description="LLM 配置")
    tools: Optional[List[str]] = Field(None, description="可用工具列表")
    max_retries: int = Field(3, description="最大重试次数")
    timeout: int = Field(300, description="超时时间(秒)")
    cache_enabled: bool = Field(True, description="是否启用缓存")
    # LangGraph 0.6 函数式API配置
    use_functional_api: bool = Field(True, description="是否使用函数式API")
    checkpointer_type: str = Field("memory", description="检查点器类型: memory, redis, file")
    max_concurrency: int = Field(10, description="最大并发任务数")
    enable_send_api: bool = Field(True, description="是否启用Send API动态分发")

class AgentStatus(BaseModel):
    """智能体状态"""
    agent_id: str
    status: Literal["idle", "initializing", "running", "error", "completed"]
    current_task: Optional[str] = None
    error_message: Optional[str] = None
    start_time: Optional[datetime] = None
    last_update: datetime = Field(default_factory=datetime.utcnow)
    metrics: Dict[str, Any] = Field(default_factory=dict)

class ValidationResult(BaseModel):
    """输入验证结果"""
    is_valid: bool
    error_message: Optional[str] = None
    normalized_input: Optional[Dict[str, Any]] = None
    warnings: List[str] = Field(default_factory=list)

class CheckpointData(BaseModel):
    """检查点数据"""
    agent_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    state_snapshot: Dict[str, Any]
    execution_context: Dict[str, Any]
    metadata: Dict[str, Any] = Field(default_factory=dict)

class AgentMetrics(BaseModel):
    """智能体性能指标"""
    agent_id: str
    execution_count: int = 0
    success_count: int = 0
    error_count: int = 0
    average_execution_time: float = 0.0
    last_execution_time: Optional[datetime] = None
    cache_hit_rate: float = 0.0
```

### BaseAgent 基类

```python
class BaseAgent(ABC):
    """
    多智能体系统基础类，兼容 LangGraph 0.6
    支持协调者-执行者模式和网络架构
    """
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.status = AgentStatus(
            agent_id=config.agent_id,
            status="idle"
        )
        self.metrics = AgentMetrics(agent_id=config.agent_id)
        self._cache_manager = None
        self._logger = None
        self._initialized = False
    
    # === 核心生命周期方法 ===
    
    @abstractmethod
    async def initialize(self, config: AgentConfig) -> None:
        """
        初始化智能体
        
        Args:
            config: 智能体配置
            
        Raises:
            InitializationError: 初始化失败时抛出
        """
        pass
    
    @abstractmethod
    async def validate_input(self, state: MessagesState) -> ValidationResult:
        """
        验证输入状态
        
        Args:
            state: LangGraph 消息状态
            
        Returns:
            ValidationResult: 验证结果
        """
        pass
    
    @abstractmethod
    async def execute(self, state: MessagesState) -> Union[MessagesState, Command]:
        """
        执行智能体核心逻辑
        
        Args:
            state: LangGraph 消息状态
            
        Returns:
            Union[MessagesState, Command]: 更新后的状态或路由命令
            
        Raises:
            ExecutionError: 执行失败时抛出
        """
        pass
    
    async def post_process(self, result: Union[MessagesState, Command]) -> Union[MessagesState, Command]:
        """
        后处理结果（可选重写）
        
        Args:
            result: 执行结果
            
        Returns:
            Union[MessagesState, Command]: 处理后的结果
        """
        return result
    
    async def cleanup(self) -> None:
        """
        清理资源（可选重写）
        """
        pass
    
    # === LangGraph 兼容方法 ===
    
    async def __call__(self, state: MessagesState) -> Union[MessagesState, Command]:
        """
        LangGraph 节点调用入口
        实现标准的 LangGraph 节点接口
        
        Args:
            state: LangGraph 消息状态
            
        Returns:
            Union[MessagesState, Command]: 执行结果
        """
        try:
            # 确保智能体已初始化
            if not self._initialized:
                await self.initialize(self.config)
                self._initialized = True
            
            # 更新状态
            self.status.status = "running"
            self.status.start_time = datetime.utcnow()
            
            # 验证输入
            validation = await self.validate_input(state)
            if not validation.is_valid:
                raise ValueError(f"Input validation failed: {validation.error_message}")
            
            # 执行核心逻辑
            result = await self.execute(state)
            
            # 后处理
            final_result = await self.post_process(result)
            
            # 更新状态和指标
            self.status.status = "completed"
            self.status.last_update = datetime.utcnow()
            self._update_metrics(success=True)
            
            return final_result
            
        except Exception as e:
            self.status.status = "error"
            self.status.error_message = str(e)
            self.status.last_update = datetime.utcnow()
            self._update_metrics(success=False)
            
            return await self.handle_error(e, state)
    
    # === 错误处理和恢复 ===
    
    async def handle_error(self, error: Exception, state: MessagesState) -> Command:
        """
        处理执行错误
        
        Args:
            error: 异常对象
            state: 当前状态
            
        Returns:
            Command: 错误处理命令
        """
        error_message = f"Agent {self.config.agent_id} error: {str(error)}"
        
        # 记录错误日志
        if self._logger:
            self._logger.error(
                error_message, 
                extra={
                    "agent_id": self.config.agent_id,
                    "agent_type": self.config.agent_type,
                    "error_type": type(error).__name__
                }
            )
        
        # 返回错误处理命令
        return Command(
            goto="error_handler",  # 可以配置错误处理节点
            update={
                "messages": state.get("messages", []) + [{
                    "role": "system",
                    "content": error_message,
                    "metadata": {
                        "agent_id": self.config.agent_id,
                        "error_type": type(error).__name__,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                }],
                "error_info": {
                    "agent_id": self.config.agent_id,
                    "error_type": type(error).__name__,
                    "error_message": str(error),
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
        )
    
    async def save_checkpoint(self, state: MessagesState) -> CheckpointData:
        """
        保存检查点
        
        Args:
            state: 当前状态
            
        Returns:
            CheckpointData: 检查点数据
        """
        return CheckpointData(
            agent_id=self.config.agent_id,
            state_snapshot=state,
            execution_context={
                "status": self.status.dict(),
                "config": self.config.dict(),
                "metrics": self.metrics.dict()
            },
            metadata={
                "agent_type": self.config.agent_type,
                "checkpoint_reason": "manual_save"
            }
        )
    
    async def restore_checkpoint(self, checkpoint: CheckpointData) -> MessagesState:
        """
        恢复检查点
        
        Args:
            checkpoint: 检查点数据
            
        Returns:
            MessagesState: 恢复的状态
        """
        # 恢复智能体状态
        if "status" in checkpoint.execution_context:
            self.status = AgentStatus(**checkpoint.execution_context["status"])
        
        # 恢复指标
        if "metrics" in checkpoint.execution_context:
            self.metrics = AgentMetrics(**checkpoint.execution_context["metrics"])
        
        return checkpoint.state_snapshot
    
    # === 监控和诊断 ===
    
    async def get_health_status(self) -> AgentStatus:
        """
        获取健康状态
        
        Returns:
            AgentStatus: 当前状态
        """
        return self.status
    
    async def get_metrics(self) -> AgentMetrics:
        """
        获取性能指标
        
        Returns:
            AgentMetrics: 性能指标
        """
        return self.metrics
    
    def _update_metrics(self, success: bool = True):
        """更新性能指标"""
        self.metrics.execution_count += 1
        if success:
            self.metrics.success_count += 1
        else:
            self.metrics.error_count += 1
        
        self.metrics.last_execution_time = datetime.utcnow()
        
        # 计算平均执行时间
        if self.status.start_time:
            execution_time = (datetime.utcnow() - self.status.start_time).total_seconds()
            if self.metrics.execution_count == 1:
                self.metrics.average_execution_time = execution_time
            else:
                # 移动平均
                self.metrics.average_execution_time = (
                    self.metrics.average_execution_time * 0.9 + execution_time * 0.1
                )
    
    # === 工具集成 ===
    
    def as_tool(self, description: str = None):
        """
        将智能体包装为工具，供其他智能体调用
        支持 LangGraph 0.6 的工具调用模式
        
        Args:
            description: 工具描述
            
        Returns:
            Tool: LangChain 工具对象
        """
        from langchain_core.tools import tool
        
        @tool(
            name=f"call_{self.config.agent_id}",
            description=description or f"Call {self.config.agent_type} agent for {self.config.agent_id}",
        )
        async def agent_tool(input_data: str, config=None) -> str:
            """调用智能体工具"""
            # 构造消息状态
            state = {
                "messages": [{
                    "role": "user", 
                    "content": input_data
                }]
            }
            
            # 调用智能体
            result = await self(state)
            
            # 返回结果
            if isinstance(result, Command):
                return f"Agent routed to: {result.goto}, Update: {result.update}"
            else:
                last_message = result.get("messages", [])[-1] if result.get("messages") else {}
                return last_message.get("content", "No response")
        
        return agent_tool

    # === LangGraph 0.6 函数式API支持 ===

    def as_task(self, timeout: Optional[int] = None, retry: int = 3):
        """
        将智能体包装为LangGraph 0.6的@task函数

        Args:
            timeout: 任务超时时间
            retry: 重试次数

        Returns:
            Callable: 可用于@task装饰的函数
        """
        from langgraph.func import task

        @task(timeout=timeout or self.config.timeout, retry=retry)
        async def agent_task(input_data: Any) -> Any:
            """智能体任务函数"""
            # 构造消息状态
            if isinstance(input_data, dict):
                state = input_data
            else:
                state = {
                    "messages": [{
                        "role": "user",
                        "content": str(input_data)
                    }]
                }

            # 调用智能体
            result = await self(state)
            return result

        return agent_task

    def create_workflow(self, checkpointer=None):
        """
        创建基于函数式API的工作流

        Args:
            checkpointer: 检查点器，默认使用内存检查点器

        Returns:
            Callable: 可用于@entrypoint装饰的工作流函数
        """
        from langgraph.func import entrypoint
        from langgraph.checkpoint.memory import InMemorySaver

        if checkpointer is None:
            checkpointer = InMemorySaver()

        @entrypoint(checkpointer=checkpointer)
        async def agent_workflow(input_data: Any) -> Any:
            """智能体工作流"""
            # 获取任务函数
            task_func = self.as_task()

            # 执行任务
            result = await task_func(input_data)
            return result.result() if hasattr(result, 'result') else result

        return agent_workflow

    def supports_send_api(self) -> bool:
        """检查是否支持Send API动态分发"""
        return self.config.enable_send_api

    def create_send_tasks(self, task_list: List[Any]):
        """
        创建Send API任务列表

        Args:
            task_list: 任务数据列表

        Returns:
            List[Send]: Send对象列表
        """
        if not self.supports_send_api():
            raise ValueError("Send API not enabled for this agent")

        from langgraph.types import Send

        task_func = self.as_task()
        return [Send(task_func.__name__, {"input_data": task}) for task in task_list]

    # === 缓存集成 ===
    
    async def _get_cached_result(self, cache_key: str) -> Optional[Any]:
        """获取缓存结果"""
        if not self.config.cache_enabled or not self._cache_manager:
            return None
        
        try:
            return await self._cache_manager.get(cache_key)
        except Exception as e:
            if self._logger:
                self._logger.warning(f"Cache get failed: {e}")
            return None
    
    async def _set_cached_result(self, cache_key: str, result: Any, content_type: str = "agent_result"):
        """设置缓存结果"""
        if not self.config.cache_enabled or not self._cache_manager:
            return
        
        try:
            await self._cache_manager.set(cache_key, result, content_type)
        except Exception as e:
            if self._logger:
                self._logger.warning(f"Cache set failed: {e}")
    
    def _generate_cache_key(self, state: MessagesState) -> str:
        """生成缓存键"""
        import hashlib
        import json
        
        # 使用智能体ID、类型和输入状态生成缓存键
        cache_data = {
            "agent_id": self.config.agent_id,
            "agent_type": self.config.agent_type,
            "messages": state.get("messages", [])
        }
        
        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()
```

## 使用示例

### 实现具体智能体

```python
class RequirementAnalystAgent(BaseAgent):
    """需求分析智能体实现"""
    
    async def initialize(self, config: AgentConfig) -> None:
        """初始化智能体"""
        from src.llm.manager import LLMManager
        from src.cache.manager import CacheManager
        from src.utils.logger import setup_logger
        
        self._llm_manager = LLMManager(config.llm_config)
        self._cache_manager = CacheManager()
        self._logger = setup_logger(config.agent_id)
        
        # 验证 LLM 连接
        await self._llm_manager.test_connection()
        
        self._logger.info(f"RequirementAnalystAgent {config.agent_id} initialized")
    
    async def validate_input(self, state: MessagesState) -> ValidationResult:
        """验证输入状态"""
        messages = state.get("messages", [])
        
        if not messages:
            return ValidationResult(
                is_valid=False,
                error_message="No messages in state"
            )
        
        last_message = messages[-1]
        if not last_message.get("content"):
            return ValidationResult(
                is_valid=False,
                error_message="Last message has no content"
            )
        
        return ValidationResult(is_valid=True)
    
    async def execute(self, state: MessagesState) -> Command:
        """执行需求分析逻辑"""
        # 检查缓存
        cache_key = self._generate_cache_key(state)
        cached_result = await self._get_cached_result(cache_key)
        
        if cached_result:
            self.metrics.cache_hit_rate = (
                self.metrics.cache_hit_rate * 0.9 + 1.0 * 0.1
            )
            return Command(
                goto=cached_result["next_agent"],
                update=cached_result["state_update"]
            )
        
        # 执行分析
        messages = state["messages"]
        analysis_result = await self._analyze_requirements(messages)
        
        # 决定下一步
        if analysis_result.needs_more_info:
            next_agent = "information_retrieval_agent"
        else:
            next_agent = "research_planner_agent"
        
        result = {
            "next_agent": next_agent,
            "state_update": {
                "messages": messages + [analysis_result.to_message()],
                "analysis_result": analysis_result.dict()
            }
        }
        
        # 缓存结果
        await self._set_cached_result(cache_key, result, "llm_analysis")
        
        return Command(
            goto=next_agent,
            update=result["state_update"]
        )
    
    async def _analyze_requirements(self, messages):
        """执行具体的需求分析逻辑"""
        # TODO: 实现具体的分析逻辑
        pass
```

### 在 LangGraph 中使用

```python
from langgraph.graph import StateGraph, MessagesState, START, END

# 创建智能体实例
requirement_agent = RequirementAnalystAgent(AgentConfig(
    agent_id="req_analyst_001",
    agent_type="requirement_analyst",
    llm_config={"model": "gpt-4", "api_key": "..."}
))

# 构建工作流
builder = StateGraph(MessagesState)
builder.add_node("requirement_analyst", requirement_agent)
builder.add_edge(START, "requirement_analyst")

# 编译并运行
workflow = builder.compile()
result = await workflow.ainvoke({
    "messages": [{"role": "user", "content": "分析AI技术发展趋势"}]
})
```

## 扩展点

### 自定义错误处理

```python
class CustomAgent(BaseAgent):
    async def handle_error(self, error: Exception, state: MessagesState) -> Command:
        if isinstance(error, SpecificError):
            # 特定错误的处理逻辑
            return Command(goto="retry_handler", update={...})
        else:
            # 使用默认错误处理
            return await super().handle_error(error, state)
```

### 自定义缓存策略

```python
class CacheOptimizedAgent(BaseAgent):
    def _generate_cache_key(self, state: MessagesState) -> str:
        # 自定义缓存键生成逻辑
        return f"custom_{self.config.agent_id}_{hash(state)}"
```

---

*本设计文档遵循项目 `.augmentrules` 规范，保存在 `doc/dev/design/api/` 目录下。*
