# 智能体通信协议规范

## 文档信息
- **文档版本**: v1.1
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31
- **相关文档**: [ADR-003: LangGraph 0.6 函数式API优化集成决策](../../architecture/decisions/20250731.ADR-003.langgraph.functional.api.optimization.v1.0.md)

## 1. 通信架构

### 1.1 技术栈
- **消息队列**: Python asyncio.Queue（内存队列）
- **消息格式**: JSON
- **通信模式**: 异步消息传递
- **LangGraph 0.6**: Send API动态任务分发
- **函数式API**: @task和@entrypoint装饰器

### 1.2 通信原则
- 智能体间松耦合通信
- 异步非阻塞消息传递
- 标准化消息格式
- 错误处理和重试机制
- 支持LangGraph 0.6 Send API动态分发
- 函数式API优先，StateGraph作为补充
- 自动检查点和状态恢复

## 2. 消息格式规范

### 2.1 标准消息结构
```json
{
  "message_id": "uuid4_string",
  "timestamp": "2025-07-31T10:30:00Z",
  "sender": "agent_name",
  "receiver": "agent_name",
  "message_type": "task|result|error|status",
  "payload": {},
  "correlation_id": "optional_uuid4_string"
}
```

### 2.2 消息类型定义

| 消息类型 | 描述 | 使用场景 |
|---------|------|----------|
| task | 任务分发消息 | 协调者向执行者分发任务 |
| result | 结果返回消息 | 执行者向协调者返回结果 |
| error | 错误报告消息 | 任何智能体报告错误 |
| status | 状态更新消息 | 智能体状态变更通知 |

### 2.3 Payload 结构示例

#### 任务消息 (task)
```json
{
  "task_type": "requirement_analysis|information_retrieval|content_analysis|document_writing",
  "task_id": "uuid4_string",
  "input_data": {},
  "config": {},
  "priority": 1
}
```

#### 结果消息 (result)
```json
{
  "task_id": "uuid4_string",
  "status": "success|failed|partial",
  "output_data": {},
  "metadata": {
    "execution_time": 1.23,
    "tokens_used": 1500
  }
}
```

#### 错误消息 (error)
```json
{
  "error_type": "validation|network|llm|system",
  "error_code": "ERR_001",
  "error_message": "Error description",
  "context": {}
}
```

## 3. 智能体通信接口

### 3.1 基础通信接口
```python
class AgentCommunicator:
    async def send_message(self, message: Dict[str, Any]) -> None
    async def receive_message(self) -> Dict[str, Any]
    async def send_task(self, receiver: str, task_data: Dict[str, Any]) -> str
    async def send_result(self, receiver: str, task_id: str, result_data: Dict[str, Any]) -> None
    async def send_error(self, receiver: str, error_data: Dict[str, Any]) -> None
```

### 3.2 消息路由规则

| 发送者 | 接收者 | 消息类型 | 场景 |
|--------|--------|----------|------|
| TaskSchedulerAgent | 各执行智能体 | task | 任务分发 |
| 各执行智能体 | TaskSchedulerAgent | result | 结果汇报 |
| 任何智能体 | QualityMonitorAgent | status | 状态监控 |
| 任何智能体 | 任何智能体 | error | 错误通知 |

## 4. 错误处理机制

### 4.1 重试策略
- **重试次数**: 最多3次
- **重试间隔**: 指数退避 (1s, 2s, 4s)
- **重试条件**: 网络错误、临时性LLM错误

### 4.2 错误代码定义

| 错误代码 | 错误类型 | 描述 |
|---------|----------|------|
| ERR_001 | validation | 输入数据验证失败 |
| ERR_002 | network | 网络连接错误 |
| ERR_003 | llm | LLM服务调用失败 |
| ERR_004 | system | 系统内部错误 |
| ERR_005 | timeout | 操作超时 |

## 5. 性能和监控

### 5.1 性能指标
- 消息传递延迟 < 10ms
- 消息队列长度监控
- 智能体响应时间统计

### 5.2 监控数据格式
```json
{
  "agent_name": "string",
  "message_count": 123,
  "avg_response_time": 1.23,
  "error_count": 5,
  "last_activity": "2025-07-31T10:30:00Z"
}
```

## 6. 实现要点

### 6.1 消息队列管理
- 每个智能体维护独立的接收队列
- 使用 asyncio.Queue 实现异步消息处理
- 队列大小限制：1000条消息

### 6.2 消息持久化
- 关键消息（任务、结果）需要持久化到SQLite
- 错误消息记录到日志文件
- 状态消息仅内存处理

### 6.3 并发控制
- 使用 asyncio.Lock 保护共享资源
- 消息处理采用异步并发模式
- 避免死锁和竞态条件

## 7. LangGraph 0.6 Send API集成

### 7.1 Send API通信模式

LangGraph 0.6的Send API提供了更高效的动态任务分发机制，替代传统的消息队列模式。

```python
from langgraph.types import Send
from langgraph.func import task, entrypoint

@task
def process_research_task(task_data: dict):
    """处理单个调研任务"""
    return research_agent.invoke(task_data)

def assign_research_tasks(state: dict):
    """动态任务分发函数"""
    tasks = state["research_tasks"]
    return [Send("process_research_task", {"task_data": task}) for task in tasks]

@entrypoint()
def orchestrated_research_workflow(research_plan: dict):
    """协调者-执行者模式的研究工作流"""
    # 任务分解
    tasks = decompose_research_plan(research_plan)

    # 动态分发和并行执行
    task_futures = [process_research_task(task) for task in tasks]

    # 结果汇聚
    results = [future.result() for future in task_futures]
    return aggregate_research_results(results)
```

### 7.2 函数式API通信优势

1. **自动负载均衡**: Send API自动处理任务分发和负载均衡
2. **并行执行**: 天然支持并行任务处理，无需手动管理线程
3. **错误隔离**: 单个任务失败不影响其他任务执行
4. **状态管理**: 自动处理检查点和状态恢复
5. **简化代码**: 减少50%的样板代码

### 7.3 迁移策略

**阶段1: 核心模块迁移**
- 信息检索模块 → 并行@task实现
- 需求分析模块 → @task + @entrypoint
- 任务调度模块 → Send API实现

**阶段2: 协调机制优化**
- 智能体间通信 → Command对象
- 质量监控 → 实时流式处理
- 错误处理 → 自动检查点恢复

**阶段3: 性能优化**
- 并发限制和超时配置
- 缓存集成和优化
- 监控和日志完善

### 7.4 兼容性保证

- **向后兼容**: 保留现有消息队列机制作为备选
- **渐进迁移**: 支持Send API和消息队列并存
- **统一接口**: 提供统一的通信接口抽象

---

*本协议规范遵循项目 `.augmentrules` 规范，保存在 `doc/dev/design/api/` 目录下。*
