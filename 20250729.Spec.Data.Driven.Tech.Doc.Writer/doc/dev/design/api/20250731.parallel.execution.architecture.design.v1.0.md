# 并行执行架构设计文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31
- **设计类型**: 架构设计文档
- **相关文档**: 
  - [BaseAgent 函数式API设计](./20250731.base.agent.functional.api.design.v1.0.md)
  - [多智能体工作流设计](../../../design/20250730.multi.agent.workflow.design.v1.0.md)

## 设计概述

基于LangGraph 0.6函数式API的三层并行执行架构，通过@task装饰器和Send API实现动态任务分发，达到3-5倍性能提升和10+并发任务处理能力。

### 核心目标
1. **性能提升**: 实现3-5倍执行效率提升
2. **并发能力**: 支持10+并发任务处理
3. **动态分发**: Send API智能负载均衡
4. **自动恢复**: 内置故障检测和恢复机制

## 三层并行架构

### 第一层：智能体级并行
多个专业智能体同时处理不同类型的任务

```python
# src/core/parallel_coordinator.py
from langgraph import task, entrypoint
from langgraph.types import Send
from typing import List, Dict, Any

class ParallelCoordinator:
    """并行协调器 - 第一层并行"""
    
    @entrypoint
    async def coordinate_agents(self, state: MessagesState) -> MessagesState:
        """协调多个智能体并行执行"""
        
        # 分析任务类型和依赖关系
        task_analysis = await self.analyze_task_dependencies(state)
        
        # 生成智能体级并行任务
        agent_tasks = await self.generate_agent_tasks(task_analysis)
        
        # Send API动态分发到不同智能体
        if agent_tasks:
            return Send(agent_tasks)
        
        return state
    
    @task
    async def analyze_task_dependencies(self, state: MessagesState) -> Dict[str, Any]:
        """分析任务依赖关系"""
        requirements = state.get("requirement_list", [])
        
        # 构建任务依赖图
        dependency_graph = {
            "independent_tasks": [],  # 可并行执行的任务
            "dependent_tasks": [],    # 有依赖关系的任务
            "critical_path": []       # 关键路径任务
        }
        
        for req in requirements:
            if req.get("dependencies"):
                dependency_graph["dependent_tasks"].append(req)
            else:
                dependency_graph["independent_tasks"].append(req)
        
        return dependency_graph
    
    @task
    async def generate_agent_tasks(self, task_analysis: Dict[str, Any]) -> List[Send]:
        """生成智能体级并行任务"""
        parallel_sends = []
        
        # 为独立任务创建并行Send对象
        for task in task_analysis["independent_tasks"]:
            agent_type = self.determine_agent_type(task)
            parallel_sends.append(
                Send(agent_type, {
                    "task": task,
                    "priority": task.get("priority", "normal"),
                    "timeout": task.get("timeout", 300)
                })
            )
        
        return parallel_sends
    
    def determine_agent_type(self, task: Dict[str, Any]) -> str:
        """确定任务对应的智能体类型"""
        task_type = task.get("type", "")
        
        agent_mapping = {
            "requirement_analysis": "requirement_analyst",
            "information_retrieval": "information_retrieval",
            "content_analysis": "content_analysis",
            "document_writing": "document_writer"
        }
        
        return agent_mapping.get(task_type, "generic_agent")
```

### 第二层：任务级并行
单个智能体内部的多个任务并行执行

```python
# src/agents/information_retrieval.py
from .base import BaseAgent
from langgraph import task
from langgraph.types import Send
from typing import List

class InformationRetrievalAgent(BaseAgent):
    """信息检索智能体 - 第二层并行"""
    
    @task
    async def execute_task(self, state: MessagesState) -> MessagesState:
        """并行执行多个检索任务"""
        keywords = state.get("search_keywords", [])
        
        # 创建并行检索任务
        retrieval_tasks = await self.create_parallel_retrieval_tasks(keywords)
        
        # 并行执行所有检索任务
        results = await self.execute_parallel_retrievals(retrieval_tasks)
        
        # 合并检索结果
        merged_results = await self.merge_retrieval_results(results)
        
        state["retrieval_results"] = merged_results
        return state
    
    @task
    async def create_parallel_retrieval_tasks(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """创建并行检索任务"""
        tasks = []
        
        # 为每个关键词创建多源检索任务
        for keyword in keywords:
            # 学术数据库检索
            tasks.append({
                "type": "academic_search",
                "keyword": keyword,
                "sources": ["arxiv", "pubmed", "ieee"]
            })
            
            # 行业报告检索
            tasks.append({
                "type": "industry_search", 
                "keyword": keyword,
                "sources": ["gartner", "mckinsey", "bcg"]
            })
            
            # 新闻资讯检索
            tasks.append({
                "type": "news_search",
                "keyword": keyword,
                "sources": ["techcrunch", "wired", "ars_technica"]
            })
        
        return tasks
    
    @task
    async def execute_parallel_retrievals(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """并行执行检索任务"""
        # 使用@task装饰器自动并行执行
        results = []
        
        # 创建并行任务组
        parallel_tasks = []
        for task in tasks:
            if task["type"] == "academic_search":
                parallel_tasks.append(self.search_academic_sources(task))
            elif task["type"] == "industry_search":
                parallel_tasks.append(self.search_industry_reports(task))
            elif task["type"] == "news_search":
                parallel_tasks.append(self.search_news_sources(task))
        
        # 等待所有并行任务完成
        results = await asyncio.gather(*parallel_tasks, return_exceptions=True)
        
        # 过滤异常结果
        valid_results = [r for r in results if not isinstance(r, Exception)]
        
        return valid_results
    
    @task
    async def search_academic_sources(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """学术数据库检索任务"""
        # 实现学术数据库检索逻辑
        return {"source": "academic", "results": []}
    
    @task
    async def search_industry_reports(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """行业报告检索任务"""
        # 实现行业报告检索逻辑
        return {"source": "industry", "results": []}
    
    @task
    async def search_news_sources(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """新闻资讯检索任务"""
        # 实现新闻资讯检索逻辑
        return {"source": "news", "results": []}
```

### 第三层：工具级并行
单个任务内部的多个工具调用并行执行

```python
# src/tools/parallel_web_scraper.py
from langgraph import task
from typing import List, Dict, Any
import asyncio
import httpx

class ParallelWebScraper:
    """并行网页抓取器 - 第三层并行"""
    
    def __init__(self, max_concurrent: int = 10):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    @task
    async def scrape_urls_parallel(self, urls: List[str]) -> List[Dict[str, Any]]:
        """并行抓取多个URL"""
        
        # 创建并行抓取任务
        scraping_tasks = [
            self.scrape_single_url(url) for url in urls
        ]
        
        # 并行执行，限制并发数
        results = await asyncio.gather(*scraping_tasks, return_exceptions=True)
        
        # 处理结果和异常
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "url": urls[i],
                    "status": "error",
                    "error": str(result)
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    @task
    async def scrape_single_url(self, url: str) -> Dict[str, Any]:
        """抓取单个URL"""
        async with self.semaphore:  # 限制并发数
            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.get(url)
                    response.raise_for_status()
                    
                    return {
                        "url": url,
                        "status": "success",
                        "content": response.text,
                        "headers": dict(response.headers)
                    }
            except Exception as e:
                return {
                    "url": url,
                    "status": "error", 
                    "error": str(e)
                }
```

## Send API动态任务分发

### 智能负载均衡器

```python
# src/core/load_balancer.py
from langgraph import task
from langgraph.types import Send
from typing import List, Dict, Any
import time

class LoadBalancer:
    """Send API智能负载均衡器"""
    
    def __init__(self):
        self.agent_loads = {}  # 智能体负载统计
        self.task_queue = []   # 任务队列
    
    @task
    async def distribute_tasks(self, tasks: List[Dict[str, Any]]) -> List[Send]:
        """智能分发任务"""
        
        # 分析任务优先级和资源需求
        prioritized_tasks = await self.prioritize_tasks(tasks)
        
        # 选择最优智能体
        send_objects = []
        for task in prioritized_tasks:
            best_agent = await self.select_best_agent(task)
            
            send_objects.append(
                Send(best_agent, {
                    "task": task,
                    "assigned_at": time.time(),
                    "priority": task.get("priority", "normal")
                })
            )
            
            # 更新负载统计
            self.update_agent_load(best_agent, task)
        
        return send_objects
    
    @task
    async def prioritize_tasks(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """任务优先级排序"""
        def priority_score(task):
            priority_map = {"high": 3, "normal": 2, "low": 1}
            return priority_map.get(task.get("priority", "normal"), 2)
        
        return sorted(tasks, key=priority_score, reverse=True)
    
    @task
    async def select_best_agent(self, task: Dict[str, Any]) -> str:
        """选择最优智能体"""
        task_type = task.get("type", "")
        
        # 获取可处理该任务类型的智能体
        capable_agents = self.get_capable_agents(task_type)
        
        # 选择负载最低的智能体
        best_agent = min(capable_agents, 
                        key=lambda agent: self.agent_loads.get(agent, 0))
        
        return best_agent
    
    def get_capable_agents(self, task_type: str) -> List[str]:
        """获取能处理指定任务类型的智能体"""
        capability_map = {
            "requirement_analysis": ["requirement_analyst"],
            "information_retrieval": ["information_retrieval", "web_scraper"],
            "content_analysis": ["content_analysis", "quality_reviewer"],
            "document_writing": ["document_writer"]
        }
        
        return capability_map.get(task_type, ["generic_agent"])
    
    def update_agent_load(self, agent: str, task: Dict[str, Any]):
        """更新智能体负载"""
        if agent not in self.agent_loads:
            self.agent_loads[agent] = 0
        
        # 根据任务复杂度增加负载
        complexity = task.get("complexity", "medium")
        load_increment = {"low": 1, "medium": 2, "high": 3}
        
        self.agent_loads[agent] += load_increment.get(complexity, 2)
```

## 性能监控和优化

### 并行执行监控器

```python
# src/monitoring/parallel_monitor.py
from langgraph import task
from typing import Dict, Any
import time
import asyncio

class ParallelExecutionMonitor:
    """并行执行性能监控器"""
    
    def __init__(self):
        self.metrics = {
            "total_tasks": 0,
            "parallel_tasks": 0,
            "execution_time": 0.0,
            "throughput": 0.0,
            "error_rate": 0.0
        }
    
    @task
    async def monitor_execution(self, state: MessagesState) -> MessagesState:
        """监控并行执行性能"""
        
        start_time = time.time()
        
        # 记录任务开始
        await self.record_task_start(state)
        
        # 执行监控逻辑
        execution_stats = await self.collect_execution_stats(state)
        
        # 计算性能指标
        performance_metrics = await self.calculate_performance_metrics(
            execution_stats, start_time
        )
        
        # 更新状态
        state["performance_metrics"] = performance_metrics
        
        return state
    
    @task
    async def record_task_start(self, state: MessagesState):
        """记录任务开始"""
        self.metrics["total_tasks"] += 1
        parallel_count = len(state.get("parallel_tasks", []))
        self.metrics["parallel_tasks"] = max(
            self.metrics["parallel_tasks"], parallel_count
        )
    
    @task
    async def collect_execution_stats(self, state: MessagesState) -> Dict[str, Any]:
        """收集执行统计信息"""
        return {
            "concurrent_tasks": len(state.get("parallel_tasks", [])),
            "completed_tasks": len(state.get("completed_tasks", [])),
            "failed_tasks": len(state.get("failed_tasks", [])),
            "cache_hits": state.get("cache_hits", 0),
            "cache_misses": state.get("cache_misses", 0)
        }
    
    @task
    async def calculate_performance_metrics(self, stats: Dict[str, Any], start_time: float) -> Dict[str, Any]:
        """计算性能指标"""
        execution_time = time.time() - start_time
        
        total_tasks = stats["completed_tasks"] + stats["failed_tasks"]
        throughput = total_tasks / execution_time if execution_time > 0 else 0
        error_rate = stats["failed_tasks"] / total_tasks if total_tasks > 0 else 0
        
        cache_total = stats["cache_hits"] + stats["cache_misses"]
        cache_hit_rate = stats["cache_hits"] / cache_total if cache_total > 0 else 0
        
        return {
            "execution_time": execution_time,
            "throughput": throughput,
            "error_rate": error_rate,
            "cache_hit_rate": cache_hit_rate,
            "concurrent_tasks": stats["concurrent_tasks"],
            "performance_improvement": self.calculate_improvement_ratio(throughput)
        }
    
    def calculate_improvement_ratio(self, current_throughput: float) -> float:
        """计算性能提升比例"""
        # 基准吞吐量（串行执行）
        baseline_throughput = 1.0
        
        return current_throughput / baseline_throughput if baseline_throughput > 0 else 1.0
```

## 故障恢复机制

### 自动恢复系统

```python
# src/core/recovery_system.py
from langgraph import task
from typing import Dict, Any, List

class AutoRecoverySystem:
    """自动故障恢复系统"""
    
    @task
    async def handle_task_failure(self, failed_task: Dict[str, Any], state: MessagesState) -> MessagesState:
        """处理任务失败"""
        
        # 分析失败原因
        failure_analysis = await self.analyze_failure(failed_task)
        
        # 决定恢复策略
        recovery_strategy = await self.determine_recovery_strategy(failure_analysis)
        
        # 执行恢复操作
        recovery_result = await self.execute_recovery(recovery_strategy, failed_task, state)
        
        return recovery_result
    
    @task
    async def analyze_failure(self, failed_task: Dict[str, Any]) -> Dict[str, Any]:
        """分析失败原因"""
        error_type = failed_task.get("error_type", "unknown")
        error_message = failed_task.get("error_message", "")
        
        analysis = {
            "error_type": error_type,
            "is_retryable": error_type in ["timeout", "network_error", "rate_limit"],
            "suggested_delay": self.calculate_retry_delay(error_type),
            "alternative_approach": self.suggest_alternative(error_type)
        }
        
        return analysis
    
    @task
    async def execute_recovery(self, strategy: Dict[str, Any], failed_task: Dict[str, Any], state: MessagesState) -> MessagesState:
        """执行恢复操作"""
        if strategy["action"] == "retry":
            # 重试任务
            await asyncio.sleep(strategy["delay"])
            # 重新提交任务到队列
            state.setdefault("retry_queue", []).append(failed_task)
        
        elif strategy["action"] == "alternative":
            # 使用替代方案
            alternative_task = strategy["alternative_task"]
            state.setdefault("alternative_queue", []).append(alternative_task)
        
        elif strategy["action"] == "skip":
            # 跳过任务，记录日志
            state.setdefault("skipped_tasks", []).append(failed_task)
        
        return state
```

## 性能目标验证

### 性能基准测试

```python
# tests/test_parallel_performance.py
import pytest
import time
import asyncio

class TestParallelPerformance:
    
    @pytest.mark.asyncio
    async def test_3x_performance_improvement(self):
        """验证3倍性能提升目标"""
        
        # 串行执行基准测试
        start_time = time.time()
        await self.execute_serial_tasks()
        serial_time = time.time() - start_time
        
        # 并行执行测试
        start_time = time.time()
        await self.execute_parallel_tasks()
        parallel_time = time.time() - start_time
        
        # 验证性能提升
        improvement_ratio = serial_time / parallel_time
        assert improvement_ratio >= 3.0, f"Performance improvement {improvement_ratio:.2f}x < 3x target"
    
    @pytest.mark.asyncio
    async def test_10_concurrent_tasks(self):
        """验证10+并发任务处理能力"""
        
        concurrent_tasks = 12  # 超过10个任务
        
        start_time = time.time()
        tasks = [self.create_test_task(i) for i in range(concurrent_tasks)]
        
        # 并行执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        execution_time = time.time() - start_time
        
        # 验证所有任务都成功完成
        successful_tasks = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_tasks) >= concurrent_tasks * 0.9  # 90%成功率
        
        # 验证执行时间合理
        assert execution_time < 60.0  # 1分钟内完成
```

---

*本设计文档遵循项目 `.augmentrules` 规范，保存在 `doc/dev/design/api/` 目录下。*
