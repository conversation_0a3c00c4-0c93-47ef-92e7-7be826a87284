# BaseAgent 函数式API设计文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-07-31
- **最后更新**: 2025-07-31
- **设计类型**: API设计文档
- **相关文档**: 
  - [ADR-002: Sprint 1 技术决策](../../architecture/decisions/20250731.ADR-002.sprint1.technical.decisions.v1.0.md)
  - [多智能体工作流设计](../../../design/20250730.multi.agent.workflow.design.v1.0.md)

## 设计概述

基于LangGraph 0.6函数式API的BaseAgent基类设计，完全采用@task和@entrypoint装饰器，实现高性能并行执行和自动检查点机制。

### 核心设计原则
1. **函数式优先**: 所有核心方法使用@task装饰器
2. **并行执行**: 天然支持并发，实现3-5倍性能提升
3. **Send API集成**: 原生支持动态任务分发
4. **自动检查点**: 无需手动状态管理
5. **类型安全**: 与Pydantic模型完美集成

## 核心接口设计

### BaseAgent 基类

```python
# src/agents/base.py
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel
from langgraph import task, entrypoint
from langgraph.graph import MessagesState
from langgraph.types import Send
import structlog

logger = structlog.get_logger()

class AgentConfig(BaseModel):
    """智能体配置模型"""
    agent_id: str
    agent_type: str
    llm_config: Dict[str, Any]
    tools: Optional[List[str]] = None
    max_retries: int = 3
    timeout: int = 300
    parallel_limit: int = 10
    cache_enabled: bool = True

class AgentMetrics(BaseModel):
    """智能体性能指标"""
    execution_time: float
    cache_hit_rate: float
    parallel_tasks_count: int
    error_count: int

class BaseAgent(ABC):
    """
    基于LangGraph 0.6函数式API的智能体基类
    使用@task装饰器实现并行执行和自动检查点
    """
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.metrics = AgentMetrics(
            execution_time=0.0,
            cache_hit_rate=0.0,
            parallel_tasks_count=0,
            error_count=0
        )
        self.logger = logger.bind(agent_id=config.agent_id)
    
    # === 函数式API核心方法 ===
    
    @abstractmethod
    @task
    async def execute_task(self, state: MessagesState) -> MessagesState:
        """
        使用@task装饰器的核心执行方法
        自动支持并行执行和检查点机制
        
        Args:
            state: 当前工作流状态
            
        Returns:
            更新后的状态
        """
        pass
    
    @abstractmethod
    @task
    async def validate_input(self, state: MessagesState) -> bool:
        """
        输入验证任务
        
        Args:
            state: 输入状态
            
        Returns:
            验证结果
        """
        pass
    
    @abstractmethod
    @task
    async def process_parallel_tasks(self, state: MessagesState) -> List[Send]:
        """
        生成并行任务的Send对象列表
        支持动态任务分发和负载均衡
        
        Args:
            state: 当前状态
            
        Returns:
            Send对象列表，用于动态任务分发
        """
        pass
    
    @task
    async def collect_metrics(self, state: MessagesState) -> MessagesState:
        """
        收集性能指标任务
        
        Args:
            state: 当前状态
            
        Returns:
            包含指标的状态
        """
        # 更新性能指标
        self.metrics.parallel_tasks_count = len(
            state.get("parallel_tasks", [])
        )
        
        # 添加指标到状态
        state["agent_metrics"] = self.metrics.dict()
        return state
    
    # === 工作流组织方法 ===
    
    @entrypoint
    async def run_workflow(self, state: MessagesState) -> MessagesState:
        """
        使用@entrypoint装饰器的主工作流
        自动管理任务依赖和并行执行
        
        Args:
            state: 初始状态
            
        Returns:
            最终状态
        """
        try:
            self.logger.info("Starting agent workflow", 
                           agent_type=self.config.agent_type)
            
            # 1. 输入验证
            is_valid = await self.validate_input(state)
            if not is_valid:
                raise ValueError("Input validation failed")
            
            # 2. 执行核心任务
            result = await self.execute_task(state)
            
            # 3. 处理并行任务（如果需要）
            parallel_sends = await self.process_parallel_tasks(result)
            if parallel_sends:
                self.logger.info("Dispatching parallel tasks", 
                               count=len(parallel_sends))
                # Send API动态分发并行任务
                return Send(parallel_sends)
            
            # 4. 收集性能指标
            final_result = await self.collect_metrics(result)
            
            self.logger.info("Agent workflow completed successfully")
            return final_result
            
        except Exception as e:
            self.metrics.error_count += 1
            self.logger.error("Agent workflow failed", 
                            error=str(e), exc_info=True)
            raise
    
    # === 辅助方法 ===
    
    async def get_cache_key(self, state: MessagesState) -> str:
        """生成缓存键"""
        import hashlib
        content = f"{self.config.agent_id}:{state.get('input_hash', '')}"
        return hashlib.md5(content.encode()).hexdigest()
    
    async def should_use_cache(self, state: MessagesState) -> bool:
        """判断是否使用缓存"""
        return (
            self.config.cache_enabled and 
            state.get("use_cache", True)
        )
    
    def get_metrics(self) -> AgentMetrics:
        """获取性能指标"""
        return self.metrics
```

## 具体智能体实现示例

### RequirementAnalystAgent 示例

```python
# src/agents/requirement_analyst.py
from .base import BaseAgent, AgentConfig
from langgraph import task, entrypoint
from langgraph.graph import MessagesState
from langgraph.types import Send
from typing import List

class RequirementAnalystAgent(BaseAgent):
    """需求分析智能体 - 函数式API实现"""
    
    @task
    async def execute_task(self, state: MessagesState) -> MessagesState:
        """执行需求分析任务"""
        user_input = state.get("user_input", "")
        
        # 并行执行多个分析任务
        intent_analysis = await self.analyze_user_intent(state)
        keyword_generation = await self.generate_search_keywords(state)
        
        # 合并结果
        state.update({
            "user_intent": intent_analysis["intent"],
            "requirement_list": intent_analysis["requirements"],
            "search_keywords": keyword_generation["keywords"]
        })
        
        return state
    
    @task
    async def validate_input(self, state: MessagesState) -> bool:
        """验证用户输入"""
        user_input = state.get("user_input", "")
        return len(user_input.strip()) > 10  # 最少10个字符
    
    @task
    async def process_parallel_tasks(self, state: MessagesState) -> List[Send]:
        """生成并行任务"""
        keywords = state.get("search_keywords", [])
        
        # 为每个关键词创建并行检索任务
        parallel_tasks = []
        for keyword in keywords[:5]:  # 限制并行任务数量
            parallel_tasks.append(
                Send("information_retrieval", {
                    "keyword": keyword,
                    "source_type": "academic"
                })
            )
        
        return parallel_tasks
    
    @task
    async def analyze_user_intent(self, state: MessagesState) -> Dict[str, Any]:
        """分析用户意图"""
        # 实现意图分析逻辑
        return {
            "intent": "technology_research",
            "requirements": ["feature_analysis", "market_research"]
        }
    
    @task
    async def generate_search_keywords(self, state: MessagesState) -> Dict[str, Any]:
        """生成搜索关键词"""
        # 实现关键词生成逻辑
        return {
            "keywords": ["AI", "machine learning", "deep learning"]
        }
```

## 性能优化特性

### 1. 自动并行执行
- @task装饰器自动支持并行执行
- 无需手动管理线程或协程
- 自动负载均衡和资源优化

### 2. Send API动态分发
- 支持运行时动态创建任务
- 智能负载均衡
- 自动故障转移

### 3. 自动检查点机制
- 函数式API自动保存中间状态
- 支持故障恢复和断点续传
- 无需手动状态管理

### 4. 缓存集成
- 与二层缓存系统无缝集成
- 自动缓存键生成
- 智能缓存策略

## 测试策略

### 单元测试
```python
# tests/test_base_agent.py
import pytest
from src.agents.base import BaseAgent, AgentConfig

class TestBaseAgent:
    @pytest.mark.asyncio
    async def test_task_decorator_functionality(self):
        """测试@task装饰器功能"""
        pass
    
    @pytest.mark.asyncio
    async def test_parallel_execution_performance(self):
        """测试并行执行性能提升"""
        pass
    
    @pytest.mark.asyncio
    async def test_send_api_dynamic_dispatch(self):
        """测试Send API动态分发"""
        pass
```

### 性能测试
- 验证3-5倍性能提升目标
- 测试10+并发任务处理能力
- 监控内存和CPU使用情况

## 迁移指南

### 从StateGraph到函数式API
1. 将节点函数改为@task装饰的方法
2. 使用@entrypoint组织主工作流
3. 用Send API替换手动路由逻辑
4. 移除手动状态管理代码

### 兼容性考虑
- 保持与现有Pydantic模型的兼容性
- 支持渐进式迁移
- 提供向后兼容的适配器

---

*本设计文档遵循项目 `.augmentrules` 规范，保存在 `doc/dev/design/api/` 目录下。*
