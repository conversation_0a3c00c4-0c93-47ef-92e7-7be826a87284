# 敏捷开发文档目录结构

本目录包含了完整的敏捷开发文档体系，支持从需求分析到产品交付的全生命周期管理。

## 目录结构说明

### 📋 开发需求管理 (requirements/)
- **backlog/**: 产品待办事项列表，包含优先级和估算
- **user-stories/**: 从原始需求拆解出的技术用户故事
- **acceptance-criteria/**: 技术实现的验收标准

> **重要区别**:
> - `doc/requirements/`: 存放用户直接提出的**原始需求**
> - `doc/dev/requirements/`: 存放开发过程中拆解和产生的**技术需求**

### 🏃‍♂️ 迭代管理 (sprints/)
- **planning/**: 迭代规划文档，包含目标、任务分配和时间安排
- **retrospectives/**: 回顾会议记录，总结经验教训和改进措施
- **reviews/**: 迭代评审文档，展示交付成果和用户反馈

### 🏗️ 架构设计 (architecture/)
- **decisions/**: 架构决策记录 (ADR)，记录重要技术决策
  - `ADR-001`: 多智能体架构优化决策
  - `ADR-002`: Sprint 1 技术决策记录
  - `ADR-003`: LangGraph 0.6.2 函数式API优化集成决策
- **diagrams/**: 架构图表，包含系统架构、组件关系等
- **patterns/**: 设计模式和架构模式文档

### 🎨 详细设计 (design/)
- **api/**: API 设计文档，包含接口规范和示例
- **database/**: 数据库设计，包含表结构、关系图等
- **ui-ux/**: 用户界面和用户体验设计文档

### 🔧 实现指南 (implementation/)
- **迁移指南**: LangGraph 0.6.2 函数式API迁移指南
- **最佳实践**: 开发实施的最佳实践和代码示例
- **技术集成**: 具体技术栈的集成方案

### 🧪 测试管理 (testing/)
- **test-plans/**: 测试计划，定义测试策略和范围
- **test-cases/**: 测试用例，包含手动和自动化测试
- **automation/**: 自动化测试脚本和配置
- **专项测试策略**: LangGraph 0.6.2 函数式API测试策略

### 🚀 部署管理 (deployment/)
- **environments/**: 环境配置文档，包含开发、测试、生产环境
- **procedures/**: 部署流程和操作手册
- **rollback/**: 回滚策略和应急预案

### ⚙️ 流程管理 (processes/)
- **workflows/**: 工作流程定义，包含开发、测试、发布流程
- **guidelines/**: 开发规范和编码标准
- **templates/**: 文档模板和检查清单

### 🤝 会议管理 (meetings/)
- **daily-standups/**: 每日站会记录
- **planning/**: 规划会议记录
- **reviews/**: 评审会议记录

### 📚 知识库 (knowledge-base/)
- **troubleshooting/**: 问题排查指南
- **best-practices/**: 最佳实践总结
- **lessons-learned/**: 经验教训记录

## 文档命名规范

建议使用以下命名格式：
- 日期前缀：`YYYYMMDD.` (如：20250731.)
- 版本后缀：`.v1.0.md` (如：.v1.0.md)
- 示例：`20250731.sprint-01.planning.v1.0.md`

## 使用指南

1. **原始需求收集**：在 `doc/requirements/` 中记录用户直接提出的原始需求
2. **需求分析拆解**：在本目录 requirements/ 中将原始需求拆解为技术需求
3. **迭代规划**：在 sprints/planning/ 中制定迭代计划
4. **技术决策**：在 architecture/decisions/ 中记录 ADR
5. **设计文档**：在 design/ 相应子目录中创建详细设计
6. **测试文档**：在 testing/ 中创建测试计划和用例
7. **部署文档**：在 deployment/ 中维护部署相关文档

### 需求管理流程
1. **收集**: 用户需求记录在 `doc/requirements/`
2. **分析**: 理解和分析原始需求
3. **拆解**: 在 `doc/dev/requirements/` 中拆解为技术需求
4. **规划**: 将技术需求纳入Sprint规划
5. **实现**: 基于技术需求进行开发
6. **验证**: 对照原始需求验证实现效果

## 最新更新

### LangGraph 0.6.2 函数式API优化集成 (2025-07-31)
- **ADR-003**: LangGraph 0.6.2 函数式API优化集成决策
- **迁移指南**: 详细的从StateGraph到函数式API的迁移步骤
- **测试策略**: 针对函数式API的全面测试策略
- **预期收益**: 40-60%开发效率提升，3-5倍性能提升

### 核心优化点
1. **函数式API**: 使用@task和@entrypoint装饰器简化代码
2. **并行执行**: 三层并行架构，支持10+并发任务
3. **Send API**: 动态任务分发和智能负载均衡
4. **自动检查点**: 简化容错恢复机制实现
5. **零配置启动**: 仅需LLM API密钥即可运行

## 维护原则

- 保持文档的及时更新
- 使用版本控制跟踪文档变更
- 定期清理过时文档
- 确保文档的可读性和实用性
- 遵循渐进式优化，避免破坏性变更
