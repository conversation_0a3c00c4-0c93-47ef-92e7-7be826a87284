"""
重构后系统的集成测试

验证重构后的 URLCacheManager、WebContentProcessor 和工作流的集成。
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
import tempfile
import json
from datetime import datetime

from src.backend.agents.url_cache_manager import URLCacheManager
from src.backend.agents.web_content_processor import WebContentProcessor
from src.backend.agents.requirement_analyst.workflows import collect_raw_information
from src.backend.config import Config


class TestIntegrationRefactored:
    """重构后系统的集成测试"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # 创建模拟配置
        self.mock_config = Mock(spec=Config)
        self.mock_config.web_scraper = Mock()
        self.mock_config.web_scraper.timeout = 30
        self.mock_config.web_scraper.max_retries = 3
        self.mock_config.web_scraper.user_agent = "Test Agent"
    
    async def test_cache_manager_integration(self):
        """测试缓存管理器集成"""
        cache_manager = URLCacheManager(cache_dir=self.temp_dir / "cache")
        await cache_manager.initialize()
        
        test_url = "https://example.com/integration-test"
        
        # 测试完整的缓存工作流
        assert await cache_manager.is_url_processed(test_url) is False
        
        # 生成文件路径
        file_paths = cache_manager.generate_file_paths(test_url, "html")
        
        # 标记为已处理
        await cache_manager.mark_url_processed(
            url=test_url,
            success=True,
            raw_content_path=file_paths["raw_content_path"],
            markdown_path=file_paths["markdown_path"],
            metadata={
                "quality_score": 0.8,
                "processing_time": 2.5,
                "file_extension": "html"
            }
        )
        
        # 验证缓存状态
        assert await cache_manager.is_url_processed(test_url) is True
        
        # 验证元数据
        metadata = await cache_manager.get_url_metadata(test_url)
        assert metadata["success"] is True
        assert metadata["metadata"]["quality_score"] == 0.8
        
        # 验证统计信息
        stats = cache_manager.get_cache_stats()
        assert stats["cached_url_hashes_count"] == 1
        assert stats["successful_urls"] == 1
    
    @patch('src.backend.agents.web_content_processor.WebScraper')
    @patch('src.backend.agents.web_content_processor.HTMLCleaner')
    @patch('src.backend.agents.web_content_processor.MarkdownConverter')
    @patch('src.backend.agents.web_content_processor.ContentQualityAssessor')
    async def test_web_content_processor_integration(
        self, 
        mock_quality_assessor,
        mock_markdown_converter,
        mock_html_cleaner,
        mock_web_scraper
    ):
        """测试网页内容处理器集成"""
        
        # 设置模拟对象
        mock_scraper_instance = AsyncMock()
        mock_scraper_instance.fetch_url.return_value = {
            "success": True,
            "content": "<html><body>Test content</body></html>"
        }
        mock_web_scraper.return_value = mock_scraper_instance
        
        mock_cleaner_instance = Mock()
        mock_cleaner_instance.clean_html.return_value = {
            "success": True,
            "content": "Test content"
        }
        mock_html_cleaner.return_value = mock_cleaner_instance
        
        mock_converter_instance = Mock()
        mock_converter_instance.convert_to_markdown.return_value = {
            "success": True,
            "markdown_content": "# Test Content\n\nTest content",
            "title": "Test Page"
        }
        mock_markdown_converter.return_value = mock_converter_instance
        
        mock_assessor_instance = Mock()
        mock_assessor_instance.assess_quality.return_value = {
            "quality_score": 0.8,
            "passes_quality": True,
            "word_count": 50,
            "title": "Test Page"
        }
        mock_quality_assessor.return_value = mock_assessor_instance
        
        # 创建处理器
        processor = WebContentProcessor(self.mock_config)
        
        # 设置临时目录
        processor.cache_manager = URLCacheManager(cache_dir=self.temp_dir / "cache")
        
        await processor.initialize()
        
        # 测试URL处理
        test_url = "https://example.com/test-page"
        result = await processor.process_url_with_lock(test_url)
        
        # 验证结果
        assert result["success"] is True
        assert result["url"] == test_url
        assert "raw_content_path" in result
        assert "markdown_path" in result
        assert "quality_result" in result
        
        # 验证缓存已更新
        assert await processor.cache_manager.is_url_processed(test_url) is True
    
    @patch('src.backend.config.get_config')
    @patch('src.backend.agents.web_content_processor.WebContentProcessor')
    async def test_collect_raw_information_workflow_integration(
        self,
        mock_web_content_processor,
        mock_get_config
    ):
        """测试收集原始信息工作流集成"""
        
        # 设置模拟配置
        mock_get_config.return_value = self.mock_config
        
        # 设置模拟处理器
        mock_processor_instance = AsyncMock()
        mock_processor_instance.cache_manager = AsyncMock()
        mock_processor_instance.cache_manager.is_url_processed.return_value = False
        
        # 模拟处理结果
        mock_processor_instance.process_urls.return_value = [
            {
                "url": "https://example.com/doc1",
                "success": True,
                "raw_content_path": "data/raw_content/hash1.html",
                "markdown_path": "data/markdown_content/hash1.md",
                "quality_result": {
                    "quality_score": 0.8,
                    "passes_quality": True,
                    "word_count": 500,
                    "title": "Document 1"
                },
                "processing_time": 2.5
            },
            {
                "url": "https://example.com/doc2",
                "success": False,
                "error": "Failed to fetch",
                "stage": "fetch"
            }
        ]
        
        # 模拟统计信息
        mock_processor_instance.stats = {
            "total_processed": 2,
            "successful_processed": 1,
            "failed_processed": 1,
            "cache_hits": 0,
            "concurrent_skips": 0
        }
        
        mock_processor_instance.cache_manager.get_cache_stats.return_value = {
            "cached_url_hashes_count": 1,
            "successful_urls": 1,
            "failed_urls": 1
        }
        
        mock_web_content_processor.return_value = mock_processor_instance
        
        # 准备测试数据
        source_list = [
            {
                "url": "https://example.com/doc1",
                "title": "Document 1",
                "description": "Test document 1"
            },
            {
                "url": "https://example.com/doc2",
                "title": "Document 2",
                "description": "Test document 2"
            }
        ]
        
        # 执行工作流
        result = await collect_raw_information(source_list)
        
        # 验证结果
        assert "raw_information_files" in result
        assert "metadata" in result
        
        metadata = result["metadata"]
        assert metadata["total_sources"] == 2
        assert metadata["successful_extractions"] == 1
        assert metadata["failed_extractions"] == 1
        
        # 验证文件信息
        files = result["raw_information_files"]
        assert len(files) == 1
        assert files[0]["url"] == "https://example.com/doc1"
        assert files[0]["raw_content_path"] == "data/raw_content/hash1.html"
        assert files[0]["markdown_path"] == "data/markdown_content/hash1.md"
    
    async def test_concurrent_processing_integration(self):
        """测试并发处理集成"""
        from src.backend.utils.concurrent_processor import ConcurrentProcessor
        
        processor = ConcurrentProcessor()
        
        # 模拟处理函数
        processed_urls = []
        
        async def mock_process_func(url: str):
            processed_urls.append(url)
            await asyncio.sleep(0.1)  # 模拟处理时间
            return {
                "url": url,
                "success": True,
                "processing_time": 0.1
            }
        
        urls = [f"https://example.com/page{i}" for i in range(10)]
        
        # 测试并发处理
        start_time = datetime.now()
        results = await processor.process_urls_concurrently(
            urls=urls,
            process_func=mock_process_func,
            max_concurrent=3
        )
        end_time = datetime.now()
        
        # 验证结果
        assert len(results) == 10
        assert len(processed_urls) == 10
        
        # 验证并发性能（应该比串行处理快）
        processing_time = (end_time - start_time).total_seconds()
        assert processing_time < 1.0  # 应该远小于 10 * 0.1 = 1.0 秒
        
        # 验证所有URL都被处理
        processed_url_set = set(processed_urls)
        expected_url_set = set(urls)
        assert processed_url_set == expected_url_set
    
    async def test_file_integrity_and_cleanup_integration(self):
        """测试文件完整性和清理集成"""
        from src.backend.utils.cache_integrity import verify_file_integrity, cleanup_corrupted_files
        
        cache_manager = URLCacheManager(cache_dir=self.temp_dir / "cache")
        await cache_manager.initialize()
        
        test_url = "https://example.com/integrity-test"
        
        # 创建测试文件
        raw_content_dir = self.temp_dir / "raw_content"
        markdown_content_dir = self.temp_dir / "markdown_content"
        raw_content_dir.mkdir(parents=True, exist_ok=True)
        markdown_content_dir.mkdir(parents=True, exist_ok=True)
        
        file_paths = cache_manager.generate_file_paths(test_url, "html")
        raw_file = Path(file_paths["raw_content_path"])
        markdown_file = Path(file_paths["markdown_path"])
        
        # 创建文件
        raw_file.parent.mkdir(parents=True, exist_ok=True)
        markdown_file.parent.mkdir(parents=True, exist_ok=True)
        raw_file.write_text("Raw content")
        markdown_file.write_text("# Markdown content")
        
        # 标记为已处理
        await cache_manager.mark_url_processed(
            url=test_url,
            success=True,
            raw_content_path=str(raw_file),
            markdown_path=str(markdown_file)
        )
        
        # 验证文件完整性
        integrity = await verify_file_integrity(test_url, cache_manager.url_metadata)
        assert integrity["metadata_exists"] is True
        assert integrity["raw_content_exists"] is True
        assert integrity["markdown_exists"] is True
        
        # 测试清理功能
        cleaned_files = await cleanup_corrupted_files(test_url, cache_manager.url_metadata)
        assert "raw_content" in cleaned_files
        assert "markdown" in cleaned_files
        
        # 验证文件已被删除
        assert not raw_file.exists()
        assert not markdown_file.exists()


if __name__ == "__main__":
    pytest.main([__file__])
