"""
网络重试机制测试

测试网络请求重试功能的正确性和有效性
"""

import asyncio
import pytest
import httpx
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from src.backend.agents.web_content_processor import WebScraper
from src.backend.config import NetworkRetryConfig


class TestNetworkRetry:
    """网络重试机制测试类"""

    def test_retry_config_defaults(self):
        """测试重试配置的默认值"""
        config = NetworkRetryConfig()
        
        assert config.max_retries == 3
        assert config.initial_delay == 1.0
        assert config.max_delay == 60.0
        assert config.backoff_factor == 2.0
        assert config.jitter is True
        assert config.retry_on_timeout is True
        assert config.retry_on_connection_error is True
        assert config.retry_on_http_error is False
        assert config.retry_http_status_codes == [429, 502, 503, 504]

    def test_retry_config_validation(self):
        """测试重试配置的验证"""
        # 测试有效配置
        config = NetworkRetryConfig(
            max_retries=5,
            initial_delay=0.5,
            max_delay=30.0,
            backoff_factor=1.5
        )
        assert config.max_retries == 5
        assert config.initial_delay == 0.5
        
        # 测试无效配置会使用默认值或抛出异常
        with pytest.raises(ValueError):
            NetworkRetryConfig(max_retries=-1)
        
        with pytest.raises(ValueError):
            NetworkRetryConfig(max_retries=15)  # 超过最大值

    def test_calculate_retry_delay(self):
        """测试重试延迟计算"""
        config = NetworkRetryConfig(
            initial_delay=1.0,
            max_delay=10.0,
            backoff_factor=2.0,
            jitter=False  # 关闭抖动以便精确测试
        )
        scraper = WebScraper(retry_config=config)
        
        # 测试指数退避
        assert scraper._calculate_retry_delay(0) == 1.0  # 1.0 * 2^0
        assert scraper._calculate_retry_delay(1) == 2.0  # 1.0 * 2^1
        assert scraper._calculate_retry_delay(2) == 4.0  # 1.0 * 2^2
        assert scraper._calculate_retry_delay(3) == 8.0  # 1.0 * 2^3
        
        # 测试最大延迟限制
        assert scraper._calculate_retry_delay(4) == 10.0  # 受max_delay限制

    def test_calculate_retry_delay_with_jitter(self):
        """测试带抖动的重试延迟计算"""
        config = NetworkRetryConfig(
            initial_delay=4.0,
            max_delay=60.0,
            backoff_factor=2.0,
            jitter=True
        )
        scraper = WebScraper(retry_config=config)
        
        # 测试抖动范围（±25%）
        delay = scraper._calculate_retry_delay(0)
        assert 3.0 <= delay <= 5.0  # 4.0 ± 1.0
        
        # 多次计算应该有不同结果（由于随机性）
        delays = [scraper._calculate_retry_delay(0) for _ in range(10)]
        assert len(set(delays)) > 1  # 应该有不同的值

    def test_should_retry_logic(self):
        """测试重试判断逻辑"""
        config = NetworkRetryConfig(
            max_retries=3,
            retry_on_timeout=True,
            retry_on_connection_error=True,
            retry_on_http_error=True,
            retry_http_status_codes=[429, 502, 503, 504]
        )
        scraper = WebScraper(retry_config=config)
        
        # 测试超时异常
        timeout_error = httpx.TimeoutException("Timeout")
        assert scraper._should_retry(timeout_error, 0) is True
        assert scraper._should_retry(timeout_error, 2) is True
        assert scraper._should_retry(timeout_error, 3) is False  # 超过最大重试次数
        
        # 测试连接错误
        request_error = httpx.RequestError("Connection failed")
        assert scraper._should_retry(request_error, 0) is True
        assert scraper._should_retry(request_error, 3) is False
        
        # 测试HTTP状态错误
        mock_response = MagicMock()
        mock_response.status_code = 503
        http_error = httpx.HTTPStatusError("Service Unavailable", request=None, response=mock_response)
        assert scraper._should_retry(http_error, 0) is True
        
        # 测试不重试的HTTP状态码
        mock_response.status_code = 404
        http_error_404 = httpx.HTTPStatusError("Not Found", request=None, response=mock_response)
        assert scraper._should_retry(http_error_404, 0) is False

    @pytest.mark.asyncio
    async def test_fetch_url_success_first_attempt(self):
        """测试第一次尝试就成功的情况"""
        config = NetworkRetryConfig(max_retries=3)
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            # 模拟成功响应
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.text = "Test content"
            mock_response.headers = {"content-type": "text/html"}
            mock_response.content = b"Test content"
            mock_response.raise_for_status.return_value = None
            
            mock_client.get.return_value = mock_response
            
            scraper = WebScraper(retry_config=config)
            result = await scraper.fetch_url("https://example.com")
            
            assert result["success"] is True
            assert result["content"] == "Test content"
            assert result["attempts"] == 1
            assert mock_client.get.call_count == 1

    @pytest.mark.asyncio
    async def test_fetch_url_retry_on_timeout(self):
        """测试超时重试机制"""
        config = NetworkRetryConfig(
            max_retries=2,
            initial_delay=0.1,  # 快速测试
            jitter=False
        )
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            # 前两次超时，第三次成功
            timeout_error = httpx.TimeoutException("Timeout")
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.text = "Success after retry"
            mock_response.headers = {"content-type": "text/html"}
            mock_response.content = b"Success after retry"
            mock_response.raise_for_status.return_value = None
            
            mock_client.get.side_effect = [timeout_error, timeout_error, mock_response]
            
            scraper = WebScraper(retry_config=config)
            result = await scraper.fetch_url("https://example.com")
            
            assert result["success"] is True
            assert result["content"] == "Success after retry"
            assert result["attempts"] == 3
            assert mock_client.get.call_count == 3

    @pytest.mark.asyncio
    async def test_fetch_url_max_retries_exceeded(self):
        """测试超过最大重试次数的情况"""
        config = NetworkRetryConfig(
            max_retries=2,
            initial_delay=0.1,
            jitter=False
        )
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            # 所有尝试都超时
            timeout_error = httpx.TimeoutException("Timeout")
            mock_client.get.side_effect = timeout_error
            
            scraper = WebScraper(retry_config=config)
            result = await scraper.fetch_url("https://example.com")
            
            assert result["success"] is False
            assert result["error_type"] == "timeout"
            assert result["attempts"] == 3  # 1 + 2 retries
            assert "after 3 attempts" in result["error"]
            assert mock_client.get.call_count == 3

    @pytest.mark.asyncio
    async def test_fetch_url_no_retry_on_non_retryable_error(self):
        """测试不可重试错误的处理"""
        config = NetworkRetryConfig(
            max_retries=3,
            retry_on_http_error=False  # 不重试HTTP错误
        )
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            # 404错误不应该重试
            mock_response = MagicMock()
            mock_response.status_code = 404
            http_error = httpx.HTTPStatusError("Not Found", request=None, response=mock_response)
            mock_client.get.side_effect = http_error
            
            scraper = WebScraper(retry_config=config)
            result = await scraper.fetch_url("https://example.com")
            
            assert result["success"] is False
            assert result["error_type"] == "http_error"
            assert result["attempts"] == 1  # 没有重试
            assert mock_client.get.call_count == 1

    @pytest.mark.asyncio
    async def test_fetch_url_retry_delay(self):
        """测试重试延迟机制"""
        config = NetworkRetryConfig(
            max_retries=2,
            initial_delay=0.1,
            backoff_factor=2.0,
            jitter=False
        )
        
        with patch('httpx.AsyncClient') as mock_client_class, \
             patch('asyncio.sleep') as mock_sleep:
            
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            # 前两次超时，第三次成功
            timeout_error = httpx.TimeoutException("Timeout")
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.text = "Success"
            mock_response.headers = {}
            mock_response.content = b"Success"
            mock_response.raise_for_status.return_value = None
            
            mock_client.get.side_effect = [timeout_error, timeout_error, mock_response]
            
            scraper = WebScraper(retry_config=config)
            await scraper.fetch_url("https://example.com")
            
            # 验证延迟调用
            assert mock_sleep.call_count == 2
            mock_sleep.assert_any_call(0.1)  # 第一次重试延迟
            mock_sleep.assert_any_call(0.2)  # 第二次重试延迟


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
