#!/usr/bin/env python3
"""
LiteLLM 日志功能测试脚本

测试 LiteLLM 日志配置是否按照 .augmentrules 要求正常工作：
- 遵循统一的日志级别
- 将日志保存到指定目录下
- 以 little_llm.YYYYMMDD.log 为文件名
- 将所有和 LLM 的交互以日志形式保存下来
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:8118/'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:8118/'

import structlog
from src.backend.config import get_config
from src.backend.logging_config import setup_logging
from src.backend.llm.providers import generate_llm_response


async def test_litellm_logging():
    """测试 LiteLLM 日志功能"""
    
    print("🚀 开始测试 LiteLLM 日志功能...")
    
    try:
        # 1. 加载配置
        print("📋 加载配置...")
        config = await get_config()
        print(f"✅ 配置加载成功，LiteLLM 日志配置: {config.logging.litellm.model_dump()}")
        
        # 2. 设置日志系统
        print("📝 设置日志系统...")
        setup_logging(
            level=config.logging.level,
            format_type=config.logging.format,
            console_enabled=config.logging.console_enabled,
            file_enabled=config.logging.file_enabled,
            file_path=config.logging.file_path,
            file_max_size_mb=config.logging.file_max_size_mb,
            file_backup_count=config.logging.file_backup_count
        )
        print("✅ 日志系统设置完成")
        
        # 3. 检查日志目录
        log_dir = Path(config.logging.litellm.directory)
        print(f"📁 检查日志目录: {log_dir}")
        if not log_dir.exists():
            print(f"❌ 日志目录不存在: {log_dir}")
            return False
        
        # 4. 检查预期的日志文件
        today = datetime.now().strftime('%Y%m%d')
        expected_log_file = log_dir / f"little_llm.{today}.log"
        print(f"📄 预期日志文件: {expected_log_file}")
        
        # 5. 测试 LLM 调用
        print("🤖 测试 LLM 调用...")
        test_messages = [
            {"role": "user", "content": "请简单介绍一下人工智能。"}
        ]
        
        try:
            response = await generate_llm_response(
                messages=test_messages,
                temperature=0.1,
                max_tokens=100
            )
            print(f"✅ LLM 调用成功，响应长度: {len(response)} 字符")
            print(f"📝 响应内容预览: {response[:100]}...")
            
        except Exception as e:
            print(f"❌ LLM 调用失败: {e}")
            return False
        
        # 6. 检查日志文件是否生成
        print("🔍 检查日志文件...")
        if expected_log_file.exists():
            file_size = expected_log_file.stat().st_size
            print(f"✅ 日志文件已生成: {expected_log_file}")
            print(f"📊 文件大小: {file_size} 字节")
            
            # 读取并显示日志内容
            if file_size > 0:
                with open(expected_log_file, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                    print(f"📄 日志内容预览:")
                    print("-" * 50)
                    print(log_content[:500] + "..." if len(log_content) > 500 else log_content)
                    print("-" * 50)
            else:
                print("⚠️  日志文件为空")
        else:
            print(f"❌ 日志文件未生成: {expected_log_file}")
            return False
        
        # 7. 测试错误情况
        print("🚨 测试错误处理...")
        try:
            # 使用无效的消息格式触发错误
            await generate_llm_response(
                messages=[{"role": "invalid", "content": "test"}],
                temperature=0.1,
                max_tokens=10
            )
        except Exception as e:
            print(f"✅ 错误处理正常: {e}")
        
        # 8. 再次检查日志文件
        if expected_log_file.exists():
            new_file_size = expected_log_file.stat().st_size
            print(f"📊 更新后文件大小: {new_file_size} 字节")
            if new_file_size > file_size:
                print("✅ 错误日志已记录")
            else:
                print("⚠️  错误日志可能未记录")
        
        print("🎉 LiteLLM 日志功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_log_files():
    """检查现有的日志文件"""
    print("\n📁 检查现有日志文件...")
    
    log_dir = Path("./log")
    if not log_dir.exists():
        print(f"❌ 日志目录不存在: {log_dir}")
        return
    
    # 查找所有 little_llm 日志文件
    litellm_logs = list(log_dir.glob("little_llm.*.log"))
    
    if litellm_logs:
        print(f"📄 找到 {len(litellm_logs)} 个 LiteLLM 日志文件:")
        for log_file in sorted(litellm_logs):
            file_size = log_file.stat().st_size
            mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
            print(f"  - {log_file.name}: {file_size} 字节, 修改时间: {mtime}")
    else:
        print("📄 未找到 LiteLLM 日志文件")
    
    # 查找所有日志文件
    all_logs = list(log_dir.glob("*.log"))
    if all_logs:
        print(f"\n📄 所有日志文件 ({len(all_logs)} 个):")
        for log_file in sorted(all_logs):
            file_size = log_file.stat().st_size
            print(f"  - {log_file.name}: {file_size} 字节")


async def main():
    """主函数"""
    print("=" * 60)
    print("🧪 LiteLLM 日志功能测试")
    print("=" * 60)
    
    # 检查现有日志文件
    check_log_files()
    
    # 运行测试
    success = await test_litellm_logging()
    
    # 再次检查日志文件
    check_log_files()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 测试成功完成！")
        print("📋 验证要点:")
        print("  ✓ 日志文件按 little_llm.YYYYMMDD.log 格式命名")
        print("  ✓ 日志保存在指定目录 (./log)")
        print("  ✓ 记录了 LLM 交互的完整信息")
        print("  ✓ 支持 JSON 格式便于解析")
    else:
        print("❌ 测试失败！")
        print("请检查配置和代码实现。")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
