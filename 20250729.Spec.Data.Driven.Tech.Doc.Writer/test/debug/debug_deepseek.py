#!/usr/bin/env python3
"""
调试DeepSeek API调用
"""

import asyncio
import sys
import httpx
import json
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import get_config

async def debug_deepseek():
    """调试DeepSeek API调用"""
    try:
        print("🔄 Loading configuration...")
        config = await get_config()
        
        # 获取DeepSeek配置
        deepseek_config = config.llm_primary
        print(f"DeepSeek API Key: {deepseek_config.api_key[:10]}...")
        print(f"DeepSeek Model: {deepseek_config.model}")
        print(f"DeepSeek Base URL: {deepseek_config.base_url}")
        
        # 获取代理配置
        import os
        proxy_url = os.getenv('HTTPS_PROXY') or os.getenv('https_proxy')
        print(f"Proxy URL: {proxy_url}")
        
        # 构造请求
        headers = {
            'Authorization': f'Bearer {deepseek_config.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': deepseek_config.model,
            'messages': [
                {
                    'role': 'user',
                    'content': '请简单回答：1+1等于多少？'
                }
            ],
            'temperature': 0.1,
            'max_tokens': 100
        }
        
        print("\n🔄 Making DeepSeek API request...")
        print(f"URL: {deepseek_config.base_url}/chat/completions")
        print(f"Headers: {headers}")
        print(f"Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        async with httpx.AsyncClient(proxy=proxy_url, timeout=30.0) as client:
            response = await client.post(
                f"{deepseek_config.base_url}/chat/completions",
                headers=headers,
                json=data
            )
            
            print(f"\n📥 Response Status: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ Success! Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
                content = result['choices'][0]['message']['content']
                print(f"✓ Content: {content}")
            else:
                print(f"✗ Error! Response: {response.text}")
                response.raise_for_status()
        
    except Exception as e:
        print(f"✗ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_deepseek())
