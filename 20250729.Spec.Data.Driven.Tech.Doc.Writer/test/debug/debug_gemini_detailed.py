#!/usr/bin/env python3
"""
详细调试Gemini API调用问题
"""

import asyncio
import sys
import os
import time
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_gemini_step_by_step():
    """逐步测试Gemini API调用"""
    
    print("=" * 60)
    print("Gemini API 详细调试")
    print("=" * 60)
    
    # 步骤1: 检查环境变量
    print("\n🔍 步骤1: 检查环境变量")
    print(f"HTTP_PROXY: {os.getenv('HTTP_PROXY')}")
    print(f"HTTPS_PROXY: {os.getenv('HTTPS_PROXY')}")
    print(f"NO_PROXY: {os.getenv('NO_PROXY')}")
    
    # 步骤2: 设置代理环境变量
    print("\n🔧 步骤2: 设置代理环境变量")
    os.environ['HTTP_PROXY'] = 'http://127.0.0.1:8118/'
    os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:8118/'
    print("✓ 代理环境变量已设置")
    
    # 步骤3: 导入Google AI库
    print("\n📦 步骤3: 导入Google AI库")
    try:
        import google.generativeai as genai
        print("✓ Google Generative AI 库导入成功")
        print(f"版本信息: {genai.__version__ if hasattr(genai, '__version__') else '未知'}")
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return
    
    # 步骤4: 配置API密钥
    print("\n🔑 步骤4: 配置API密钥")
    api_key = "AIzaSyDNlCj2Dm9Jh9dKy6GT_5jIGvlUAZj4WXw"
    try:
        genai.configure(api_key=api_key)
        print("✓ API密钥配置成功")
    except Exception as e:
        print(f"✗ API密钥配置失败: {e}")
        return
    
    # 步骤5: 创建模型实例
    print("\n🤖 步骤5: 创建模型实例")
    try:
        model = genai.GenerativeModel('gemini-2.5-pro')
        print("✓ 模型实例创建成功")
    except Exception as e:
        print(f"✗ 模型创建失败: {e}")
        return
    
    # 步骤6: 测试简单的同步调用
    print("\n🔄 步骤6: 测试同步调用")
    try:
        print("发送简单测试消息...")
        start_time = time.time()
        response = model.generate_content("请简单回答：1+1等于多少？")
        end_time = time.time()
        print(f"✓ 同步调用成功，耗时: {end_time - start_time:.2f}秒")
        print(f"响应: {response.text}")
    except Exception as e:
        print(f"✗ 同步调用失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        
        # 尝试获取更多错误信息
        if hasattr(e, 'response'):
            print(f"HTTP状态码: {getattr(e.response, 'status_code', '未知')}")
            print(f"响应内容: {getattr(e.response, 'text', '未知')}")
    
    # 步骤7: 测试异步调用
    print("\n🔄 步骤7: 测试异步调用")
    try:
        print("发送异步测试消息...")
        start_time = time.time()
        response = await model.generate_content_async("请简单回答：2+2等于多少？")
        end_time = time.time()
        print(f"✓ 异步调用成功，耗时: {end_time - start_time:.2f}秒")
        print(f"响应: {response.text}")
    except Exception as e:
        print(f"✗ 异步调用失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
    
    # 步骤8: 测试网络连接
    print("\n🌐 步骤8: 测试网络连接")
    try:
        import httpx
        proxy_url = 'http://127.0.0.1:8118/'
        
        print("测试直接HTTP请求到Google AI API...")
        async with httpx.AsyncClient(proxy=proxy_url, timeout=10.0) as client:
            response = await client.get('https://generativelanguage.googleapis.com/')
            print(f"✓ 网络连接测试成功，状态码: {response.status_code}")
    except Exception as e:
        print(f"✗ 网络连接测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("调试完成")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_gemini_step_by_step())
