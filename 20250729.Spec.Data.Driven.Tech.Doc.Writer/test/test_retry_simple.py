#!/usr/bin/env python3
"""
简单的重试机制测试

测试重试配置和逻辑的基本功能，不依赖外部模块
"""

import random
import time
from typing import Dict, Any


class SimpleNetworkRetryConfig:
    """简化的网络重试配置"""
    
    def __init__(self, 
                 max_retries: int = 3,
                 initial_delay: float = 1.0,
                 max_delay: float = 60.0,
                 backoff_factor: float = 2.0,
                 jitter: bool = True):
        self.max_retries = max_retries
        self.initial_delay = initial_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        self.jitter = jitter


class SimpleWebScraper:
    """简化的网页抓取器，用于测试重试逻辑"""
    
    def __init__(self, retry_config: SimpleNetworkRetryConfig = None):
        self.retry_config = retry_config or SimpleNetworkRetryConfig()
    
    def _calculate_retry_delay(self, attempt: int) -> float:
        """计算重试延迟"""
        delay = self.retry_config.initial_delay * (self.retry_config.backoff_factor ** attempt)
        delay = min(delay, self.retry_config.max_delay)
        
        if self.retry_config.jitter:
            # 添加随机抖动 (±25% of delay)
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
            delay = max(0.1, delay)  # 确保最小延迟
        
        return delay
    
    def _should_retry(self, error_type: str, attempt: int) -> bool:
        """判断是否应该重试"""
        if attempt >= self.retry_config.max_retries:
            return False
        
        # 简化的重试逻辑
        retryable_errors = ["timeout", "connection_error", "503", "502", "429"]
        return error_type in retryable_errors
    
    def simulate_fetch_url(self, url: str, failure_mode: str = None) -> Dict[str, Any]:
        """模拟网络请求，支持不同的失败模式"""
        print(f"模拟请求: {url}")
        
        last_error = None
        
        for attempt in range(self.retry_config.max_retries + 1):
            try:
                if attempt > 0:
                    delay = self._calculate_retry_delay(attempt - 1)
                    print(f"  重试 {attempt}/{self.retry_config.max_retries}, 延迟 {delay:.2f}秒")
                    time.sleep(min(delay, 0.1))  # 实际测试中使用较短延迟
                
                # 模拟不同的失败情况
                if failure_mode == "success_after_retry" and attempt < 2:
                    raise Exception("timeout")
                elif failure_mode == "always_fail":
                    raise Exception("connection_error")
                elif failure_mode == "non_retryable":
                    raise Exception("404")
                elif failure_mode is None:
                    # 成功情况
                    pass
                else:
                    # 默认成功
                    pass
                
                # 成功
                return {
                    "url": url,
                    "success": True,
                    "content": f"Success content for {url}",
                    "attempts": attempt + 1,
                    "status_code": 200
                }
                
            except Exception as e:
                last_error = str(e)
                
                if not self._should_retry(last_error, attempt):
                    break
                
                print(f"  尝试 {attempt + 1} 失败: {last_error}")
        
        # 所有重试都失败了
        return {
            "url": url,
            "success": False,
            "error": f"Failed after {attempt + 1} attempts: {last_error}",
            "error_type": last_error,
            "attempts": attempt + 1
        }


def test_retry_config():
    """测试重试配置"""
    print("=== 测试重试配置 ===")
    
    # 默认配置
    config = SimpleNetworkRetryConfig()
    print(f"默认最大重试次数: {config.max_retries}")
    print(f"默认初始延迟: {config.initial_delay}秒")
    print(f"默认最大延迟: {config.max_delay}秒")
    print(f"默认退避因子: {config.backoff_factor}")
    print(f"默认启用抖动: {config.jitter}")
    
    # 自定义配置
    custom_config = SimpleNetworkRetryConfig(
        max_retries=2,
        initial_delay=0.5,
        max_delay=10.0,
        backoff_factor=1.5,
        jitter=False
    )
    print(f"\n自定义最大重试次数: {custom_config.max_retries}")
    print(f"自定义初始延迟: {custom_config.initial_delay}秒")


def test_delay_calculation():
    """测试延迟计算"""
    print("\n=== 测试延迟计算 ===")
    
    # 无抖动测试
    config = SimpleNetworkRetryConfig(
        initial_delay=1.0,
        max_delay=10.0,
        backoff_factor=2.0,
        jitter=False
    )
    scraper = SimpleWebScraper(config)
    
    print("指数退避延迟计算（无抖动）:")
    for i in range(5):
        delay = scraper._calculate_retry_delay(i)
        print(f"  尝试 {i}: {delay:.2f}秒")
    
    # 带抖动测试
    config_jitter = SimpleNetworkRetryConfig(
        initial_delay=2.0,
        max_delay=20.0,
        backoff_factor=2.0,
        jitter=True
    )
    scraper_jitter = SimpleWebScraper(config_jitter)
    
    print("\n指数退避延迟计算（带抖动）:")
    for i in range(3):
        delays = [scraper_jitter._calculate_retry_delay(i) for _ in range(3)]
        print(f"  尝试 {i}: {[f'{d:.2f}' for d in delays]} 秒")


def test_retry_scenarios():
    """测试不同的重试场景"""
    print("\n=== 测试重试场景 ===")
    
    config = SimpleNetworkRetryConfig(max_retries=3, initial_delay=0.1, jitter=False)
    scraper = SimpleWebScraper(config)
    
    # 场景1: 立即成功
    print("\n场景1: 立即成功")
    result = scraper.simulate_fetch_url("https://example.com", None)
    print(f"结果: {'成功' if result['success'] else '失败'}")
    print(f"尝试次数: {result['attempts']}")
    
    # 场景2: 重试后成功
    print("\n场景2: 重试后成功")
    result = scraper.simulate_fetch_url("https://example.com", "success_after_retry")
    print(f"结果: {'成功' if result['success'] else '失败'}")
    print(f"尝试次数: {result['attempts']}")
    
    # 场景3: 所有重试都失败
    print("\n场景3: 所有重试都失败")
    result = scraper.simulate_fetch_url("https://example.com", "always_fail")
    print(f"结果: {'成功' if result['success'] else '失败'}")
    print(f"尝试次数: {result['attempts']}")
    print(f"错误: {result.get('error', 'N/A')}")
    
    # 场景4: 不可重试的错误
    print("\n场景4: 不可重试的错误")
    result = scraper.simulate_fetch_url("https://example.com", "non_retryable")
    print(f"结果: {'成功' if result['success'] else '失败'}")
    print(f"尝试次数: {result['attempts']}")
    print(f"错误: {result.get('error', 'N/A')}")


def test_should_retry_logic():
    """测试重试判断逻辑"""
    print("\n=== 测试重试判断逻辑 ===")
    
    config = SimpleNetworkRetryConfig(max_retries=3)
    scraper = SimpleWebScraper(config)
    
    test_cases = [
        ("timeout", 0, True),
        ("timeout", 3, False),  # 超过最大重试次数
        ("connection_error", 1, True),
        ("503", 2, True),
        ("404", 0, False),  # 不可重试的错误
        ("500", 0, False),  # 不在重试列表中
    ]
    
    for error_type, attempt, expected in test_cases:
        result = scraper._should_retry(error_type, attempt)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {error_type} (尝试{attempt}): {result} (期望: {expected})")


def main():
    """主测试函数"""
    print("🚀 开始测试网络重试机制\n")
    
    try:
        test_retry_config()
        test_delay_calculation()
        test_should_retry_logic()
        test_retry_scenarios()
        
        print("\n✅ 所有测试完成!")
        print("\n📋 测试总结:")
        print("- ✅ 重试配置正常工作")
        print("- ✅ 延迟计算（指数退避 + 抖动）正常")
        print("- ✅ 重试判断逻辑正确")
        print("- ✅ 各种重试场景测试通过")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
