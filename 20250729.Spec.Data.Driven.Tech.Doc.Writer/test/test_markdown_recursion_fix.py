#!/usr/bin/env python3
"""
Markdown 递归深度修复测试

测试修复后的 MarkdownConverter 是否能正确处理深度嵌套的HTML。
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

import pytest
from src.backend.config import Config
from src.backend.agents.web_content_processor import MarkdownConverter


class TestMarkdownRecursionFix:
    """测试 Markdown 递归深度修复"""
    
    def setup_method(self):
        """设置测试环境"""
        self.config = Config()
        self.converter = MarkdownConverter(config=self.config)
    
    def test_normal_html_conversion(self):
        """测试正常HTML转换"""
        html_content = """
        <h1>Test Title</h1>
        <p>This is a <strong>test</strong> paragraph.</p>
        <ul>
            <li>Item 1</li>
            <li>Item 2</li>
        </ul>
        """
        
        result = self.converter.convert_to_markdown(html_content, "https://test.com")
        
        assert result["success"] is True
        assert "# Test Title" in result["markdown_content"]
        assert "**test**" in result["markdown_content"]
        assert result["word_count"] > 0
        assert "fallback_strategy" not in result  # 应该不需要降级
    
    def test_deeply_nested_html(self):
        """测试深度嵌套HTML"""
        # 创建深度嵌套的HTML（超过默认递归限制）
        depth = 1500  # 超过默认的1000递归限制
        deeply_nested_html = "<div>" * depth + "Deep Content" + "</div>" * depth
        
        result = self.converter.convert_to_markdown(deeply_nested_html, "https://deep.test.com")
        
        # 应该成功转换（通过增加递归限制或降级策略）
        assert result["success"] is True
        assert "Deep Content" in result["markdown_content"]
        assert result["word_count"] > 0
    
    def test_extremely_nested_html_with_fallback(self):
        """测试极度嵌套HTML，触发降级策略"""
        # 创建极度复杂的嵌套结构
        depth = 3000  # 可能超过增加后的递归限制
        complex_html = f"""
        <div>{'<div><span><p>' * depth}
        Extremely nested content
        {'</p></span></div>' * depth}</div>
        """
        
        result = self.converter.convert_to_markdown(complex_html, "https://extreme.test.com")
        
        # 应该成功转换（可能使用降级策略）
        assert result["success"] is True
        assert "nested content" in result["markdown_content"].lower()
        
        # 检查是否使用了降级策略
        if "fallback_strategy" in result:
            assert result["fallback_strategy"] in ["preprocessing", "text_extraction"]
    
    def test_html_preprocessing(self):
        """测试HTML预处理功能"""
        # 创建需要预处理的HTML
        nested_html = """
        <div><div><div><div><div><div><div><div><div><div>
        <div><div><div><div><div><div><div><div><div><div>
        <div><div><div><div><div><div><div><div><div><div>
        <h1>Title</h1>
        <p>Content that should be preserved</p>
        </div></div></div></div></div></div></div></div></div></div>
        </div></div></div></div></div></div></div></div></div></div>
        </div></div></div></div></div></div></div></div></div></div>
        """
        
        # 测试预处理功能
        preprocessed = self.converter._preprocess_html(nested_html)
        
        # 预处理后的HTML应该更简单
        assert len(preprocessed) <= len(nested_html)
        assert "Title" in preprocessed
        assert "Content that should be preserved" in preprocessed
    
    def test_text_extraction_fallback(self):
        """测试文本提取降级策略"""
        html_with_scripts = """
        <html>
        <head>
            <script>alert('test');</script>
            <style>body { color: red; }</style>
        </head>
        <body>
            <h1>Main Title</h1>
            <p>Important content here</p>
            <script>console.log('more scripts');</script>
        </body>
        </html>
        """
        
        result = self.converter._extract_text_content(html_with_scripts, "https://script.test.com")
        
        assert result["success"] is True
        assert "Main Title" in result["markdown_content"]
        assert "Important content" in result["markdown_content"]
        assert "alert" not in result["markdown_content"]  # 脚本应该被移除
        assert "color: red" not in result["markdown_content"]  # 样式应该被移除
        assert result["conversion_method"] == "text_extraction"
    
    def test_configuration_loading(self):
        """测试配置加载"""
        # 验证配置正确加载
        markdown_config = self.config.content_processing.markdown
        
        assert markdown_config.recursion_limit >= 1000
        assert markdown_config.max_html_depth >= 10
        assert isinstance(markdown_config.enable_preprocessing, bool)
        assert isinstance(markdown_config.fallback_strategies, list)
        assert len(markdown_config.fallback_strategies) > 0
    
    def test_recursion_limit_management(self):
        """测试递归限制管理"""
        import sys
        
        original_limit = sys.getrecursionlimit()
        
        # 测试简单HTML转换
        simple_html = "<p>Test</p>"
        result = self.converter._convert_with_increased_limit(simple_html, "https://simple.test.com")
        
        # 验证递归限制被正确恢复
        assert sys.getrecursionlimit() == original_limit
        assert result["success"] is True
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效HTML
        invalid_html = None
        
        try:
            result = self.converter.convert_to_markdown(invalid_html, "https://invalid.test.com")
            # 应该优雅地处理错误
            assert result["success"] is False
            assert "error" in result
        except Exception:
            # 如果抛出异常，也是可以接受的
            pass


def test_integration():
    """集成测试"""
    print("🧪 开始 Markdown 递归深度修复集成测试")
    
    config = Config()
    converter = MarkdownConverter(config=config)
    
    # 测试配置
    print(f"📋 配置信息:")
    print(f"   递归限制: {config.content_processing.markdown.recursion_limit}")
    print(f"   最大HTML深度: {config.content_processing.markdown.max_html_depth}")
    print(f"   预处理启用: {config.content_processing.markdown.enable_preprocessing}")
    print(f"   降级策略: {config.content_processing.markdown.fallback_strategies}")
    
    # 测试深度嵌套HTML
    print("\n🔍 测试深度嵌套HTML处理...")
    depth = 1200
    nested_html = "<div>" * depth + "Deep Test Content" + "</div>" * depth
    
    result = converter.convert_to_markdown(nested_html, "https://test.example.com")
    
    print(f"✅ 转换结果:")
    print(f"   成功: {result['success']}")
    print(f"   字数: {result.get('word_count', 0)}")
    print(f"   行数: {result.get('line_count', 0)}")
    
    if "fallback_strategy" in result:
        print(f"   使用降级策略: {result['fallback_strategy']}")
    
    if "conversion_method" in result:
        print(f"   转换方法: {result['conversion_method']}")
    
    print("\n🎉 集成测试完成!")
    return result["success"]


if __name__ == "__main__":
    # 运行集成测试
    success = test_integration()
    
    if success:
        print("\n✅ 所有测试通过！Markdown 递归深度修复成功。")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！需要进一步检查。")
        sys.exit(1)
