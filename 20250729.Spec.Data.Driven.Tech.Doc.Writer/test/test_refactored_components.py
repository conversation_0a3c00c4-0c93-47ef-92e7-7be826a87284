"""
测试重构后的组件

验证重构后的 URLCacheManager、WebContentProcessor 和相关工具的正确性。
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
import tempfile
import json
from datetime import datetime

from src.backend.agents.url_cache_manager import URLCacheManager
from src.backend.utils.url_utils import generate_url_hash, normalize_url_for_hash, get_file_extension_from_url
from src.backend.utils.url_processing_lock import URLProcessingLock, URLAlreadyProcessingError
from src.backend.utils.markitdown_processor import MarkitdownProcessor
from src.backend.utils.content_file_manager import ContentFileManager
from src.backend.utils.cache_integrity import verify_file_integrity, cleanup_corrupted_files
from src.backend.utils.cache_persistence import load_url_status_cache, save_cache_data
from src.backend.utils.cache_stats import calculate_cache_stats
from src.backend.utils.concurrent_processor import ConcurrentProcessor


class TestURLUtils:
    """测试URL工具函数"""
    
    def test_normalize_url_for_hash(self):
        """测试URL标准化"""
        # 测试去除fragment
        assert normalize_url_for_hash("https://example.com/page#section") == "https://example.com/page"
        
        # 测试小写转换
        assert normalize_url_for_hash("HTTPS://EXAMPLE.COM/PAGE") == "https://example.com/page"
        
        # 测试默认端口移除
        assert normalize_url_for_hash("https://example.com:443/page") == "https://example.com/page"
        assert normalize_url_for_hash("http://example.com:80/page") == "http://example.com/page"
        
        # 测试查询参数排序
        url1 = normalize_url_for_hash("https://example.com/page?b=2&a=1")
        url2 = normalize_url_for_hash("https://example.com/page?a=1&b=2")
        assert url1 == url2
    
    def test_generate_url_hash(self):
        """测试URL哈希生成"""
        url1 = "https://example.com/page"
        url2 = "https://example.com/page#section"
        
        hash1 = generate_url_hash(url1)
        hash2 = generate_url_hash(url2)
        
        # 相同的标准化URL应该生成相同的哈希
        assert hash1 == hash2
        assert len(hash1) == 32  # MD5哈希长度
    
    def test_get_file_extension_from_url(self):
        """测试从URL提取文件扩展名"""
        assert get_file_extension_from_url("https://example.com/file.pdf") == "pdf"
        assert get_file_extension_from_url("https://example.com/file.docx") == "docx"
        assert get_file_extension_from_url("https://example.com/page") == "html"
        assert get_file_extension_from_url("https://example.com/page.html?param=1") == "html"


class TestURLCacheManagerV2:
    """测试重构后的URL缓存管理器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.cache_manager = URLCacheManager(cache_dir=self.temp_dir / "cache")
    
    async def test_initialization(self):
        """测试初始化"""
        await self.cache_manager.initialize()
        
        assert self.cache_manager._initialized is True
        assert self.cache_manager.cache_dir.exists()
        assert self.cache_manager.raw_content_dir.exists()
        assert self.cache_manager.markdown_content_dir.exists()
    
    async def test_url_processing_workflow(self):
        """测试URL处理工作流"""
        await self.cache_manager.initialize()
        
        test_url = "https://example.com/test"
        
        # 初始状态：未处理
        assert await self.cache_manager.is_url_processed(test_url) is False
        
        # 标记为已处理
        await self.cache_manager.mark_url_processed(
            url=test_url,
            success=True,
            raw_content_path="data/raw_content/test.html",
            markdown_path="data/markdown_content/test.md",
            metadata={"test": "data"}
        )
        
        # 检查状态：已处理
        assert await self.cache_manager.is_url_processed(test_url) is True
        
        # 获取元数据
        metadata = await self.cache_manager.get_url_metadata(test_url)
        assert metadata is not None
        assert metadata["original_url"] == test_url
        assert metadata["success"] is True
        assert metadata["raw_content_path"] == "data/raw_content/test.html"
        assert metadata["markdown_path"] == "data/markdown_content/test.md"
    
    async def test_file_path_generation(self):
        """测试文件路径生成"""
        test_url = "https://example.com/test.pdf"
        
        paths = self.cache_manager.generate_file_paths(test_url)
        
        assert "url_hash" in paths
        assert "raw_content_path" in paths
        assert "markdown_path" in paths
        assert paths["raw_content_path"].endswith(".pdf")
        assert paths["markdown_path"].endswith(".md")
    
    async def test_cache_persistence(self):
        """测试缓存持久化"""
        await self.cache_manager.initialize()
        
        test_url = "https://example.com/persist"
        await self.cache_manager.mark_url_processed(url=test_url, success=True)
        
        # 创建新的缓存管理器实例
        new_cache_manager = URLCacheManager(cache_dir=self.temp_dir / "cache")
        await new_cache_manager.initialize()
        
        # 验证数据已持久化
        assert await new_cache_manager.is_url_processed(test_url) is True
    
    async def test_cache_stats(self):
        """测试缓存统计"""
        await self.cache_manager.initialize()
        
        # 添加一些测试数据
        await self.cache_manager.mark_url_processed(url="https://example.com/1", success=True)
        await self.cache_manager.mark_url_processed(url="https://example.com/2", success=False)
        
        # 模拟一些请求
        await self.cache_manager.is_url_processed("https://example.com/1")  # 缓存命中
        await self.cache_manager.is_url_processed("https://example.com/3")  # 缓存未命中
        
        stats = self.cache_manager.get_cache_stats()
        
        assert stats["cached_url_hashes_count"] == 2
        assert stats["successful_urls"] == 1
        assert stats["failed_urls"] == 1
        assert stats["total_requests"] == 2
        assert stats["cache_hits"] == 1
        assert stats["cache_misses"] == 1


class TestURLProcessingLock:
    """测试URL处理锁"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.lock_manager = URLProcessingLock(lock_dir=self.temp_dir / "locks")
    
    def test_lock_acquisition_and_release(self):
        """测试锁的获取和释放"""
        test_url = "https://example.com/test"
        
        # 正常获取和释放锁
        with self.lock_manager.acquire_url_lock(test_url) as url_hash:
            assert url_hash == generate_url_hash(test_url)
            
            # 在锁内部，同一URL应该无法再次获取锁
            with pytest.raises(URLAlreadyProcessingError):
                with self.lock_manager.acquire_url_lock(test_url):
                    pass
        
        # 锁释放后应该可以再次获取
        with self.lock_manager.acquire_url_lock(test_url):
            pass
    
    def test_stale_lock_cleanup(self):
        """测试过期锁清理"""
        # 创建一个超时时间很短的锁管理器
        short_timeout_lock = URLProcessingLock(
            lock_dir=self.temp_dir / "locks", 
            timeout=1  # 1秒超时
        )
        
        test_url = "https://example.com/stale"
        
        # 创建一个锁文件并立即退出（模拟进程异常退出）
        lock_file = short_timeout_lock.lock_dir / f"{generate_url_hash(test_url)}.lock"
        lock_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入过期的锁信息
        with open(lock_file, 'w') as f:
            f.write(f"99999:{datetime.now().timestamp() - 3600}:{test_url}")  # 1小时前的锁
        
        # 应该能够清理过期锁并获取新锁
        cleaned_count = short_timeout_lock.cleanup_stale_locks()
        assert cleaned_count >= 1
        
        # 现在应该能够正常获取锁
        with short_timeout_lock.acquire_url_lock(test_url):
            pass


class TestContentFileManager:
    """测试内容文件管理器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.file_manager = ContentFileManager()
    
    async def test_save_raw_content(self):
        """测试保存原始内容"""
        test_file = self.temp_dir / "test.html"
        test_content = "<html><body>Test content</body></html>"
        
        success = await self.file_manager.save_raw_content(test_file, test_content)
        
        assert success is True
        assert test_file.exists()
        assert test_file.read_text(encoding='utf-8') == test_content
    
    async def test_save_markdown_content(self):
        """测试保存Markdown内容"""
        test_file = self.temp_dir / "test.md"
        test_content = "# Test Markdown\n\nThis is a test."
        
        success = await self.file_manager.save_markdown_content(test_file, test_content)
        
        assert success is True
        assert test_file.exists()
        assert test_file.read_text(encoding='utf-8') == test_content
    
    def test_create_error_result(self):
        """测试创建错误结果"""
        result = self.file_manager.create_error_result(
            url="https://example.com/error",
            error="Test error",
            stage="test_stage"
        )
        
        assert result["url"] == "https://example.com/error"
        assert result["success"] is False
        assert result["error"] == "Test error"
        assert result["stage"] == "test_stage"
    
    def test_create_success_result(self):
        """测试创建成功结果"""
        result = self.file_manager.create_success_result(
            url="https://example.com/success",
            raw_content_path="data/raw_content/test.html",
            markdown_path="data/markdown_content/test.md",
            processing_time=1.5
        )
        
        assert result["url"] == "https://example.com/success"
        assert result["success"] is True
        assert result["raw_content_path"] == "data/raw_content/test.html"
        assert result["markdown_path"] == "data/markdown_content/test.md"
        assert result["processing_time"] == 1.5


class TestConcurrentProcessor:
    """测试并发处理器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.processor = ConcurrentProcessor()
    
    async def test_concurrent_processing(self):
        """测试并发处理"""
        # 模拟处理函数
        async def mock_process_func(url: str):
            await asyncio.sleep(0.1)  # 模拟处理时间
            return {
                "url": url,
                "success": True,
                "processing_time": 0.1
            }
        
        urls = [f"https://example.com/{i}" for i in range(5)]
        
        results = await self.processor.process_urls_concurrently(
            urls=urls,
            process_func=mock_process_func,
            max_concurrent=3
        )
        
        assert len(results) == 5
        for result in results:
            assert result["success"] is True
            assert "url" in result
    
    async def test_concurrent_processing_with_errors(self):
        """测试带错误的并发处理"""
        async def mock_process_func_with_errors(url: str):
            if "error" in url:
                raise ValueError("Test error")
            return {"url": url, "success": True}
        
        urls = ["https://example.com/good", "https://example.com/error", "https://example.com/good2"]
        
        results = await self.processor.process_urls_concurrently(
            urls=urls,
            process_func=mock_process_func_with_errors,
            max_concurrent=2
        )
        
        assert len(results) == 3
        
        # 检查错误处理
        error_result = next(r for r in results if "error" in r["url"])
        assert error_result["success"] is False
        assert "Test error" in error_result["error"]
        
        # 检查成功结果
        success_results = [r for r in results if r.get("success", False)]
        assert len(success_results) == 2


if __name__ == "__main__":
    pytest.main([__file__])
