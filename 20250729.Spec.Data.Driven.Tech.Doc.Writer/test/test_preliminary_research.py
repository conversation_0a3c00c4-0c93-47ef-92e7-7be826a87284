"""
初步调研工作流测试

测试初步调研工作流的完整功能，包括并行搜索、结果处理和文档生成。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List

from src.backend.agents.requirement_analyst.workflows import (
    preliminary_research,
    _prepare_search_queries,
    _parallel_search,
    _process_search_results,
    _generate_information_summary,
    _generate_source_list,
    _define_research_scope,
    _classify_source,
    _get_engines_used
)
from src.backend.models.request import SearchKeywords
from src.backend.agents.search_engines import SearchEngineService


@pytest.fixture
def sample_keywords():
    """示例搜索关键词"""
    return SearchKeywords(
        requirement_analysis_id="test-analysis-123",
        primary_keywords=["人工智能", "机器学习", "深度学习"],
        secondary_keywords=["神经网络", "算法"],
        technical_terms=["neural networks", "deep learning"],
        english_keywords=["artificial intelligence", "machine learning"],
        industry_terms=["AI技术", "智能系统"]
    )


@pytest.fixture
def sample_search_results():
    """示例搜索结果"""
    return [
        {
            "query": "人工智能",
            "results": [
                {
                    "title": "人工智能技术发展报告",
                    "content": "这是一份关于人工智能技术发展的详细报告，涵盖了最新的技术趋势和应用案例。",
                    "url": "https://example.com/ai-report",
                    "source": "tavily",
                    "quality_score": 0.9,
                    "relevance_score": 0.8,
                    "apa_citation": "Unknown Author (n.d.). 人工智能技术发展报告. Retrieved from https://example.com/ai-report",
                    "content_summary": "人工智能技术发展的综合报告",
                    "publication_date": "2024-01-01",
                    "authors": ["张三", "李四"]
                },
                {
                    "title": "机器学习基础教程",
                    "content": "机器学习的基础概念和算法介绍。",
                    "url": "https://example.com/ml-tutorial",
                    "source": "duckduckgo",
                    "quality_score": 0.7,
                    "relevance_score": 0.9,
                    "apa_citation": "Unknown Author (n.d.). 机器学习基础教程. Retrieved from https://example.com/ml-tutorial",
                    "content_summary": "机器学习基础概念介绍",
                    "publication_date": None,
                    "authors": []
                }
            ],
            "metadata": {
                "total_count": 2,
                "search_timestamp": "2024-01-01T12:00:00",
                "primary_engine": "tavily"
            }
        }
    ]


class TestSearchQueryPreparation:
    """测试搜索查询准备"""
    
    def test_prepare_search_queries(self, sample_keywords):
        """测试搜索查询准备"""
        queries = _prepare_search_queries(sample_keywords)
        
        assert isinstance(queries, list)
        assert len(queries) <= 5  # 最多5个查询
        assert "人工智能" in queries
        assert "机器学习" in queries
        
        # 验证去重
        assert len(queries) == len(set(queries))
    
    def test_prepare_search_queries_empty_keywords(self):
        """测试空关键词的查询准备"""
        empty_keywords = SearchKeywords(
            requirement_analysis_id="test-empty-123",
            primary_keywords=["dummy"],  # 至少需要一个主要关键词
            secondary_keywords=[],
            technical_terms=[],
            english_keywords=[],
            industry_terms=[]
        )
        
        queries = _prepare_search_queries(empty_keywords)
        assert isinstance(queries, list)
        assert len(queries) >= 1  # 至少有一个查询（来自主要关键词）
    
    def test_prepare_search_queries_limited_count(self, sample_keywords):
        """测试查询数量限制"""
        # 添加更多关键词
        sample_keywords.primary_keywords.extend(["额外关键词1", "额外关键词2", "额外关键词3"])
        sample_keywords.technical_terms.extend(["extra term 1", "extra term 2", "extra term 3"])
        
        queries = _prepare_search_queries(sample_keywords)
        assert len(queries) <= 5  # 应该被限制在5个以内


class TestSearchResultProcessing:
    """测试搜索结果处理"""
    
    def test_process_search_results(self, sample_search_results):
        """测试搜索结果处理"""
        processed = _process_search_results(sample_search_results)
        
        assert isinstance(processed, list)
        assert len(processed) == 2
        
        # 验证排序（按质量评分降序）
        assert processed[0]["quality_score"] >= processed[1]["quality_score"]
    
    def test_process_search_results_deduplication(self):
        """测试搜索结果去重"""
        duplicate_results = [
            {
                "results": [
                    {
                        "title": "Title 1",
                        "url": "https://example.com/same",
                        "quality_score": 0.8
                    },
                    {
                        "title": "Title 2",
                        "url": "https://example.com/different",
                        "quality_score": 0.7
                    }
                ]
            },
            {
                "results": [
                    {
                        "title": "Title 1 Duplicate",
                        "url": "https://example.com/same",  # 重复URL
                        "quality_score": 0.9
                    }
                ]
            }
        ]
        
        processed = _process_search_results(duplicate_results)
        assert len(processed) == 2  # 应该去重
        
        # 验证URL唯一性
        urls = [result["url"] for result in processed]
        assert len(urls) == len(set(urls))
    
    def test_process_empty_search_results(self):
        """测试空搜索结果处理"""
        processed = _process_search_results([])
        assert processed == []


class TestInformationSummaryGeneration:
    """测试信息摘要生成"""
    
    def test_generate_information_summary(self, sample_search_results):
        """测试信息摘要生成"""
        processed_results = _process_search_results(sample_search_results)
        summary = _generate_information_summary(processed_results)
        
        assert isinstance(summary, str)
        assert len(summary) > 0
        assert "初步调研发现" in summary or "初步调研完成" in summary
    
    def test_generate_information_summary_empty_results(self):
        """测试空结果的摘要生成"""
        summary = _generate_information_summary([])
        assert summary == "未找到相关信息。"
    
    def test_generate_information_summary_with_main_finding(self, sample_search_results):
        """测试包含主要发现的摘要生成"""
        processed_results = _process_search_results(sample_search_results)
        summary = _generate_information_summary(processed_results)
        
        # 应该包含主要发现
        assert "主要发现" in summary


class TestSourceListGeneration:
    """测试信息源清单生成"""
    
    def test_generate_source_list(self, sample_search_results):
        """测试信息源清单生成"""
        processed_results = _process_search_results(sample_search_results)
        source_list = _generate_source_list(processed_results)
        
        assert isinstance(source_list, list)
        assert len(source_list) == 2
        
        # 验证源信息结构
        for source in source_list:
            assert "title" in source
            assert "url" in source
            assert "source_type" in source
            assert "quality_score" in source
            assert "apa_citation" in source
    
    def test_generate_source_list_limit(self):
        """测试信息源清单数量限制"""
        # 创建超过15个结果
        many_results = []
        for i in range(20):
            many_results.append({
                "title": f"Title {i}",
                "url": f"https://example.com/{i}",
                "quality_score": 0.5
            })
        
        source_list = _generate_source_list(many_results)
        assert len(source_list) <= 15  # 应该被限制在15个以内


class TestResearchScopeDefinition:
    """测试调研范围界定"""
    
    def test_define_research_scope(self, sample_search_results, sample_keywords):
        """测试调研范围界定"""
        processed_results = _process_search_results(sample_search_results)
        scope = _define_research_scope(processed_results, sample_keywords)
        
        assert isinstance(scope, dict)
        assert "primary_topics" in scope
        assert "technical_areas" in scope
        assert "research_depth" in scope
        assert "information_coverage" in scope
        assert "recommended_next_steps" in scope
        
        # 验证主题和技术领域
        assert scope["primary_topics"] == sample_keywords.primary_keywords
        assert scope["technical_areas"] == sample_keywords.technical_terms
    
    def test_define_research_scope_recommendations(self, sample_keywords):
        """测试调研范围推荐"""
        # 创建信息覆盖度不足的结果
        limited_results = [
            {"title": "Title 1", "url": "https://news.example.com/1"}  # 只有新闻资源
        ]
        
        scope = _define_research_scope(limited_results, sample_keywords)
        
        # 应该有推荐的下一步行动
        assert len(scope["recommended_next_steps"]) > 0
        assert any("学术资源" in step for step in scope["recommended_next_steps"])


class TestSourceClassification:
    """测试信息源分类"""
    
    def test_classify_academic_sources(self):
        """测试学术资源分类"""
        academic_result = {"url": "https://arxiv.org/abs/1234", "title": "research paper"}
        assert _classify_source(academic_result) == "学术资源"
        
        ieee_result = {"url": "https://ieeexplore.ieee.org/document/123", "title": "ieee paper"}
        assert _classify_source(ieee_result) == "学术资源"
    
    def test_classify_industry_reports(self):
        """测试行业报告分类"""
        report_result = {"url": "https://example.com/market-report", "title": "market analysis report"}
        assert _classify_source(report_result) == "行业报告"
    
    def test_classify_news_sources(self):
        """测试新闻资源分类"""
        news_result = {"url": "https://techcrunch.com/article", "title": "tech news"}
        assert _classify_source(news_result) == "新闻资讯"
    
    def test_classify_technical_docs(self):
        """测试技术文档分类"""
        github_result = {"url": "https://github.com/user/repo", "title": "project documentation"}
        assert _classify_source(github_result) == "技术文档"
    
    def test_classify_other_sources(self):
        """测试其他资源分类"""
        other_result = {"url": "https://example.com/page", "title": "general content"}
        assert _classify_source(other_result) == "其他资源"


class TestEngineUsageTracking:
    """测试搜索引擎使用跟踪"""
    
    def test_get_engines_used(self, sample_search_results):
        """测试获取使用的搜索引擎"""
        engines = _get_engines_used(sample_search_results)
        
        assert isinstance(engines, list)
        assert "tavily" in engines
    
    def test_get_engines_used_empty_results(self):
        """测试空结果的引擎跟踪"""
        engines = _get_engines_used([])
        assert engines == []


class TestPreliminaryResearchIntegration:
    """测试初步调研集成"""
    
    @pytest.mark.asyncio
    async def test_preliminary_research_workflow(self, sample_keywords):
        """测试完整的初步调研工作流"""
        with patch('src.backend.agents.requirement_analyst.workflows.get_config') as mock_get_config, \
             patch('src.backend.agents.requirement_analyst.workflows.SearchEngineService') as mock_service_class:
            
            # 模拟配置
            mock_config = Mock()
            mock_get_config.return_value = mock_config
            
            # 模拟搜索服务
            mock_service = AsyncMock()
            mock_service_class.return_value = mock_service
            mock_service.search.return_value = {
                "query": "test",
                "results": [
                    {
                        "title": "Test Result",
                        "content": "Test content",
                        "url": "https://example.com",
                        "quality_score": 0.8,
                        "apa_citation": "Test citation"
                    }
                ],
                "metadata": {"primary_engine": "tavily"}
            }
            
            # 执行初步调研
            result = await preliminary_research(sample_keywords)
            
            # 验证结果结构
            assert isinstance(result, dict)
            assert "information_summary" in result
            assert "source_list" in result
            assert "research_scope" in result
            assert "metadata" in result
            
            # 验证元数据
            metadata = result["metadata"]
            assert "search_queries" in metadata
            assert "total_sources" in metadata
            assert "timestamp" in metadata
            assert "search_engines_used" in metadata


if __name__ == "__main__":
    pytest.main([__file__])
