#!/usr/bin/env python3
"""
手动测试网络重试机制

这个脚本用于手动验证网络重试机制的功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.backend.agents.web_content_processor import WebScraper
from src.backend.config import NetworkRetryConfig


async def test_retry_config():
    """测试重试配置"""
    print("=== 测试重试配置 ===")
    
    # 测试默认配置
    config = NetworkRetryConfig()
    print(f"默认最大重试次数: {config.max_retries}")
    print(f"默认初始延迟: {config.initial_delay}秒")
    print(f"默认最大延迟: {config.max_delay}秒")
    print(f"默认退避因子: {config.backoff_factor}")
    print(f"默认启用抖动: {config.jitter}")
    print(f"超时重试: {config.retry_on_timeout}")
    print(f"连接错误重试: {config.retry_on_connection_error}")
    print(f"HTTP错误重试: {config.retry_on_http_error}")
    print(f"重试的HTTP状态码: {config.retry_http_status_codes}")
    
    # 测试自定义配置
    custom_config = NetworkRetryConfig(
        max_retries=2,
        initial_delay=0.5,
        max_delay=10.0,
        backoff_factor=1.5,
        jitter=False
    )
    print(f"\n自定义配置最大重试次数: {custom_config.max_retries}")
    print(f"自定义配置初始延迟: {custom_config.initial_delay}秒")


async def test_delay_calculation():
    """测试延迟计算"""
    print("\n=== 测试延迟计算 ===")
    
    config = NetworkRetryConfig(
        initial_delay=1.0,
        max_delay=10.0,
        backoff_factor=2.0,
        jitter=False
    )
    scraper = WebScraper(retry_config=config)
    
    print("指数退避延迟计算（无抖动）:")
    for i in range(5):
        delay = scraper._calculate_retry_delay(i)
        print(f"  尝试 {i}: {delay}秒")
    
    # 测试带抖动的延迟
    config_with_jitter = NetworkRetryConfig(
        initial_delay=2.0,
        max_delay=20.0,
        backoff_factor=2.0,
        jitter=True
    )
    scraper_jitter = WebScraper(retry_config=config_with_jitter)
    
    print("\n指数退避延迟计算（带抖动）:")
    for i in range(3):
        delays = [scraper_jitter._calculate_retry_delay(i) for _ in range(3)]
        print(f"  尝试 {i}: {delays} 秒")


async def test_successful_request():
    """测试成功的网络请求"""
    print("\n=== 测试成功的网络请求 ===")
    
    config = NetworkRetryConfig(max_retries=3)
    scraper = WebScraper(retry_config=config)
    
    try:
        # 测试一个可靠的网站
        result = await scraper.fetch_url("https://httpbin.org/status/200")
        
        if result["success"]:
            print(f"✅ 请求成功!")
            print(f"   状态码: {result.get('status_code')}")
            print(f"   尝试次数: {result.get('attempts', 1)}")
            print(f"   响应大小: {result.get('content_size', 0)} 字节")
            print(f"   耗时: {result.get('fetch_time', 0):.2f} 秒")
        else:
            print(f"❌ 请求失败: {result.get('error')}")
            print(f"   错误类型: {result.get('error_type')}")
            print(f"   尝试次数: {result.get('attempts', 1)}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    await scraper.client.aclose()


async def test_timeout_retry():
    """测试超时重试（模拟）"""
    print("\n=== 测试超时重试机制 ===")
    
    config = NetworkRetryConfig(
        max_retries=2,
        initial_delay=0.1,  # 快速测试
        jitter=False
    )
    scraper = WebScraper(retry_config=config)
    
    try:
        # 使用一个会超时的URL（设置很短的超时时间）
        scraper.client._timeout = 0.001  # 1毫秒超时，几乎肯定会超时
        
        result = await scraper.fetch_url("https://httpbin.org/delay/1")
        
        print(f"请求结果: {'成功' if result['success'] else '失败'}")
        print(f"尝试次数: {result.get('attempts', 1)}")
        print(f"总耗时: {result.get('total_time', 0):.2f} 秒")
        
        if not result["success"]:
            print(f"错误类型: {result.get('error_type')}")
            print(f"错误信息: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    await scraper.client.aclose()


async def test_http_error_retry():
    """测试HTTP错误重试"""
    print("\n=== 测试HTTP错误重试机制 ===")
    
    # 配置为重试特定HTTP错误
    config = NetworkRetryConfig(
        max_retries=2,
        initial_delay=0.1,
        retry_on_http_error=True,
        retry_http_status_codes=[503, 502, 429]
    )
    scraper = WebScraper(retry_config=config)
    
    try:
        # 测试503错误（应该重试）
        result = await scraper.fetch_url("https://httpbin.org/status/503")
        
        print(f"503错误请求结果: {'成功' if result['success'] else '失败'}")
        print(f"尝试次数: {result.get('attempts', 1)}")
        
        if not result["success"]:
            print(f"错误类型: {result.get('error_type')}")
            print(f"状态码: {result.get('status_code')}")
        
        # 测试404错误（不应该重试）
        result2 = await scraper.fetch_url("https://httpbin.org/status/404")
        
        print(f"\n404错误请求结果: {'成功' if result2['success'] else '失败'}")
        print(f"尝试次数: {result2.get('attempts', 1)}")
        
        if not result2["success"]:
            print(f"错误类型: {result2.get('error_type')}")
            print(f"状态码: {result2.get('status_code')}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    await scraper.client.aclose()


async def main():
    """主测试函数"""
    print("🚀 开始测试网络重试机制\n")
    
    try:
        await test_retry_config()
        await test_delay_calculation()
        await test_successful_request()
        await test_timeout_retry()
        await test_http_error_retry()
        
        print("\n✅ 所有测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
