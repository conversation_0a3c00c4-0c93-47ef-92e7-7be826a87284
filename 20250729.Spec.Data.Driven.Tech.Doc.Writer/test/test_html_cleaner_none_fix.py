"""
Test for HTML cleaner None value handling fix

This test verifies that the HTML cleaner properly handles None values
returned by readabilipy, which was causing the error:
"object of type 'NoneType' has no len()"
"""

import pytest
from unittest.mock import patch, MagicMock
from src.backend.agents.web_content_processor import HTMLCleaner


class TestHTMLCleanerNoneFix:
    """Test HTML cleaner None value handling"""
    
    def setup_method(self):
        self.cleaner = HTMLCleaner()
    
    def test_clean_html_with_none_title(self):
        """Test HTML cleaning when readabilipy returns None for title"""
        
        # Mock readabilipy to return None for title
        mock_article = {
            "title": None,
            "content": "<p>Some content</p>",
            "plain_text": "Some content"
        }
        
        with patch('src.backend.agents.web_content_processor.simple_json_from_html_string') as mock_readability:
            mock_readability.return_value = mock_article
            
            result = self.cleaner.clean_html("<html><body>test</body></html>", "https://example.com")
            
            # Should not raise an error and should handle None title gracefully
            assert result["success"] is True
            assert result["title"] == ""  # Should be converted to empty string
            assert result["content"] == "<p>Some content</p>"
            assert result["text_content"] == "Some content"
    
    def test_clean_html_with_none_content(self):
        """Test HTML cleaning when readabilipy returns None for content"""
        
        # Mock readabilipy to return None for content
        mock_article = {
            "title": "Test Title",
            "content": None,
            "plain_text": "Some text"
        }
        
        with patch('src.backend.agents.web_content_processor.simple_json_from_html_string') as mock_readability:
            mock_readability.return_value = mock_article
            
            result = self.cleaner.clean_html("<html><body>test</body></html>", "https://example.com")
            
            # Should not raise an error and should handle None content gracefully
            assert result["success"] is True
            assert result["title"] == "Test Title"
            assert result["content"] == ""  # Should be converted to empty string
            assert result["text_content"] == "Some text"
    
    def test_clean_html_with_none_plain_text(self):
        """Test HTML cleaning when readabilipy returns None for plain_text"""
        
        # Mock readabilipy to return None for plain_text
        mock_article = {
            "title": "Test Title",
            "content": "<p>Some content</p>",
            "plain_text": None
        }
        
        with patch('src.backend.agents.web_content_processor.simple_json_from_html_string') as mock_readability:
            mock_readability.return_value = mock_article
            
            result = self.cleaner.clean_html("<html><body>test</body></html>", "https://example.com")
            
            # Should not raise an error and should handle None plain_text gracefully
            assert result["success"] is True
            assert result["title"] == "Test Title"
            assert result["content"] == "<p>Some content</p>"
            assert result["text_content"] == ""  # Should be converted to empty string
    
    def test_clean_html_with_all_none_values(self):
        """Test HTML cleaning when readabilipy returns None for all fields"""
        
        # Mock readabilipy to return None for all fields (as mentioned in docs)
        mock_article = {
            "title": None,
            "content": None,
            "plain_text": None
        }
        
        with patch('src.backend.agents.web_content_processor.simple_json_from_html_string') as mock_readability:
            mock_readability.return_value = mock_article
            
            result = self.cleaner.clean_html("<html><body>test</body></html>", "https://example.com")
            
            # Should not raise an error and should handle all None values gracefully
            assert result["success"] is True
            assert result["title"] == ""
            assert result["content"] == ""
            assert result["text_content"] == ""
            assert result["content_length"] == 0
            assert result["word_count"] == 0
    
    def test_clean_html_with_empty_article(self):
        """Test HTML cleaning when readabilipy returns empty article"""
        
        # Mock readabilipy to return empty article
        mock_article = {}
        
        with patch('src.backend.agents.web_content_processor.simple_json_from_html_string') as mock_readability:
            mock_readability.return_value = mock_article
            
            result = self.cleaner.clean_html("<html><body>test</body></html>", "https://example.com")
            
            # Should not raise an error and should handle empty article gracefully
            assert result["success"] is True
            assert result["title"] == ""
            assert result["content"] == ""
            assert result["text_content"] == ""
    
    def test_clean_html_with_none_article(self):
        """Test HTML cleaning when readabilipy returns None article"""
        
        # Mock readabilipy to return None
        with patch('src.backend.agents.web_content_processor.simple_json_from_html_string') as mock_readability:
            mock_readability.return_value = None
            
            result = self.cleaner.clean_html("<html><body>test</body></html>", "https://example.com")
            
            # Should return failure result for None article
            assert result["success"] is False
            assert result["error"] == "No content extracted"


if __name__ == "__main__":
    pytest.main([__file__])
