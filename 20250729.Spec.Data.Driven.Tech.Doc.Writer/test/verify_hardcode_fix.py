#!/usr/bin/env python3
"""
硬编码修复验证脚本（简化版）

验证所有硬编码常量已被配置项替换，确保符合项目规范
"""

import re
from pathlib import Path


def check_config_model():
    """检查配置模型是否正确添加"""
    print("=== 检查配置模型 ===")
    
    config_file = Path("src/backend/config.py")
    if not config_file.exists():
        print("❌ 配置文件不存在")
        return False
    
    content = config_file.read_text(encoding='utf-8')
    
    # 检查 ResearchConfig 类是否存在
    if "class ResearchConfig(BaseModel):" in content:
        print("✅ ResearchConfig 类已添加")
    else:
        print("❌ ResearchConfig 类未找到")
        return False
    
    # 检查关键配置项
    required_fields = [
        "max_search_results",
        "max_source_list_size", 
        "max_concurrent_requests",
        "search_query_limit",
        "primary_keywords_limit",
        "technical_terms_limit",
        "english_keywords_limit",
        "information_summary_limit"
    ]
    
    missing_fields = []
    for field in required_fields:
        if field not in content:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ 缺少配置字段: {missing_fields}")
        return False
    else:
        print("✅ 所有必需配置字段已添加")
    
    # 检查是否集成到 RetrievalConfig
    if "research: ResearchConfig = ResearchConfig()" in content:
        print("✅ ResearchConfig 已集成到 RetrievalConfig")
        return True
    else:
        print("❌ ResearchConfig 未集成到 RetrievalConfig")
        return False


def check_workflow_functions():
    """检查工作流函数是否已修复硬编码"""
    print("\n=== 检查工作流函数 ===")
    
    workflow_file = Path("src/backend/agents/requirement_analyst/workflows.py")
    if not workflow_file.exists():
        print("❌ 工作流文件不存在")
        return False
    
    content = workflow_file.read_text(encoding='utf-8')
    
    # 检查函数签名是否添加了 config 参数
    functions_to_check = [
        "_prepare_search_queries",
        "_generate_source_list", 
        "_generate_information_summary"
    ]
    
    for func_name in functions_to_check:
        # 查找函数定义，更灵活的匹配
        pattern = rf"def {func_name}\([^)]*config[^)]*\):"
        if re.search(pattern, content, re.MULTILINE):
            print(f"✅ {func_name} 已添加 config 参数")
        else:
            # 尝试更宽松的匹配
            pattern2 = rf"def {func_name}\("
            if re.search(pattern2, content):
                # 找到函数，检查是否有config参数
                func_start = content.find(f"def {func_name}(")
                func_end = content.find("):", func_start)
                if func_end > func_start:
                    func_signature = content[func_start:func_end+2]
                    if "config" in func_signature:
                        print(f"✅ {func_name} 已添加 config 参数")
                    else:
                        print(f"❌ {func_name} 未添加 config 参数")
                        return False
                else:
                    print(f"❌ {func_name} 函数签名解析失败")
                    return False
            else:
                print(f"❌ {func_name} 函数未找到")
                return False
    
    return True


def check_hardcoded_removal():
    """检查硬编码常量是否已移除"""
    print("\n=== 检查硬编码常量移除 ===")
    
    workflow_file = Path("src/backend/agents/requirement_analyst/workflows.py")
    if not workflow_file.exists():
        print("❌ 工作流文件不存在")
        return False
    
    content = workflow_file.read_text(encoding='utf-8')
    
    # 检查是否还有硬编码的数字（在注释中的除外）
    hardcoded_issues = []
    
    # 检查 results[:15] 等硬编码切片
    if re.search(r'results\[:\d+\]', content):
        matches = re.findall(r'results\[:\d+\]', content)
        hardcoded_issues.extend([f"硬编码切片: {match}" for match in matches])
    
    # 检查 keywords.primary_keywords[:3] 等硬编码
    if re.search(r'keywords\.\w+\[:\d+\]', content):
        matches = re.findall(r'keywords\.\w+\[:\d+\]', content)
        hardcoded_issues.extend([f"硬编码关键词切片: {match}" for match in matches])
    
    # 检查 max_concurrent = 5 等硬编码赋值（排除默认值情况）
    max_concurrent_matches = re.findall(r'max_concurrent\s*=\s*\d+', content)
    for match in max_concurrent_matches:
        # 检查是否在 else 分支中作为默认值
        match_pos = content.find(match)
        before_context = content[max(0, match_pos-200):match_pos]
        if "# 使用默认值" not in before_context and "else:" not in before_context:
            hardcoded_issues.append(f"硬编码并发数: {match}")
    
    if hardcoded_issues:
        print("❌ 仍然存在硬编码问题:")
        for issue in hardcoded_issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ 所有硬编码常量已移除")
        return True


def check_config_usage():
    """检查配置是否正确使用"""
    print("\n=== 检查配置使用 ===")
    
    workflow_file = Path("src/backend/agents/requirement_analyst/workflows.py")
    if not workflow_file.exists():
        print("❌ 工作流文件不存在")
        return False
    
    content = workflow_file.read_text(encoding='utf-8')
    
    # 检查是否有配置获取逻辑
    config_patterns = [
        r'config\.retrieval\.research\.',
        r'research_config\.',
        r'hasattr\(config.*research\)',
        r'getattr\(.*research.*\)'
    ]
    
    found_patterns = []
    for pattern in config_patterns:
        if re.search(pattern, content):
            found_patterns.append(pattern)
    
    if found_patterns:
        print("✅ 发现配置使用模式:")
        for pattern in found_patterns:
            print(f"  - {pattern}")
        return True
    else:
        print("❌ 未发现配置使用模式")
        return False


def check_config_template():
    """检查配置模板是否已更新"""
    print("\n=== 检查配置模板 ===")
    
    template_file = Path("config.yaml.example")
    if not template_file.exists():
        print("❌ 配置模板文件不存在")
        return False
    
    content = template_file.read_text(encoding='utf-8')
    
    # 检查是否添加了 research 配置节
    if "research:" in content:
        print("✅ 配置模板已添加 research 配置节")
    else:
        print("❌ 配置模板未添加 research 配置节")
        return False
    
    # 检查关键配置项
    research_fields = [
        "max_source_list_size",
        "max_concurrent_requests", 
        "search_query_limit"
    ]
    
    missing_fields = []
    for field in research_fields:
        if field not in content:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ 配置模板缺少字段: {missing_fields}")
        return False
    else:
        print("✅ 配置模板包含所有必需字段")
        return True


def check_backward_compatibility():
    """检查向后兼容性"""
    print("\n=== 检查向后兼容性 ===")
    
    workflow_file = Path("src/backend/agents/requirement_analyst/workflows.py")
    if not workflow_file.exists():
        print("❌ 工作流文件不存在")
        return False
    
    content = workflow_file.read_text(encoding='utf-8')
    
    # 检查是否有默认值处理
    compatibility_patterns = [
        r'# 使用默认值.*向后兼容',
        r'else:\s*#.*向后兼容',
        r'默认值.*\d+'
    ]
    
    found_compatibility = False
    for pattern in compatibility_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            found_compatibility = True
            break
    
    if found_compatibility:
        print("✅ 发现向后兼容性处理")
        return True
    else:
        print("❌ 未发现向后兼容性处理")
        return False


def generate_summary():
    """生成修复总结"""
    print("\n" + "="*50)
    print("📋 硬编码修复总结")
    print("="*50)
    
    print("\n🎯 修复的硬编码问题:")
    print("1. ❌ results[:15] → ✅ results[:max_sources] (可配置)")
    print("2. ❌ results[:10] → ✅ results[:summary_limit] (可配置)")
    print("3. ❌ keywords.primary_keywords[:3] → ✅ keywords.primary_keywords[:primary_limit] (可配置)")
    print("4. ❌ max_concurrent = 5 → ✅ max_concurrent = config.max_concurrent_requests (可配置)")
    print("5. ❌ unique_queries[:5] → ✅ unique_queries[:query_limit] (可配置)")
    
    print("\n🔧 添加的配置项:")
    print("- max_search_results: 搜索结果最大数量 (10-200, 默认60)")
    print("- max_source_list_size: 信息源清单最大数量 (5-50, 默认15)")
    print("- max_concurrent_requests: 最大并发请求数 (1-20, 默认5)")
    print("- search_query_limit: 搜索查询数量限制 (1-10, 默认5)")
    print("- primary_keywords_limit: 主要关键词数量限制 (1-5, 默认3)")
    print("- technical_terms_limit: 技术术语数量限制 (1-5, 默认2)")
    print("- english_keywords_limit: 英文关键词数量限制 (1-5, 默认2)")
    print("- information_summary_limit: 信息摘要处理数量限制 (5-20, 默认10)")
    
    print("\n✅ 符合的项目规范:")
    print("- 禁用硬编码: 所有常量都可通过配置修改")
    print("- 智能配置管理: 提供合理默认值和边界检查")
    print("- 配置驱动: 支持通过配置调整系统行为")
    print("- 向后兼容: 保持现有API兼容性")
    
    print("\n🚀 用户现在可以:")
    print("- 根据调研深度需求调整源数量")
    print("- 根据性能要求调整并发数")
    print("- 根据搜索策略调整查询数量")
    print("- 通过配置文件而非修改代码来调整行为")


def main():
    """主验证函数"""
    print("🚀 开始硬编码修复验证\n")
    
    checks = [
        ("配置模型", check_config_model),
        ("工作流函数", check_workflow_functions),
        ("硬编码移除", check_hardcoded_removal),
        ("配置使用", check_config_usage),
        ("配置模板", check_config_template),
        ("向后兼容性", check_backward_compatibility)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} 检查失败: {e}")
            results.append((check_name, False))
    
    # 统计结果
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 验证结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有检查通过！硬编码修复成功完成！")
        generate_summary()
    else:
        print("⚠️  部分检查未通过，需要进一步修复:")
        for check_name, result in results:
            status = "✅" if result else "❌"
            print(f"  {status} {check_name}")


if __name__ == "__main__":
    main()
