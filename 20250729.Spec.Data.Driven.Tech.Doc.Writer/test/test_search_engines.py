"""
搜索引擎服务测试

测试 Tavily 和 DuckDuckGo 搜索引擎集成、错误处理和降级机制。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List

from src.backend.agents.search_engines import (
    SearchEngineService,
    TavilySearchEngine,
    DuckDuckGoSearchEngine,
    SearchEngineSelector,
    URLCache,
    ResultStandardizer,
    SearchResult,
    SearchEngineType
)
from src.backend.config import Config


class TestSearchResult:
    """测试 SearchResult 类"""
    
    def test_search_result_creation(self):
        """测试搜索结果创建"""
        result = SearchResult(
            title="Test Title",
            content="Test content",
            url="https://example.com",
            source="test",
            relevance_score=0.8
        )
        
        assert result.title == "Test Title"
        assert result.content == "Test content"
        assert result.url == "https://example.com"
        assert result.source == "test"
        assert result.relevance_score == 0.8
        assert result.timestamp is not None
    
    def test_search_result_to_dict(self):
        """测试搜索结果转换为字典"""
        result = SearchResult(
            title="Test Title",
            content="Test content",
            url="https://example.com",
            source="test"
        )
        
        result_dict = result.to_dict()
        assert isinstance(result_dict, dict)
        assert result_dict["title"] == "Test Title"
        assert result_dict["url"] == "https://example.com"
    
    def test_url_hash(self):
        """测试URL哈希生成"""
        result = SearchResult(
            title="Test",
            content="Test",
            url="https://example.com",
            source="test"
        )
        
        hash_value = result.get_url_hash()
        assert isinstance(hash_value, str)
        assert len(hash_value) == 32  # MD5 hash length


class TestURLCache:
    """测试 URL 缓存管理器"""
    
    def test_url_cache_creation(self):
        """测试缓存创建"""
        cache = URLCache()
        assert len(cache.url_cache) == 0
    
    def test_duplicate_detection(self):
        """测试重复URL检测"""
        cache = URLCache()
        url = "https://example.com"
        
        # 第一次应该不是重复
        assert not cache.is_duplicate(url)
        
        # 添加URL后应该检测为重复
        cache.add_url(url)
        assert cache.is_duplicate(url)
    
    def test_result_deduplication(self):
        """测试结果去重"""
        cache = URLCache()
        
        results = [
            SearchResult("Title 1", "Content 1", "https://example.com/1", "test"),
            SearchResult("Title 2", "Content 2", "https://example.com/2", "test"),
            SearchResult("Title 1 Duplicate", "Content 1", "https://example.com/1", "test"),  # 重复URL
        ]
        
        deduplicated = cache.deduplicate_results(results)
        assert len(deduplicated) == 2
        assert deduplicated[0].url == "https://example.com/1"
        assert deduplicated[1].url == "https://example.com/2"


class TestResultStandardizer:
    """测试结果标准化器"""
    
    def test_standardizer_creation(self):
        """测试标准化器创建"""
        standardizer = ResultStandardizer()
        assert standardizer is not None
    
    def test_quality_score_calculation(self):
        """测试质量评分计算"""
        standardizer = ResultStandardizer()
        
        # 高质量结果
        high_quality_result = SearchResult(
            title="Comprehensive Guide to Machine Learning",
            content="This is a detailed article about machine learning with over 200 characters of content that provides valuable insights and information.",
            url="https://example.com/article",
            source="test",
            authors=["John Doe"],
            publication_date="2024-01-01"
        )
        
        score = standardizer._calculate_quality_score(high_quality_result)
        assert score > 0.8  # 应该是高质量评分
        
        # 低质量结果
        low_quality_result = SearchResult(
            title="",
            content="Short",
            url="invalid-url",
            source="test"
        )
        
        score = standardizer._calculate_quality_score(low_quality_result)
        assert score < 0.3  # 应该是低质量评分
    
    def test_apa_citation_generation(self):
        """测试APA引用格式生成"""
        standardizer = ResultStandardizer()
        
        result = SearchResult(
            title="Machine Learning Fundamentals",
            content="Content",
            url="https://example.com/article",
            source="test",
            authors=["John Doe", "Jane Smith"],
            publication_date="2024"
        )
        
        citation = standardizer._generate_apa_citation(result)
        assert "John Doe" in citation
        assert "Jane Smith" in citation
        assert "2024" in citation
        assert "Machine Learning Fundamentals" in citation
        assert "https://example.com/article" in citation
    
    def test_website_name_extraction(self):
        """测试网站名称提取"""
        standardizer = ResultStandardizer()
        
        # 测试常见域名
        assert standardizer._extract_website_name("https://www.example.com/page") == "Example"
        assert standardizer._extract_website_name("https://github.com/user/repo") == "Github"
        assert standardizer._extract_website_name("https://arxiv.org/abs/1234") == "Arxiv"
        
        # 测试无效URL
        assert standardizer._extract_website_name("") == ""
        assert standardizer._extract_website_name("invalid-url") == ""


@pytest.fixture
def mock_config():
    """模拟配置对象"""
    config = Mock()
    config.get.return_value = None

    # 模拟代理配置
    proxy_config = Mock()
    proxy_config.http_proxy = "http://127.0.0.1:8118/"
    proxy_config.https_proxy = "http://127.0.0.1:8118/"
    config.proxy = proxy_config

    return config


class TestTavilySearchEngine:
    """测试 Tavily 搜索引擎"""
    
    def test_tavily_engine_creation(self, mock_config):
        """测试 Tavily 引擎创建"""
        mock_config.get.return_value = "test-api-key"
        engine = TavilySearchEngine(mock_config)
        assert engine.api_key == "test-api-key"
        assert engine.get_engine_type() == SearchEngineType.TAVILY
    
    @pytest.mark.asyncio
    async def test_tavily_search_validation(self, mock_config):
        """测试 Tavily 搜索参数验证"""
        mock_config.get.return_value = "test-api-key"
        engine = TavilySearchEngine(mock_config)
        
        # 模拟搜索工具
        engine.search_tool = AsyncMock()
        
        # 测试空查询
        with pytest.raises(ValueError, match="Search query cannot be empty"):
            await engine.search("")
        
        # 测试空白查询
        with pytest.raises(ValueError, match="Search query cannot be empty"):
            await engine.search("   ")


class TestDuckDuckGoSearchEngine:
    """测试 DuckDuckGo 搜索引擎"""
    
    def test_duckduckgo_engine_creation(self, mock_config):
        """测试 DuckDuckGo 引擎创建"""
        engine = DuckDuckGoSearchEngine(mock_config)
        assert engine.get_engine_type() == SearchEngineType.DUCKDUCKGO
    
    @pytest.mark.asyncio
    async def test_duckduckgo_search_validation(self, mock_config):
        """测试 DuckDuckGo 搜索参数验证"""
        engine = DuckDuckGoSearchEngine(mock_config)
        
        # 模拟搜索工具
        engine.search_tool = Mock()
        
        # 测试空查询
        with pytest.raises(ValueError, match="Search query cannot be empty"):
            await engine.search("")


class TestSearchEngineSelector:
    """测试搜索引擎选择器"""
    
    def test_selector_creation(self, mock_config):
        """测试选择器创建"""
        mock_config.get.side_effect = lambda key, default=None: {
            "retrieval.search_engines.primary": "tavily",
            "retrieval.search_engines.fallback": "duckduckgo"
        }.get(key, default)
        
        selector = SearchEngineSelector(mock_config)
        assert selector.primary_engine == "tavily"
        assert selector.fallback_engine == "duckduckgo"
    
    @pytest.mark.asyncio
    async def test_fallback_mechanism(self, mock_config):
        """测试降级机制"""
        mock_config.get.side_effect = lambda key, default=None: {
            "retrieval.search_engines.primary": "tavily",
            "retrieval.search_engines.fallback": "duckduckgo",
            "retrieval.search_engines.tavily_api_key": "test-key"
        }.get(key, default)
        
        selector = SearchEngineSelector(mock_config)
        
        # 模拟主要引擎失败
        selector.tavily_engine.search = AsyncMock(side_effect=Exception("Tavily failed"))
        selector.duckduckgo_engine.search = AsyncMock(return_value=[])
        
        # 应该降级到 DuckDuckGo
        result = await selector.search("test query")
        
        # 验证调用
        selector.tavily_engine.search.assert_called_once()
        selector.duckduckgo_engine.search.assert_called_once()


class TestSearchEngineService:
    """测试搜索引擎服务"""
    
    def test_service_creation(self, mock_config):
        """测试服务创建"""
        service = SearchEngineService(mock_config)
        assert service.selector is not None
        assert service.cache is not None
        assert service.standardizer is not None
    
    @pytest.mark.asyncio
    async def test_search_integration(self, mock_config):
        """测试搜索集成"""
        service = SearchEngineService(mock_config)
        
        # 模拟搜索结果
        mock_results = [
            SearchResult("Title 1", "Content 1", "https://example.com/1", "test"),
            SearchResult("Title 2", "Content 2", "https://example.com/2", "test")
        ]
        
        service.selector.search = AsyncMock(return_value=mock_results)
        
        result = await service.search("test query", max_results=5)
        
        assert "query" in result
        assert "results" in result
        assert "metadata" in result
        assert result["query"] == "test query"
        assert len(result["results"]) == 2


if __name__ == "__main__":
    pytest.main([__file__])
