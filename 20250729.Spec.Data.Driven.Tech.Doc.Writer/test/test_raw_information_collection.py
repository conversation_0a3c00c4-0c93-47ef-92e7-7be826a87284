"""
测试收集原始信息功能

验证网页内容处理器、URL缓存管理器和工作流集成的正确性和性能。
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
import tempfile
import json
from datetime import datetime

from src.backend.agents.web_content_processor import (
    WebContentProcessor, 
    WebScraper, 
    HTMLCleaner, 
    MarkdownConverter, 
    ContentQualityAssessor
)
from src.backend.agents.url_cache_manager import URLCacheManager
from src.backend.agents.requirement_analyst.workflows import collect_raw_information
from src.backend.config import Config


class TestContentQualityAssessor:
    """测试内容质量评估器"""
    
    def setup_method(self):
        self.assessor = ContentQualityAssessor()
    
    def test_assess_quality_high_quality_content(self):
        """测试高质量内容评估"""
        content_data = {
            "text_content": "This is a comprehensive research article about cybersecurity infrastructure. " * 20,
            "title": "Cybersecurity Infrastructure Research",
            "url": "https://example.com/research"
        }
        
        result = self.assessor.assess_quality(content_data)
        
        assert result["quality_score"] > 0.5
        assert result["passes_quality"] is True
        assert result["word_count"] > 100
        assert "quality_details" in result
    
    def test_assess_quality_low_quality_content(self):
        """测试低质量内容评估"""
        content_data = {
            "text_content": "Short text",
            "title": "Title",
            "url": "https://example.com"
        }
        
        result = self.assessor.assess_quality(content_data)
        
        assert result["quality_score"] < 0.5
        assert result["passes_quality"] is False
        assert result["word_count"] < 50
    
    def test_assess_quality_empty_content(self):
        """测试空内容评估"""
        content_data = {
            "text_content": "",
            "title": "",
            "url": ""
        }
        
        result = self.assessor.assess_quality(content_data)
        
        assert result["quality_score"] == 0.0
        assert result["passes_quality"] is False
        assert result["word_count"] == 0


class TestHTMLCleaner:
    """测试HTML清洁器"""
    
    def setup_method(self):
        self.cleaner = HTMLCleaner()
    
    def test_clean_html_valid_content(self):
        """测试有效HTML内容清洁"""
        html_content = """
        <html>
            <head><title>Test Article</title></head>
            <body>
                <article>
                    <h1>Main Title</h1>
                    <p>This is the main content of the article.</p>
                    <p>Another paragraph with more information.</p>
                </article>
                <aside>Advertisement content</aside>
            </body>
        </html>
        """
        
        result = self.cleaner.clean_html(html_content, "https://example.com")
        
        assert result["success"] is True
        assert "Test Article" in result["title"]
        assert len(result["text_content"]) > 0
        assert result["word_count"] > 0
    
    def test_clean_html_empty_content(self):
        """测试空HTML内容"""
        result = self.cleaner.clean_html("", "https://example.com")
        
        assert result["success"] is False
        assert "Empty HTML content" in result["error"]
    
    def test_clean_html_invalid_content(self):
        """测试无效HTML内容"""
        html_content = "<html><body></body></html>"
        
        result = self.cleaner.clean_html(html_content, "https://example.com")
        
        # readabilipy 可能无法从空内容中提取有用信息
        assert "success" in result


class TestMarkdownConverter:
    """测试Markdown转换器"""
    
    def setup_method(self):
        self.converter = MarkdownConverter()
    
    def test_convert_to_markdown_valid_html(self):
        """测试有效HTML转Markdown"""
        html_content = """
        <h1>Main Title</h1>
        <p>This is a <strong>bold</strong> paragraph with <em>italic</em> text.</p>
        <ul>
            <li>First item</li>
            <li>Second item</li>
        </ul>
        """
        
        result = self.converter.convert_to_markdown(html_content, "https://example.com")
        
        assert result["success"] is True
        assert "# Main Title" in result["markdown_content"]
        assert "**bold**" in result["markdown_content"]
        assert "*italic*" in result["markdown_content"]
        assert result["line_count"] > 0
        assert result["word_count"] > 0
    
    def test_convert_to_markdown_empty_content(self):
        """测试空HTML内容转换"""
        result = self.converter.convert_to_markdown("", "https://example.com")
        
        assert result["success"] is False
        assert "Empty HTML content" in result["error"]


class TestURLCacheManager:
    """测试URL缓存管理器"""
    
    def setup_method(self):
        # 使用临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.cache_manager = URLCacheManager(Path(self.temp_dir))
    
    @pytest.mark.asyncio
    async def test_url_caching_workflow(self):
        """测试URL缓存工作流"""
        await self.cache_manager.initialize()
        
        test_url = "https://example.com/test"
        
        # 初始状态：URL未处理
        is_processed = await self.cache_manager.is_url_processed(test_url)
        assert is_processed is False
        
        # 标记URL为已处理 - 使用新的API
        await self.cache_manager.mark_url_processed(
            url=test_url,
            success=True,
            metadata={"test": "data"}
        )
        
        # 检查URL已处理
        is_processed = await self.cache_manager.is_url_processed(test_url)
        assert is_processed is True
        
        # 获取元数据
        metadata = await self.cache_manager.get_url_metadata(test_url)
        assert metadata is not None
        assert metadata["success"] is True
        assert metadata["metadata"]["test"] == "data"
    
    @pytest.mark.asyncio
    async def test_cache_persistence(self):
        """测试缓存持久化"""
        await self.cache_manager.initialize()
        
        test_url = "https://example.com/persist"
        await self.cache_manager.mark_url_processed(url=test_url, success=True)
        
        # 创建新的缓存管理器实例
        new_cache_manager = URLCacheManager(Path(self.temp_dir))
        await new_cache_manager.initialize()
        
        # 检查URL仍然被标记为已处理
        is_processed = await new_cache_manager.is_url_processed(test_url)
        assert is_processed is True
    
    @pytest.mark.asyncio
    async def test_cache_stats(self):
        """测试缓存统计"""
        await self.cache_manager.initialize()
        
        # 执行一些操作
        await self.cache_manager.is_url_processed("https://example.com/1")
        await self.cache_manager.is_url_processed("https://example.com/2")
        await self.cache_manager.mark_url_processed(url="https://example.com/1", success=True)
        await self.cache_manager.is_url_processed("https://example.com/1")
        
        stats = self.cache_manager.get_cache_stats()
        
        assert stats["total_requests"] == 3
        assert stats["cache_hits"] == 1
        assert stats["cache_misses"] == 2
        assert stats["urls_added"] == 1
        assert stats["cache_hit_rate"] > 0


@pytest.mark.asyncio
class TestWebScraper:
    """测试网页抓取器"""
    
    def setup_method(self):
        # 创建模拟配置
        self.config = Mock(spec=Config)
        self.config.proxy = Mock()
        self.config.proxy.http_proxy = None
        self.config.proxy.https_proxy = None
        
        self.scraper = WebScraper(self.config)
    
    @patch('httpx.AsyncClient')
    async def test_fetch_url_success(self, mock_client_class):
        """测试成功抓取URL"""
        # 设置模拟响应
        mock_response = Mock()
        mock_response.text = "<html><body>Test content</body></html>"
        mock_response.status_code = 200
        mock_response.headers = {"content-type": "text/html"}
        mock_response.content = b"test content"
        mock_response.raise_for_status = Mock()
        
        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response
        mock_client_class.return_value = mock_client
        
        # 执行测试
        result = await self.scraper.fetch_url("https://example.com")
        
        assert result["success"] is True
        assert result["url"] == "https://example.com"
        assert result["status_code"] == 200
        assert "Test content" in result["content"]
    
    @patch('httpx.AsyncClient')
    async def test_fetch_url_timeout(self, mock_client_class):
        """测试抓取超时"""
        import httpx
        
        mock_client = AsyncMock()
        mock_client.get.side_effect = httpx.TimeoutException("Request timeout")
        mock_client_class.return_value = mock_client
        
        result = await self.scraper.fetch_url("https://example.com")
        
        assert result["success"] is False
        assert result["error_type"] == "timeout"
        assert "Timeout" in result["error"]


@pytest.mark.asyncio
class TestCollectRawInformation:
    """测试收集原始信息工作流"""
    
    @patch.dict('os.environ', {}, clear=True)  # 清空环境变量
    @patch('src.backend.config.get_config')
    @patch('src.backend.agents.web_content_processor.WebContentProcessor')
    @patch('src.backend.agents.url_cache_manager.URLCacheManager')
    async def test_collect_raw_information_success(self, mock_cache_class, mock_processor_class, mock_get_config):
        """测试成功收集原始信息"""
        # 设置模拟配置
        mock_config = Mock()
        mock_config.proxy = Mock()
        mock_config.proxy.http_proxy = None
        mock_config.proxy.https_proxy = None
        mock_get_config.return_value = mock_config
        
        # 设置模拟缓存管理器
        mock_cache = AsyncMock()
        mock_cache.initialize = AsyncMock()
        mock_cache.is_url_processed.return_value = False
        mock_cache.mark_url_processed = AsyncMock()
        mock_cache.get_cache_stats.return_value = {"cache_hit_rate": 0.0}
        mock_cache_class.return_value = mock_cache
        
        # 设置模拟内容处理器
        mock_processor = AsyncMock()
        mock_processor.initialize = AsyncMock()
        mock_processor.process_urls.return_value = [
            {
                "url": "https://example.com/1",
                "success": True,
                "passes_quality": True,
                "title": "Test Article",
                "quality_score": 0.8,
                "word_count": 500,
                "content_summary": "Test summary",
                "apa_citation": "Test citation"
            }
        ]
        mock_processor.save_processed_content.return_value = Path("/tmp/test.md")
        mock_processor.get_stats.return_value = {"success_rate": 1.0}
        mock_processor.cleanup = AsyncMock()
        mock_processor_class.return_value = mock_processor
        
        # 准备测试数据
        source_list = [
            {
                "url": "https://example.com/1",
                "title": "Test Source",
                "source_type": "学术资源"
            }
        ]
        
        # 执行测试
        result = await collect_raw_information(source_list)
        
        # 验证结果
        assert len(result["raw_information_files"]) == 1
        assert result["metadata"]["successful_extractions"] == 1
        assert result["metadata"]["failed_extractions"] == 0
        
        # 验证调用
        mock_processor.initialize.assert_called_once()
        mock_cache.initialize.assert_called_once()
        mock_processor.process_urls.assert_called_once()
        mock_processor.cleanup.assert_called_once()
    
    @patch.dict('os.environ', {}, clear=True)  # 清空环境变量
    @patch('src.backend.config.get_config')
    @patch('src.backend.agents.web_content_processor.WebContentProcessor')
    @patch('src.backend.agents.url_cache_manager.URLCacheManager')
    async def test_collect_raw_information_all_cached(self, mock_cache_class, mock_processor_class, mock_get_config):
        """测试所有URL都已缓存的情况"""
        # 设置模拟配置
        mock_config = Mock()
        mock_config.proxy = Mock()
        mock_config.proxy.http_proxy = None
        mock_config.proxy.https_proxy = None
        mock_get_config.return_value = mock_config
        
        # 设置模拟缓存管理器（所有URL都已处理）
        mock_cache = AsyncMock()
        mock_cache.initialize = AsyncMock()
        mock_cache.is_url_processed.return_value = True
        mock_cache_class.return_value = mock_cache
        
        # 设置模拟内容处理器
        mock_processor = AsyncMock()
        mock_processor.initialize = AsyncMock()
        mock_processor.cleanup = AsyncMock()
        mock_processor_class.return_value = mock_processor
        
        # 准备测试数据
        source_list = [
            {
                "url": "https://example.com/1",
                "title": "Cached Source"
            }
        ]
        
        # 执行测试
        result = await collect_raw_information(source_list)
        
        # 验证结果
        assert len(result["raw_information_files"]) == 0
        assert result["metadata"]["cached_sources"] == 1
        assert result["metadata"]["processed_sources"] == 0
        
        # 验证没有调用处理器的处理方法
        mock_processor.process_urls.assert_not_called()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
