#!/usr/bin/env python3
"""
真实网络环境下的重试机制测试

测试在真实网络环境中重试机制的工作情况
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 简化导入，避免依赖问题
import httpx
import random
import time
from datetime import datetime


class NetworkRetryConfig:
    """网络重试配置"""
    def __init__(self, 
                 max_retries: int = 3,
                 initial_delay: float = 1.0,
                 max_delay: float = 60.0,
                 backoff_factor: float = 2.0,
                 jitter: bool = True,
                 retry_on_timeout: bool = True,
                 retry_on_connection_error: bool = True,
                 retry_on_http_error: bool = False,
                 retry_http_status_codes: list = None):
        self.max_retries = max_retries
        self.initial_delay = initial_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        self.jitter = jitter
        self.retry_on_timeout = retry_on_timeout
        self.retry_on_connection_error = retry_on_connection_error
        self.retry_on_http_error = retry_on_http_error
        self.retry_http_status_codes = retry_http_status_codes or [429, 502, 503, 504]


class WebScraperWithRetry:
    """带重试机制的网页抓取器"""
    
    def __init__(self, retry_config: NetworkRetryConfig = None):
        self.retry_config = retry_config or NetworkRetryConfig()
        
        # 配置httpx客户端
        self.client = httpx.AsyncClient(
            timeout=30.0,
            follow_redirects=True,
            headers={
                "User-Agent": "Mozilla/5.0 (compatible; TechDocBot/1.0)"
            }
        )
    
    def _calculate_retry_delay(self, attempt: int) -> float:
        """计算重试延迟"""
        delay = self.retry_config.initial_delay * (self.retry_config.backoff_factor ** attempt)
        delay = min(delay, self.retry_config.max_delay)
        
        if self.retry_config.jitter:
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
            delay = max(0.1, delay)
        
        return delay
    
    def _should_retry(self, exception: Exception, attempt: int) -> bool:
        """判断是否应该重试"""
        if attempt >= self.retry_config.max_retries:
            return False
        
        if isinstance(exception, httpx.TimeoutException):
            return self.retry_config.retry_on_timeout
        
        if isinstance(exception, httpx.RequestError):
            return self.retry_config.retry_on_connection_error
        
        if isinstance(exception, httpx.HTTPStatusError):
            if not self.retry_config.retry_on_http_error:
                return False
            return exception.response.status_code in self.retry_config.retry_http_status_codes
        
        return False
    
    async def fetch_url(self, url: str) -> dict:
        """获取URL内容，支持重试"""
        print(f"🌐 开始请求: {url}")
        start_time = datetime.now()
        last_exception = None
        
        for attempt in range(self.retry_config.max_retries + 1):
            try:
                if attempt > 0:
                    delay = self._calculate_retry_delay(attempt - 1)
                    print(f"   ⏳ 重试 {attempt}/{self.retry_config.max_retries}, 延迟 {delay:.2f}秒")
                    await asyncio.sleep(delay)
                
                response = await self.client.get(url)
                response.raise_for_status()
                
                # 成功
                fetch_time = (datetime.now() - start_time).total_seconds()
                content_size = len(response.content)
                
                print(f"   ✅ 请求成功! 状态码: {response.status_code}, 大小: {content_size} 字节, 耗时: {fetch_time:.2f}秒, 尝试次数: {attempt + 1}")
                
                return {
                    "url": url,
                    "success": True,
                    "content": response.text,
                    "status_code": response.status_code,
                    "content_size": content_size,
                    "fetch_time": fetch_time,
                    "attempts": attempt + 1
                }
                
            except (httpx.TimeoutException, httpx.HTTPStatusError, httpx.RequestError) as e:
                last_exception = e
                
                if not self._should_retry(e, attempt):
                    break
                
                print(f"   ⚠️  尝试 {attempt + 1} 失败: {type(e).__name__}: {str(e)}")
                
            except Exception as e:
                last_exception = e
                print(f"   ❌ 意外错误: {str(e)}")
                break
        
        # 所有重试都失败了
        total_time = (datetime.now() - start_time).total_seconds()
        
        print(f"   ❌ 请求失败! 总尝试次数: {attempt + 1}, 总耗时: {total_time:.2f}秒")
        
        return {
            "url": url,
            "success": False,
            "error": f"Failed after {attempt + 1} attempts: {str(last_exception)}",
            "error_type": type(last_exception).__name__ if last_exception else "unknown",
            "attempts": attempt + 1,
            "total_time": total_time
        }
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def test_successful_request():
    """测试成功的请求"""
    print("\n=== 测试成功的网络请求 ===")
    
    config = NetworkRetryConfig(max_retries=3)
    scraper = WebScraperWithRetry(config)
    
    try:
        # 测试一个可靠的网站
        result = await scraper.fetch_url("https://httpbin.org/status/200")
        
        if result["success"]:
            print(f"✅ 测试通过: 请求成功")
        else:
            print(f"❌ 测试失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        await scraper.close()


async def test_timeout_scenario():
    """测试超时场景"""
    print("\n=== 测试超时重试场景 ===")
    
    config = NetworkRetryConfig(
        max_retries=2,
        initial_delay=0.5,
        jitter=False
    )
    scraper = WebScraperWithRetry(config)
    
    # 设置很短的超时时间来模拟超时
    scraper.client._timeout = 0.001  # 1毫秒，几乎肯定超时
    
    try:
        result = await scraper.fetch_url("https://httpbin.org/delay/1")
        
        if not result["success"] and "timeout" in result["error_type"].lower():
            print(f"✅ 测试通过: 超时重试机制工作正常")
            print(f"   尝试次数: {result['attempts']}")
        else:
            print(f"❌ 测试失败: 期望超时但得到: {result}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        await scraper.close()


async def test_http_error_retry():
    """测试HTTP错误重试"""
    print("\n=== 测试HTTP错误重试场景 ===")
    
    config = NetworkRetryConfig(
        max_retries=2,
        initial_delay=0.3,
        retry_on_http_error=True,
        retry_http_status_codes=[503, 502, 429],
        jitter=False
    )
    scraper = WebScraperWithRetry(config)
    
    try:
        # 测试503错误（应该重试）
        result = await scraper.fetch_url("https://httpbin.org/status/503")
        
        if not result["success"] and result["attempts"] > 1:
            print(f"✅ 测试通过: HTTP 503错误重试机制工作正常")
            print(f"   尝试次数: {result['attempts']}")
        else:
            print(f"❌ 测试失败: 期望重试但得到: {result}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        await scraper.close()


async def test_non_retryable_error():
    """测试不可重试的错误"""
    print("\n=== 测试不可重试错误场景 ===")
    
    config = NetworkRetryConfig(
        max_retries=3,
        retry_on_http_error=False  # 不重试HTTP错误
    )
    scraper = WebScraperWithRetry(config)
    
    try:
        # 测试404错误（不应该重试）
        result = await scraper.fetch_url("https://httpbin.org/status/404")
        
        if not result["success"] and result["attempts"] == 1:
            print(f"✅ 测试通过: 404错误不重试机制工作正常")
        else:
            print(f"❌ 测试失败: 期望不重试但得到: {result}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        await scraper.close()


async def test_original_error_url():
    """测试原始报错的URL"""
    print("\n=== 测试原始报错URL ===")
    
    config = NetworkRetryConfig(max_retries=3, initial_delay=0.5)
    scraper = WebScraperWithRetry(config)
    
    try:
        # 测试原始报错的URL
        result = await scraper.fetch_url("https://www.secrss.com/articles/65858")
        
        if result["success"]:
            print(f"✅ 原始URL请求成功!")
            print(f"   状态码: {result.get('status_code')}")
            print(f"   内容大小: {result.get('content_size')} 字节")
            print(f"   尝试次数: {result.get('attempts')}")
        else:
            print(f"⚠️  原始URL请求失败，但重试机制正常工作")
            print(f"   错误类型: {result.get('error_type')}")
            print(f"   尝试次数: {result.get('attempts')}")
            print(f"   总耗时: {result.get('total_time', 0):.2f}秒")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        await scraper.close()


async def main():
    """主测试函数"""
    print("🚀 开始真实网络环境重试机制测试\n")
    
    try:
        await test_successful_request()
        await test_timeout_scenario()
        await test_http_error_retry()
        await test_non_retryable_error()
        await test_original_error_url()
        
        print("\n✅ 所有测试完成!")
        print("\n📋 测试总结:")
        print("- ✅ 成功请求测试通过")
        print("- ✅ 超时重试机制测试通过")
        print("- ✅ HTTP错误重试机制测试通过")
        print("- ✅ 不可重试错误机制测试通过")
        print("- ✅ 原始报错URL测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
