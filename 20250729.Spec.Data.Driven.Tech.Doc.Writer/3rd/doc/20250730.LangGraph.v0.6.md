# LangGraph v0.6 重磅发布！全新 Context API 让智能体开发更简单

> 本文由 [简悦 SimpRead](http://ksria.com/simpread/) 转码， 原文地址 [mp.weixin.qq.com](https://mp.weixin.qq.com/s/nqsP65lj-QkeZQmizZH0NA)

> ❝
> 
> **导语**：还在为复杂的上下文配置而头疼？ LangGraph v0.6 带来了革命性的 Context API，让智能体开发变得前所未有的简单！本次更新不仅大幅简化了开发体验，还引入了动态模型选择、增强的类型安全等多项重磅功能。这可能是迈向 v1.0 最关键的一次更新！

  

![](https://mmbiz.qpic.cn/mmbiz_png/Ea6oETrjsvicde7Pj8W4mBPKSByIAEM5bzyykYlvZUw5P3SbTt41EibnswabIujYyVVT1vTXdMDD2mQoEfNibhn8Q/640?wx_fmt=png&from=appmsg&randomid=kxaia3mz&watermark=1)

🚀 Context API：告别配置地狱，拥抱类型安全
----------------------------

还记得那些令人抓狂的嵌套配置吗？ LangGraph v0.6 的全新 **Context API** 彻底解决了这个痛点！

![](https://mmbiz.qpic.cn/mmbiz_png/Ea6oETrjsvicde7Pj8W4mBPKSByIAEM5bFHf1PS4fqXW2pDXMqI50t7GiaHz06bldDLBzGviaibzZFKRRMJDpztTicQ/640?wx_fmt=png&from=appmsg&randomid=p7mdcyl6&watermark=1)

#### ❌ 告别繁琐的旧方式

```
# v0.5 的痛苦回忆
def node(state: State, config: RunnableConfig):
    # 需要层层嵌套获取数据，容易出错
    user_id = config.get("configurable", {}).get("user_id")
    db_conn = config.get("configurable", {}).get("db_connection")


```

#### ✅ 拥抱简洁的新方式

```
# v0.6 的优雅体验
@dataclass
class Context:
    user_id: str  
    db_connection: str
  
def node(state: State, runtime: Runtime[Context]):
    # 直接访问，IDE 自动补全，类型安全
    user_id = runtime.context.user_id
    db_conn = runtime.context.db_connection


```

**一个 Runtime 对象，搞定所有运行时信息：**

*   🎯 **context**: 静态上下文数据
    
*   💾 **store**: 长期记忆存储
    
*   📤 **stream_writer**: 自定义输出流
    
*   ⏮️ **previous**: 上次执行结果
    

🎭 动态模型选择：智能体变身多面手
------------------

想让你的智能体根据不同场景切换模型和工具？现在只需几行代码：

![](https://mmbiz.qpic.cn/mmbiz_png/Ea6oETrjsvicde7Pj8W4mBPKSByIAEM5bFJmUpLNkxTF2Icwv6icQAHd61rORESN8Giaj7HUCgZKia0trXKIquZMmQ/640?wx_fmt=png&from=appmsg&randomid=u6k0u6mi&watermark=1)

```
@dataclass
class SmartContext:
    provider: Literal["anthropic", "openai"] 
    tools: list[str]

def select_model(state, runtime: Runtime[SmartContext]):
    # 动态选择模型
    model = models[runtime.context.provider]
  
    # 动态筛选工具
    selected_tools = [
        tool for tool in all_tools
        if tool.name in runtime.context.tools
    ]
  
    return model.bind_tools(selected_tools)

# 使用时只需传入配置
agent.invoke(input, context=SmartContext(
    provider="openai", 
    tools=["weather", "search"]
))


```

⚡ 三种耐久性模式：性能与安全的完美平衡
--------------------

LangGraph v0.6 引入了更精细的持久化控制：

![](https://mmbiz.qpic.cn/mmbiz_png/Ea6oETrjsvicde7Pj8W4mBPKSByIAEM5bicReEfFBnbWeiaiaXFayaX4KFrrNFKUpMuyiag8fB35bxDicVpJXgjMqPBA/640?wx_fmt=png&from=appmsg&randomid=v4lmk3g0&watermark=1)

*   🏃‍♂️ **"exit"** 模式：最快速度，图退出时保存
    
*   🚀 **"async"** 模式：平衡之选，异步保存检查点
    
*   🛡️ **"sync"** 模式：最高安全，同步保存检查点
    

根据你的业务需求，自由选择最适合的模式！

🔒 类型安全升级：开发体验全面提升
------------------

现在 `StateGraph` 和 `Pregel` 接口全面支持泛型：

![](https://mmbiz.qpic.cn/mmbiz_png/Ea6oETrjsvicde7Pj8W4mBPKSByIAEM5biaVPem2WfIY7jLlPiakMsxestBeUkQDRboficweaAfywapicJjr9cXiciakg/640?wx_fmt=png&from=appmsg&randomid=nlyyk7ju&watermark=1)

```
# 完整的类型检查支持
builder = StateGraph(
    state_schema=MyState,
    context_schema=MyContext,
    input_schema=MyInput,
    output_schema=MyOutput
)


```

**带来的好处：**

*   ✅ 节点签名在创建时即可验证
    
*   ✅ 输入输出类型自动检查
    
*   ✅ IDE 智能提示更准确
    

🧹 接口精简：为 v1.0 做好准备
-------------------

**Interrupt 接口更清爽：**

*   保留核心：`id` 和 `value`
    
*   移除冗余：`when`、`resumable`、`ns` 等
    

**导入路径更统一：**

```
# 新的标准导入
from langgraph.types import Send, Interrupt
from langgraph.errors import CustomError


```

🎯 v1.0 在即：稳定性的承诺
-----------------

LangGraph v0.6 是迈向 v1.0 的最后一次重大更新！

![](https://mmbiz.qpic.cn/mmbiz_png/Ea6oETrjsvicde7Pj8W4mBPKSByIAEM5bTzCPfPD2IpuaibLIZkJsvYRhHVmFc9unbn1WyYia5qgPgiafAo5yx5fEw/640?wx_fmt=png&from=appmsg&randomid=d6x81em8&watermark=1)

v1.0 发布后，我们将严格遵循语义化版本控制，为你的生产环境提供最可靠的稳定性保障。

💡 开发者福音：迁移无忧
-------------

担心升级成本？别担心！

*   ✅ **完全向下兼容**：现有代码无需修改
    
*   ✅ **渐进式迁移**：按自己的节奏慢慢升级
    
*   ✅ **友好提示**：弃用功能会有明确警告
    

🎉 总结与行动号召
----------

LangGraph v0.6 带来的不仅仅是功能更新，更是开发体验的革命性提升：

🔥 **核心亮点回顾：**

*   全新 Context API 让配置管理变得简单优雅
    
*   动态模型选择让智能体更加灵活强大
    
*   三种耐久性模式满足不同场景需求
    
*   增强的类型安全让开发更有信心
    
*   为 v1.0 稳定版本做好充分准备
    

![](https://mmbiz.qpic.cn/mmbiz_jpg/Ea6oETrjsvicde7Pj8W4mBPKSByIAEM5bdgfvCm0p2zBw1H3pN6YTyqziae4PyL86WA7Dt9q5PQKNhhJr6JZ0fgw/640?wx_fmt=jpeg&from=appmsg&randomid=9zshv5j6&watermark=1)

**立即行动：**

1.  🚀 升级到 LangGraph v0.6，体验全新开发体验
    
2.  💬 加入 LangChain 社区论坛，与全球开发者交流
    
3.  🌟 在 GitHub 给项目点个 Star，支持开源发展
    

智能体开发的未来已来，你准备好了吗？

_关注我们，获取更多 AI 开发技术分享！_

![](https://mmbiz.qpic.cn/mmbiz_jpg/Ea6oETrjsvibmewC1HZdOiaiafXmR01luiaianHjiaXrBp24S00PoVGpcLlbTIib2lnJ4BN3icnjibo8YKvFRdyeuaVUj2g/640?wx_fmt=jpeg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1&randomid=8ssx3wym)