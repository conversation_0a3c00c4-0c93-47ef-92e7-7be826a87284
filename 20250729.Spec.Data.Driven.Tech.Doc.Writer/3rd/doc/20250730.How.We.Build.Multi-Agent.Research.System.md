# How we built our multi-agent research system 我们是如何构建多智能体研究系统的

> 本文由 [简悦 SimpRead](http://ksria.com/simpread/) 转码， 原文地址 [mp.weixin.qq.com](https://mp.weixin.qq.com/s/I4xQ4SSoXLsuO-4GYbZTjQ)

Our Research feature uses multiple Claude agents to explore complex topics more effectively. We share the engineering challenges and the lessons we learned from building this system.

我们的 “研究” 功能使用多个 Claude 智能体，以更高效地探索复杂议题。本文将分享我们在构建该系统过程中遇到的工程挑战以及从中获得的经验教训。

<PERSON> now has Research capabilities that allow it to search across the web, Google Workspace, and any integrations to accomplish complex tasks.

Claude 现在具备 “研究” 功能，能够在网络、Google Workspace 以及各种集成系统中进行搜索，以完成复杂任务。

The journey of this multi-agent system from prototype to production taught us critical lessons about system architecture, tool design, and prompt engineering. A multi-agent system consists of multiple agents (LLMs autonomously using tools in a loop) working together. Our Research feature involves an agent that plans a research process based on user queries, and then uses tools to create parallel agents that search for information simultaneously. Systems with multiple agents introduce new challenges in agent coordination, evaluation, and reliability.

这一多智能体系统从原型到上线的历程，使我们在系统架构、工具设计和提示工程方面收获了许多关键经验。一个多智能体系统由多个智能体（即大模型在闭环中自主使用工具）协作组成。我们的 “研究” 功能包含一个智能体，它会根据用户的提问制定研究流程，并利用工具并行创建多个智能体同时查找信息。多智能体系统会引入一系列新挑战，例如智能体之间的协调、系统评估方式和整体可靠性等问题。

This post breaks down the principles that worked for us—we hope you'll find them useful to apply when building your own multi-agent systems.

本文将分解我们实践中行之有效的原则——希望它们对你在构建自己的多智能体系统时有所启发。

1 Benefits of a multi-agent system 多智能体系统的优势

Research work involves open-ended problems where it’s very difficult to predict the required steps in advance. You can’t hardcode a fixed path for exploring complex topics, as the process is inherently dynamic and path-dependent. When people conduct research, they tend to continuously update their approach based on discoveries, following leads that emerge during investigation.

研究工作通常是开放式问题，难以预先预测所需的步骤。探索复杂议题无法硬编码一个固定路径，因为研究过程本质上是动态的、依赖路径发展的。人类在进行研究时，往往会根据中途的发现不断调整思路，顺着调查过程中出现的新线索不断推进。

This unpredictability makes AI agents particularly well-suited for research tasks. Research demands the flexibility to pivot or explore tangential connections as the investigation unfolds. The model must operate autonomously for many turns, making decisions about which directions to pursue based on intermediate findings. A linear, one-shot pipeline cannot handle these tasks.

正因其不可预测性，AI 智能体尤其适合执行研究任务。研究需要具备灵活调整方向的能力，能在调查过程中发掘和追踪间接关联。模型必须具备在多个回合中自主运行的能力，能够根据中间发现判断下一步的探索方向。线性的一次性流程根本无法胜任这类任务。

The essence of search is compression: distilling insights from a vast corpus. Subagents facilitate compression by operating in parallel with their own context windows, exploring different aspects of the question simultaneously before condensing the most important tokens for the lead research agent. Each subagent also provides separation of concerns—distinct tools, prompts, and exploration trajectories—which reduces path dependency and enables thorough, independent investigations.

搜索的本质是压缩：从庞大语料中提炼出有价值的见解。子智能体通过各自独立的上下文窗口并行运行，能够同时探索问题的不同方面，随后将最关键的内容提炼给主研究智能体，从而实现压缩。每个子智能体还承担特定子任务，使用不同的工具、提示词和探索路径，这种 “关注点分离” 机制降低了路径依赖，使得研究过程更为全面和独立。

Once intelligence reaches a threshold, multi-agent systems become a vital way to scale performance. For instance, although individual humans have become more intelligent in the last 100,000 years, human societies have become exponentially more capable in the information age because of our collective intelligence and ability to coordinate. Even generally-intelligent agents face limits when operating as individuals; groups of agents can accomplish far more.

当智能达到一定门槛后，多智能体系统就成为提升性能的关键手段。例如，在过去十万年间，个体人类的智力增长有限，但信息时代的人类社会却通过集体智能和协作能力实现了指数级提升。即使是通用智能体，作为单个个体运行时也有其局限性；而一群智能体协作，能够完成远远超出个体能力的任务。

Our internal evaluations show that multi-agent research systems excel especially for breadth-first queries that involve pursuing multiple independent directions simultaneously. We found that a multi-agent system with Claude Opus 4 as the lead agent and Claude Sonnet 4 subagents outperformed single-agent Claude Opus 4 by 90.2% on our internal research eval. For example, when asked to identify all the board members of the companies in the Information Technology S&P 500, the multi-agent system found the correct answers by decomposing this into tasks for subagents, while the single agent system failed to find the answer with slow, sequential searches.

我们的内部评估显示，多智能体研究系统在 “广度优先” 的查询中表现尤其出色，这类任务需要同时探索多个互不关联的方向。我们发现，以 Claude Opus 4 作为主智能体、Claude Sonnet 4 作为子智能体构建的多智能体系统，在研究评测中的表现比单一 Claude Opus 4 提高了 90.2%。例如，当被要求列出标普 500 信息技术板块所有公司的董事会成员时，多智能体系统能将任务拆分分配给多个子智能体并找到正确答案，而单智能体系统只能顺序慢慢查找，最终未能完成任务。

Multi-agent systems work mainly because they help spend enough tokens to solve the problem. In our analysis, three factors explained 95% of the performance variance in the BrowseComp evaluation (which tests the ability of browsing agents to locate hard-to-find information). We found that token usage by itself explains 80% of the variance, with the number of tool calls and the model choice as the two other explanatory factors. This finding validates our architecture that distributes work across agents with separate context windows to add more capacity for parallel reasoning. The latest Claude models act as large efficiency multipliers on token use, as upgrading to Claude Sonnet 4 is a larger performance gain than doubling the token budget on Claude Sonnet 3.7. Multi-agent architectures effectively scale token usage for tasks that exceed the limits of single agents.

多智能体系统之所以有效，主要是因为它能 “花足够的 token 来把问题解决掉”。在我们的分析中，有三个因素解释了 BrowseComp 评测（评估浏览型智能体查找难找信息的能力）中 95% 的性能差异：其中仅 token 使用量一项就解释了 80% 的差异，另两个因素是工具调用次数和模型选择。这一发现验证了我们所采用的架构设计：通过不同智能体各自的上下文窗口分配任务，从而提升并行推理能力。最新的 Claude 模型极大提高了 token 使用效率，例如将 Claude Sonnet 3.7 升级为 Claude Sonnet 4 所带来的性能提升，比将原模型的 token 配额翻倍还要明显。对于超出单智能体能力上限的任务，多智能体架构提供了有效的 token 使用扩展方式。

There is a downside: in practice, these architectures burn through tokens fast. In our data, agents typically use about 4× more tokens than chat interactions, and multi-agent systems use about 15× more tokens than chats. For economic viability, multi-agent systems require tasks where the value of the task is high enough to pay for the increased performance. Further, some domains that require all agents to share the same context or involve many dependencies between agents are not a good fit for multi-agent systems today. For instance, most coding tasks involve fewer truly parallelizable tasks than research, and LLM agents are not yet great at coordinating and delegating to other agents in real time. We’ve found that multi-agent systems excel at valuable tasks that involve heavy parallelization, information that exceeds single context windows, and interfacing with numerous complex tools.

但这类架构也有缺点：在实践中，它们 token 消耗极高。根据我们的数据，单个智能体的 token 使用量通常是聊天交互的 4 倍，而多智能体系统则是聊天交互的约 15 倍。要实现经济可行性，多智能体系统必须应用于任务价值足够高、能覆盖其性能成本的场景。此外，那些需要所有智能体共享相同上下文或智能体之间依赖性很强的任务，目前还不适合使用多智能体系统。例如，大多数编程任务比研究任务更难进行真正的并行拆解，而且当前 LLM 智能体在实时协调和任务委派方面能力仍有限。我们发现，多智能体系统在处理高价值、高并行度、信息量超出单一上下文窗口、以及需要对接众多复杂工具的任务时表现最为出色。

2 Architecture overview for Research 研究功能的架构概览

Our Research system uses a multi-agent architecture with an orchestrator-worker pattern, where a lead agent coordinates the process while delegating to specialized subagents that operate in parallel.

我们的研究系统采用多智能体架构，遵循 “协调者 - 执行者” 模式：主智能体负责统筹整个流程，同时将任务分派给并行运行的专业子智能体。

![](https://mmbiz.qpic.cn/mmbiz_png/34jyl4KyicvlTsJIUWjtQ7DCLNH3E1KNlToqecZZRibZbjobEibZj9YgCGwKS8iaNb0Z5hiayXruBSIfgY2w8tJWmnQ/640?wx_fmt=png&from=appmsg&randomid=66sxyzyr&watermark=1)

The multi-agent architecture in action: user queries flow through a lead agent that creates specialized subagents to search for different aspects in parallel.

多智能体架构的实际运作：用户的查询首先由主智能体接收，随后主智能体创建多个专业子智能体，以并行方式分别查找问题的不同方面。

When a user submits a query, the lead agent analyzes it, develops a strategy, and spawns subagents to explore different aspects simultaneously. As shown in the diagram above, the subagents act as intelligent filters by iteratively using search tools to gather information, in this case on AI agent companies in 2025, and then returning a list of companies to the lead agent so it can compile a final answer.

当用户提交查询后，主智能体会对其进行分析，制定策略，并生成多个子智能体以同时探索不同方面。如上图所示，子智能体通过反复使用搜索工具起到 “智能过滤器” 的作用，例如在本例中查找 2025 年的 AI 智能体公司，最后将公司名单返回给主智能体，由其整理生成最终答案。

Traditional approaches using Retrieval Augmented Generation (RAG) use static retrieval. That is, they fetch some set of chunks that are most similar to an input query and use these chunks to generate a response. In contrast, our architecture uses a multi-step search that dynamically finds relevant information, adapts to new findings, and analyzes results to formulate high-quality answers.

传统的 “检索增强生成”（RAG）方法采用的是静态检索，即获取与输入查询最相似的一组文本片段，并基于这些片段生成回答。相比之下，我们的架构采用多步骤搜索，能够动态寻找相关信息，实时调整方向，并对结果进行分析，以生成更高质量的回答。

![](https://mmbiz.qpic.cn/mmbiz_png/34jyl4KyicvlTsJIUWjtQ7DCLNH3E1KNlvdYbbKGukjyYkeY1j50VAPhq8rONUtatj3JZjCBQ0UA0BDdeiaNXVnQ/640?wx_fmt=png&from=appmsg&randomid=q3nfcibo&watermark=1)

Process diagram showing the complete workflow of our multi-agent Research system. When a user submits a query, the system creates a LeadResearcher agent that enters an iterative research process. The LeadResearcher begins by thinking through the approach and saving its plan to Memory to persist the context, since if the context window exceeds 200,000 tokens it will be truncated and it is important to retain the plan. It then creates specialized Subagents (two are shown here, but it can be any number) with specific research tasks. Each Subagent independently performs web searches, evaluates tool results using interleaved thinking, and returns findings to the LeadResearcher. The LeadResearcher synthesizes these results and decides whether more research is needed—if so, it can create additional subagents or refine its strategy. Once sufficient information is gathered, the system exits the research loop and passes all findings to a CitationAgent, which processes the documents and research report to identify specific locations for citations. This ensures all claims are properly attributed to their sources. The final research results, complete with citations, are then returned to the user.

流程图展示了我们多智能体研究系统的完整工作流程。当用户提交查询后，系统会创建一个 LeadResearcher（主研究者）智能体，进入迭代式研究流程。LeadResearcher 首先会思考研究方案，并将计划保存到内存中，以保持上下文信息——因为一旦上下文窗口超过 200,000 个 token，就会被截断，因此保留计划内容非常关键。接着它会根据具体研究任务创建若干个专业子智能体（图中显示为两个，实际可为任意数量）。每个子智能体独立进行网页搜索，使用交叉推理方式评估工具结果，并将研究发现返回给 LeadResearcher。LeadResearcher 对结果进行整合，并判断是否需要进一步研究——如果需要，它可以创建新的子智能体或调整研究策略。当系统收集到足够信息后，就会退出研究循环，并将全部研究成果交给 CitationAgent（引文智能体），由其处理文档与研究报告，标注引用位置，确保所有论断均有确切来源支撑。最终，系统将包含完整引文的研究结果返回给用户。

3 Prompt engineering and evaluations for research agents 研究智能体的提示词工程与评估

Multi-agent systems have key differences from single-agent systems, including a rapid growth in coordination complexity. Early agents made errors like spawning 50 subagents for simple queries, scouring the web endlessly for nonexistent sources, and distracting each other with excessive updates. Since each agent is steered by a prompt, prompt engineering was our primary lever for improving these behaviors. Below are some principles we learned for prompting agents:

多智能体系统与单智能体系统之间存在关键差异，其中最明显的是协调复杂度迅速上升。早期的智能体常常出现一些问题，比如为简单问题生成多达 50 个子智能体、在网上无止境地搜索并不存在的内容，或者频繁互相打断、造成干扰。由于每个智能体都是通过提示词驱动的，提示词工程成为我们改善这些行为的主要手段。以下是我们在设计提示词过程中总结的一些原则：

Think like your agents. To iterate on prompts, you must understand their effects. To help us do this, we built simulations using our Console with the exact prompts and tools from our system, then watched agents work step-by-step. This immediately revealed failure modes: agents continuing when they already had sufficient results, using overly verbose search queries, or selecting incorrect tools. Effective prompting relies on developing an accurate mental model of the agent, which can make the most impactful changes obvious.

要像智能体那样思考。优化提示词的前提是理解提示的实际效果。为此，我们在控制台中搭建了与真实系统相同的提示词和工具模拟环境，逐步观察智能体的执行过程。这样做很快暴露出一系列失败模式，比如：在已经找到足够信息的情况下仍继续搜索、使用冗长繁复的搜索语句、或选错工具。有效的提示词设计依赖于建立起对智能体行为的准确心理模型，这能帮助我们迅速发现最有影响力的改进点。

Teach the orchestrator how to delegate. In our system, the lead agent decomposes queries into subtasks and describes them to subagents. Each subagent needs an objective, an output format, guidance on the tools and sources to use, and clear task boundaries. Without detailed task descriptions, agents duplicate work, leave gaps, or fail to find necessary information. We started by allowing the lead agent to give simple, short instructions like 'research the semiconductor shortage,' but found these instructions often were vague enough that subagents misinterpreted the task or performed the exact same searches as other agents. For instance, one subagent explored the 2021 automotive chip crisis while 2 others duplicated work investigating current 2025 supply chains, without an effective division of labor.

教会主智能体如何分工。在我们的系统中，主智能体会将用户查询拆解为若干子任务，并将其描述给子智能体。每个子智能体都需要清晰的目标、输出格式、工具与信息源指引，以及明确的任务边界。如果任务描述不够详细，子智能体就容易重复工作、遗漏要点，或找不到关键信息。我们一开始允许主智能体使用简单简短的指令，比如 “研究半导体短缺问题”，但很快发现这样的指令过于模糊，导致子智能体对任务理解偏差，甚至执行完全相同的搜索。例如，一个子智能体调查了 2021 年的汽车芯片危机，而另外两个子智能体则重复研究了 2025 年的供应链，缺乏有效的分工协作。

Scale effort to query complexity. Agents struggle to judge appropriate effort for different tasks, so we embedded scaling rules in the prompts. Simple fact-finding requires just 1 agent with 3-10 tool calls, direct comparisons might need 2-4 subagents with 10-15 calls each, and complex research might use more than 10 subagents with clearly divided responsibilities. These explicit guidelines help the lead agent allocate resources efficiently and prevent overinvestment in simple queries, which was a common failure mode in our early versions.

根据查询复杂度匹配资源投入。智能体往往难以判断不同任务应投入多少精力，因此我们在提示词中嵌入了 “规模调整” 规则。简单的事实查找任务只需要 1 个智能体调用 3～10 次工具；对比类任务可能需要 2～4 个子智能体，每个调用 10～15 次；复杂研究则可能动用 10 个以上子智能体，并分工明确。这些明确的指导方针帮助主智能体合理分配资源，避免在简单查询上投入过多，这是我们早期版本中的常见失败模式。

Tool design and selection are critical. Agent-tool interfaces are as critical as human-computer interfaces. Using the right tool is efficient—often, it’s strictly necessary. For instance, an agent searching the web for context that only exists in Slack is doomed from the start. With MCP servers that give the model access to external tools, this problem compounds, as agents encounter unseen tools with descriptions of wildly varying quality. We gave our agents explicit heuristics: for example, examine all available tools first, match tool usage to user intent, search the web for broad external exploration, or prefer specialized tools over generic ones. Bad tool descriptions can send agents down completely wrong paths, so each tool needs a distinct purpose and a clear description.

工具的设计与选择至关重要。智能体与工具之间的接口就像人机界面一样关键。使用合适的工具不仅高效，很多时候更是完成任务的必要条件。例如，如果一个智能体去网上搜索那些只存在于 Slack 的上下文信息，注定一开始就走错了方向。MCP 服务器为模型提供了外部工具接入能力，但也因此增加了出错风险，因为智能体可能会遇到描述质量参差不齐的新工具。我们为智能体设置了明确的启发式规则：例如先浏览所有可用工具、将工具选择与用户意图匹配、在外部信息搜索中优先选择网页搜索、使用专业工具优先于通用工具。糟糕的工具描述可能完全误导智能体，因此每个工具必须具备明确的用途和清晰的描述。

Let agents improve themselves. We found that the Claude 4 models can be excellent prompt engineers. When given a prompt and a failure mode, they are able to diagnose why the agent is failing and suggest improvements. We even created a tool-testing agent—when given a flawed MCP tool, it attempts to use the tool and then rewrites the tool description to avoid failures. By testing the tool dozens of times, this agent found key nuances and bugs. This process for improving tool ergonomics resulted in a 40% decrease in task completion time for future agents using the new description, because they were able to avoid most mistakes.

让智能体自我改进。我们发现 Claude 4 模型本身就是非常优秀的提示词工程师。当提供一个提示词和其失败场景时，模型能够诊断失败原因并提出改进建议。我们甚至创建了一个 “工具测试智能体”：它会尝试使用存在问题的 MCP 工具，并重新撰写工具描述以避免失败。通过反复测试，这个智能体能够找出工具中的关键细节与缺陷。这种改进工具可用性的流程，使未来使用新描述的智能体任务完成时间缩短了 40%，因为它们能有效规避大部分错误。

Start wide, then narrow down. Search strategy should mirror expert human research: explore the landscape before drilling into specifics. Agents often default to overly long, specific queries that return few results. We counteracted this tendency by prompting agents to start with short, broad queries, evaluate what’s available, then progressively narrow focus.

先广后窄，逐步聚焦。搜索策略应模仿人类专家的研究过程：先全面了解，再聚焦细节。智能体往往倾向于一开始就发出冗长且具体的查询，但这类查询往往收效甚微。我们通过提示词引导智能体从简短、广泛的搜索起步，先评估已有信息，再逐步收窄查询范围。

Guide the thinking process. Extended thinking mode, which leads Claude to output additional tokens in a visible thinking process, can serve as a controllable scratchpad. The lead agent uses thinking to plan its approach, assessing which tools fit the task, determining query complexity and subagent count, and defining each subagent’s role. Our testing showed that extended thinking improved instruction-following, reasoning, and efficiency. Subagents also plan, then use interleaved thinking after tool results to evaluate quality, identify gaps, and refine their next query. This makes subagents more effective in adapting to any task.

引导思考过程。“扩展思考模式” 可以使 Claude 在输出过程中展示其思考轨迹，作为一种可控的 “草稿板”。主智能体通过思考来规划研究路径、评估适用工具、判断查询复杂度与子智能体数量，并明确分工。我们的测试表明，扩展思考显著提升了指令遵循度、推理能力和执行效率。子智能体在执行工具后也会使用交叉思考，来评估结果质量、发现信息缺口，并优化下一轮查询，使其在适应不同任务时更为高效。

Parallel tool calling transforms speed and performance. Complex research tasks naturally involve exploring many sources. Our early agents executed sequential searches, which was painfully slow. For speed, we introduced two kinds of parallelization: (1) the lead agent spins up 3-5 subagents in parallel rather than serially; (2) the subagents use 3+ tools in parallel. These changes cut research time by up to 90% for complex queries, allowing Research to do more work in minutes instead of hours while covering more information than other systems.

并行调用工具显著提升速度与性能。复杂研究任务往往涉及大量信息源。我们早期的智能体执行的是顺序搜索，效率极低。为提速，我们引入了两种并行机制：（1）主智能体同时创建 3～5 个子智能体，而非按顺序生成；（2）子智能体并行调用 3 个以上的工具。这些改进将复杂查询的研究时间缩短了最多 90%，使得整个研究系统能在几分钟内完成原本需数小时的任务，且覆盖的信息面比其他系统更广。

Our prompting strategy focuses on instilling good heuristics rather than rigid rules. We studied how skilled humans approach research tasks and encoded these strategies in our prompts—strategies like decomposing difficult questions into smaller tasks, carefully evaluating the quality of sources, adjusting search approaches based on new information, and recognizing when to focus on depth (investigating one topic in detail) vs. breadth (exploring many topics in parallel). We also proactively mitigated unintended side effects by setting explicit guardrails to prevent the agents from spiraling out of control. Finally, we focused on a fast iteration loop with observability and test cases.

我们的提示词策略更侧重于灌输有效的启发式方法，而非制定死板规则。我们研究了熟练人类研究者的做法，并将其策略编码进提示词中——例如：将复杂问题拆解为更小的子任务、仔细评估信息源质量、根据新发现动态调整搜索方式、判断何时应聚焦深入单一主题、何时应广泛探索多个方向。我们还主动设立明确的限制机制，以防止智能体行为失控。最后，我们围绕可观测性与测试用例建立了快速迭代机制。

4 Effective evaluation of agents  智能体的有效评估方式

Good evaluations are essential for building reliable AI applications, and agents are no different. However, evaluating multi-agent systems presents unique challenges. Traditional evaluations often assume that the AI follows the same steps each time: given input X, the system should follow path Y to produce output Z. But multi-agent systems don't work this way. Even with identical starting points, agents might take completely different valid paths to reach their goal. One agent might search three sources while another searches ten, or they might use different tools to find the same answer. Because we don’t always know what the right steps are, we usually can't just check if agents followed the “correct” steps we prescribed in advance. Instead, we need flexible evaluation methods that judge whether agents achieved the right outcomes while also following a reasonable process.

良好的评估对于构建可靠的 AI 应用至关重要，智能体也不例外。然而，多智能体系统的评估存在独特挑战。传统评估方法往往假设 AI 每次都沿着相同的步骤运行：输入 X，按路径 Y，得到输出 Z。但多智能体系统并不如此运作。即使起点相同，不同智能体也可能选择完全不同但都合理的路径来完成目标。有的智能体可能查阅 3 个信息源，有的则查 10 个；有的使用某种工具，有的用另一种工具，但可能最终得出相同结论。由于我们通常无法预先定义 “正确步骤”，因此无法简单地比对智能体是否按照预设路线运行。我们需要的是更灵活的评估方法，既能判断智能体是否得到正确结果，也能评估其过程是否合理。

Start evaluating immediately with small samples. In early agent development, changes tend to have dramatic impacts because there is abundant low-hanging fruit. A prompt tweak might boost success rates from 30% to 80%. With effect sizes this large, you can spot changes with just a few test cases. We started with a set of about 20 queries representing real usage patterns. Testing these queries often allowed us to clearly see the impact of changes. We often hear that AI developer teams delay creating evals because they believe that only large evals with hundreds of test cases are useful. However, it’s best to start with small-scale testing right away with a few examples, rather than delaying until you can build more thorough evals.

从小样本评估立即开始。在智能体开发早期，很多改动都会带来显著提升，因为有大量 “低垂果实” 尚未采摘。一个提示词的微调可能就能把成功率从 30% 提升到 80%。在这种巨大变化下，只需几个测试案例就能看出改动的效果。我们一开始采用了约 20 个代表实际使用场景的查询，这些查询经常能清晰反映出系统改动的影响。我们常听说一些 AI 团队推迟构建评估体系，理由是“只有包含几百个测试用例的大型评估才有意义”。但实际上，与其等着构建更完整的评估系统，不如从一开始就用少量例子进行小规模测试。

LLM-as-judge evaluation scales when done well. Research outputs are difficult to evaluate programmatically, since they are free-form text and rarely have a single correct answer. LLMs are a natural fit for grading outputs. We used an LLM judge that evaluated each output against criteria in a rubric: factual accuracy (do claims match sources?), citation accuracy (do the cited sources match the claims?), completeness (are all requested aspects covered?), source quality (did it use primary sources over lower-quality secondary sources?), and tool efficiency (did it use the right tools a reasonable number of times?). We experimented with multiple judges to evaluate each component, but found that a single LLM call with a single prompt outputting scores from 0.0-1.0 and a pass-fail grade was the most consistent and aligned with human judgements. This method was especially effective when the eval test cases did have a clear answer, and we could use the LLM judge to simply check if the answer was correct (i.e. did it accurately list the pharma companies with the top 3 largest R&D budgets?). Using an LLM as a judge allowed us to scalably evaluate hundreds of outputs.

使用 “大模型评审” 能实现评估的规模化，只要设计得当。研究类输出通常是自由格式文本，极少存在唯一正确答案，因此难以通过程序评估。LLM 自然适合用来评判这些结果。我们使用了一个 LLM 评审，根据预设的评分标准对每个输出进行评估，包括：事实准确性（论述是否符合来源）、引用准确性（引用内容是否与结论一致）、完整性（是否覆盖所有要求方面）、信息源质量（是否优先使用一手来源而非质量较低的二手资料）、工具使用效率（是否合理调用了正确的工具等）。我们尝试用多个 LLM 分别评估各项，但最后发现一个单一提示词生成 0.0～1.0 的评分加上 “通过 / 未通过” 标记的评估方式，与人工判断的结果最一致、最稳定。这种方法在测试用例有明确答案时特别有效，例如判断智能体是否准确列出了研发投入最高的三家制药公司。使用 LLM 担任评审，使我们得以高效评估数百个输出结果。

Human evaluation catches what automation misses. People testing agents find edge cases that evals miss. These include hallucinated answers on unusual queries, system failures, or subtle source selection biases. In our case, human testers noticed that our early agents consistently chose SEO-optimized content farms over authoritative but less highly-ranked sources like academic PDFs or personal blogs. Adding source quality heuristics to our prompts helped resolve this issue. Even in a world of automated evaluations, manual testing remains essential.

人工评估能发现自动评估遗漏的问题。人工测试者往往能发现边缘案例，是自动化评估无法覆盖的，比如：罕见查询中的幻觉内容、系统崩溃、或微妙的信息源选择偏差等。在我们的案例中，人工测试者注意到，早期的智能体经常优先选择 SEO 优化的网站内容农场，而忽略了权威但搜索排名较低的来源，如学术 PDF 或个人博客。我们通过在提示词中加入 “信息源质量” 相关启发式规则，有效解决了这一问题。即使在自动评估普及的背景下，人工测试依然不可或缺。

Multi-agent systems have emergent behaviors, which arise without specific programming. For instance, small changes to the lead agent can unpredictably change how subagents behave. Success requires understanding interaction patterns, not just individual agent behavior. Therefore, the best prompts for these agents are not just strict instructions, but frameworks for collaboration that define the division of labor, problem-solving approaches, and effort budgets. Getting this right relies on careful prompting and tool design, solid heuristics, observability, and tight feedback loops. See the open-source prompts in our Cookbook for example prompts from our system.

多智能体系统具有 “涌现行为”，即智能体会出现一些未被显式编程指令规定的复杂行为。例如，对主智能体的一个小改动，可能会不可预测地影响子智能体的行为。因此，成功的关键在于理解系统的交互模式，而不仅仅是单个智能体的行为。对这类智能体来说，最好的提示词并非死板的操作指令，而是一个协作框架——它应明确各自分工、解决问题的路径、以及资源投入上限等。要做到这一点，需要精心设计提示词与工具、制定有效启发式规则、具备良好的可观测性，并建立紧密的反馈循环。可参考我们 Cookbook 中开源的提示词范例，了解系统实际使用的 prompt 设计。

5 Production reliability and engineering challenges  生产环境下的可靠性与工程挑战

In traditional software, a bug might break a feature, degrade performance, or cause outages. In agentic systems, minor changes cascade into large behavioral changes, which makes it remarkably difficult to write code for complex agents that must maintain state in a long-running process.

在传统软件中，一个 bug 可能导致功能中断、性能下降，或系统宕机。而在智能体系统中，即便是微小的改动也可能引发大规模的行为变化，这使得为那些需要长期保持状态的复杂智能体编写稳定代码变得极具挑战。

Agents are stateful and errors compound. Agents can run for long periods of time, maintaining state across many tool calls. This means we need to durably execute code and handle errors along the way. Without effective mitigations, minor system failures can be catastrophic for agents. When errors occur, we can't just restart from the beginning: restarts are expensive and frustrating for users. Instead, we built systems that can resume from where the agent was when the errors occurred. We also use the model’s intelligence to handle issues gracefully: for instance, letting the agent know when a tool is failing and letting it adapt works surprisingly well. We combine the adaptability of AI agents built on Claude with deterministic safeguards like retry logic and regular checkpoints.

智能体是有状态的，且错误会累积。它们可能长时间运行，在多次工具调用之间持续保持状态。这意味着我们需要能够稳定执行代码，并在过程中妥善处理各种错误。否则，即使是轻微的系统故障，对智能体来说也可能是灾难性的。一旦出错，我们不能简单地从头开始重启——这不仅代价高昂，还会严重影响用户体验。因此，我们构建了可从错误发生点继续执行的系统。同时，我们也充分利用模型的智能能力来灵活应对问题：例如，当某个工具失效时，模型可以识别并及时调整策略，效果出乎意料地好。我们将 Claude 智能体的适应性与确定性的工程保障机制（如重试逻辑与定期检查点）相结合，提升系统鲁棒性。

Debugging benefits from new approaches. Agents make dynamic decisions and are non-deterministic between runs, even with identical prompts. This makes debugging harder. For instance, users would report agents “not finding obvious information,” but we couldn't see why. Were the agents using bad search queries? Choosing poor sources? Hitting tool failures? Adding full production tracing let us diagnose why agents failed and fix issues systematically. Beyond standard observability, we monitor agent decision patterns and interaction structures—all without monitoring the contents of individual conversations, to maintain user privacy. This high-level observability helped us diagnose root causes, discover unexpected behaviors, and fix common failures.

调试需要全新方法。智能体的行为具有动态性和非确定性，即使提示词完全相同，每次运行的结果也可能不同，这使调试难度大幅上升。例如，用户可能反馈 “智能体没能找到明明存在的信息”，但我们无法立即判断原因：是搜索语句不佳？信息源选择不当？工具出错了？引入完整的生产追踪系统后，我们得以系统性地诊断智能体失败的原因并加以修复。除了基本可观测性之外，我们还监测智能体的决策模式与交互结构——在不查看用户具体对话内容的前提下，确保隐私的同时进行高层级监控。这种高维度可观测性帮助我们找出根本原因、发现意料之外的行为，并修复常见故障。

Deployment needs careful coordination. Agent systems are highly stateful webs of prompts, tools, and execution logic that run almost continuously. This means that whenever we deploy updates, agents might be anywhere in their process. We therefore need to prevent our well-meaning code changes from breaking existing agents. We can’t update every agent to the new version at the same time. Instead, we use rainbow deployments to avoid disrupting running agents, by gradually shifting traffic from old to new versions while keeping both running simultaneously.

部署需要精细协调。智能体系统是由提示词、工具和执行逻辑构成的高度有状态网络，几乎持续运行。这意味着每当我们部署更新时，智能体可能正处于流程的任意阶段。因此，我们必须确保良性代码更新不会破坏已有的智能体流程。我们不能一次性将所有智能体升级至新版本，而是采用 “彩虹部署” 策略——通过逐步转移流量，将旧版本平滑过渡到新版本，同时保持两者并行运行，以避免中断任务。

Synchronous execution creates bottlenecks. Currently, our lead agents execute subagents synchronously, waiting for each set of subagents to complete before proceeding. This simplifies coordination, but creates bottlenecks in the information flow between agents. For instance, the lead agent can’t steer subagents, subagents can’t coordinate, and the entire system can be blocked while waiting for a single subagent to finish searching. Asynchronous execution would enable additional parallelism: agents working concurrently and creating new subagents when needed. But this asynchronicity adds challenges in result coordination, state consistency, and error propagation across the subagents. As models can handle longer and more complex research tasks, we expect the performance gains will justify the complexity.

同步执行会形成瓶颈。目前我们的主智能体是以同步方式执行子智能体，即等待一组子智能体全部完成任务后才能继续。这虽然简化了协调逻辑，但却限制了智能体之间的信息流通。例如，主智能体无法在运行中调整子智能体行为，子智能体之间也无法协同，整个系统可能因等待某个子智能体完成搜索而陷入阻塞。若采用异步执行，则可实现更强的并行性——智能体可以并发运行，并按需动态创建新的子智能体。但异步模式也会带来新的挑战，如结果协调、状态一致性和错误传递等问题。随着模型处理更长、更复杂任务的能力提升，我们预计异步带来的性能收益将值得应对这些复杂性。

6 Conclusion 结语

When building AI agents, the last mile often becomes most of the journey. Codebases that work on developer machines require significant engineering to become reliable production systems. The compound nature of errors in agentic systems means that minor issues for traditional software can derail agents entirely. One step failing can cause agents to explore entirely different trajectories, leading to unpredictable outcomes. For all the reasons described in this post, the gap between prototype and production is often wider than anticipated.

构建 AI 智能体时，最难走的往往是最后一公里。在开发者本地能正常运行的代码，要成为可靠的生产级系统，仍需大量工程投入。智能体系统的错误具有 “复合效应”：在传统软件中只是小问题，在智能体中可能导致系统完全偏离原有轨迹，产生不可预测的结果。正因如此，原型与正式上线系统之间的差距，往往比预期更大。

Despite these challenges, multi-agent systems have proven valuable for open-ended research tasks. Users have said that Claude helped them find business opportunities they hadn’t considered, navigate complex healthcare options, resolve thorny technical bugs, and save up to days of work by uncovering research connections they wouldn't have found alone. Multi-agent research systems can operate reliably at scale with careful engineering, comprehensive testing, detail-oriented prompt and tool design, robust operational practices, and tight collaboration between research, product, and engineering teams who have a strong understanding of current agent capabilities. We're already seeing these systems transform how people solve complex problems.

尽管面临诸多挑战，多智能体系统在处理开放式研究任务中已展现出巨大价值。用户表示，Claude 帮助他们发现了此前未曾考虑的商业机会、理清了复杂的医疗选项、解决了棘手的技术故障，并通过挖掘研究之间的关联节省了数天工作量。通过细致的工程实现、全面的测试、精心设计的提示词与工具、稳健的运维实践，以及研发、产品和工程团队之间对智能体能力充分理解的紧密合作，多智能体研究系统能够在规模化场景下实现可靠运行。我们已经看到了这类系统正在改变人们解决复杂问题的方式。

![](https://mmbiz.qpic.cn/mmbiz_png/34jyl4KyicvlTsJIUWjtQ7DCLNH3E1KNldqNicr8Xp5Vt7UKiaT5jFa7krYU7blUgVJT4ibbUtibrmussjUgKiaWLCOw/640?wx_fmt=png&from=appmsg&randomid=zuz9qtkg&watermark=1)

A Clio embedding plot showing the most common ways people are using the Research feature today. The top use case categories are developing software systems across specialized domains (10%), develop and optimize professional and technical content (8%), develop business growth and revenue generation strategies (8%), assist with academic research and educational material development (7%), and research and verify information about people, places, or organizations (5%).

一张 Clio 嵌入图展示了当前用户使用 “研究” 功能的最常见方式。使用场景的前几大类别包括：跨专业领域开发软件系统（10%）、撰写和优化专业与技术内容（8%）、制定业务增长与创收策略（8%）、辅助学术研究与教学材料开发（7%），以及调研与核实人物、地点或组织相关信息（5%）。

Appendix 附录

Below are some additional miscellaneous tips for multi-agent systems.

以下是一些关于多智能体系统的补充建议。

End-state evaluation of agents that mutate state over many turns. Evaluating agents that modify persistent state across multi-turn conversations presents unique challenges. Unlike read-only research tasks, each action can change the environment for subsequent steps, creating dependencies that traditional evaluation methods struggle to handle. We found success focusing on end-state evaluation rather than turn-by-turn analysis. Instead of judging whether the agent followed a specific process, evaluate whether it achieved the correct final state. This approach acknowledges that agents may find alternative paths to the same goal while still ensuring they deliver the intended outcome. For complex workflows, break evaluation into discrete checkpoints where specific state changes should have occurred, rather than attempting to validate every intermediate step.

对在多轮交互中持续修改状态的智能体进行 “最终状态评估”。评估那些会在多轮对话中持续修改持久状态的智能体时，面临独特挑战。与只读类研究任务不同，每个操作都可能影响后续步骤的环境，造成依赖关系，而传统评估方法很难应对这一点。我们发现，相比逐步评估，聚焦于“最终状态” 更为有效。即，不再评判智能体是否按照特定步骤执行，而是判断它是否达成了正确的最终结果。这种方法承认智能体可能通过多种路径实现目标，同时确保其结果符合预期。对于复杂流程，可将评估拆分为若干关键检查点，每个点验证是否发生了应有的状态变更，而无需验证全部中间步骤。

Long-horizon conversation management. Production agents often engage in conversations spanning hundreds of turns, requiring careful context management strategies. As conversations extend, standard context windows become insufficient, necessitating intelligent compression and memory mechanisms. We implemented patterns where agents summarize completed work phases and store essential information in external memory before proceeding to new tasks. When context limits approach, agents can spawn fresh subagents with clean contexts while maintaining continuity through careful handoffs. Further, they can retrieve stored context like the research plan from their memory rather than losing previous work when reaching the context limit. This distributed approach prevents context overflow while preserving conversation coherence across extended interactions.

长对话管理。实际环境下的智能体往往需要参与数百轮的对话交互，这就要求对上下文进行精细管理。随着对话深入，标准的上下文窗口变得难以满足，需要引入智能压缩和记忆机制。我们采用的做法是：在每个阶段性任务完成后，让智能体进行总结，并将关键信息存储到外部记忆中，然后再继续下一个任务。当上下文接近上限时，智能体可生成新的子智能体，利用 “干净” 的上下文开始工作，同时通过合理的交接方式保持任务连续性。此外，智能体还能从记忆中调用已存储的内容（如研究计划），避免在达到上下文限制时丢失先前成果。这种 “分布式” 策略可以有效防止上下文溢出，同时在长对话中维持整体一致性。

Subagent output to a filesystem to minimize the ‘game of telephone.’ Direct subagent outputs can bypass the main coordinator for certain types of results, improving both fidelity and performance. Rather than requiring subagents to communicate everything through the lead agent, implement artifact systems where specialized agents can create outputs that persist independently. Subagents call tools to store their work in external systems, then pass lightweight references back to the coordinator. This prevents information loss during multi-stage processing and reduces token overhead from copying large outputs through conversation history. The pattern works particularly well for structured outputs like code, reports, or data visualizations where the subagent's specialized prompt produces better results than filtering through a general coordinator.

子智能体将输出写入文件系统，减少 “传话游戏” 式信息失真。在某些任务中，子智能体可直接将结果输出到外部系统，绕过主协调智能体，从而提高准确性与性能。相比让所有信息都通过主智能体中转，构建 “工件系统” 会更高效：子智能体调用工具将成果保存到外部持久化存储中，然后仅将简洁的引用信息返回给主智能体。这样可避免多阶段处理中信息丢失，也能减少将大型输出重复写入对话历史所带来的 token 开销。这种方式特别适用于结构化输出，如代码、报告或数据可视化等场景，在这些任务中，子智能体依赖定制提示词生成的内容通常比通过通用协调器处理后的结果质量更高。

原文链接：  

https://www.anthropic.com/engineering/built-multi-agent-research-system