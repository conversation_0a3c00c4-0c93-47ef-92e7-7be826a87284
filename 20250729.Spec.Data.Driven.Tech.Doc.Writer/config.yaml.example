# 多智能体技术文档写作系统配置模板
# 复制此文件为 config.yaml 并根据实际环境修改配置

# ============================================================================
# 核心配置 - 必需配置项
# ============================================================================

# LLM 配置 - 必需
llm:
  # 主要 LLM 提供商配置
  primary:
    provider: "openai"  # openai, anthropic, deepseek, gemini, azure, ollama
    model: "gpt-4o"
    api_key: "${OPENAI_API_KEY}"  # 环境变量或直接填写
    base_url: null  # 可选，自定义API端点
    temperature: 0.1
    max_tokens: 8192
    timeout: 300

  # 备用 LLM 配置（可选）
  # fallback:
  #   provider: "anthropic"
  #   model: "claude-3-5-sonnet-20241022"
  #   api_key: "${ANTHROPIC_API_KEY}"
  #   temperature: 0.1
  #   max_tokens: 4096
  #   timeout: 300

  # ============================================================================
  # LLM 提供商配置示例 - 根据需要选择并取消注释
  # ============================================================================

  # OpenAI 配置示例
  # primary:
  #   provider: "openai"
  #   model: "gpt-4o"
  #   api_key: "${OPENAI_API_KEY}"
  #   base_url: null  # 可选，自定义API端点
  #   temperature: 0.1
  #   max_tokens: 4096
  #   timeout: 300

  # Anthropic Claude 配置示例
  # primary:
  #   provider: "anthropic"
  #   model: "claude-3-5-sonnet-20241022"
  #   api_key: "${ANTHROPIC_API_KEY}"
  #   temperature: 0.1
  #   max_tokens: 4096
  #   timeout: 300

  # DeepSeek 配置示例
  # primary:
  #   provider: "deepseek"
  #   model: "deepseek-chat"
  #   api_key: "${DEEPSEEK_API_KEY}"
  #   base_url: "https://api.deepseek.com/v1"
  #   temperature: 0.1
  #   max_tokens: 4096
  #   timeout: 300

  # Google Gemini 配置示例
  # primary:
  #   provider: "gemini"
  #   model: "gemini-pro"
  #   api_key: "${GEMINI_API_KEY}"
  #   temperature: 0.1
  #   max_tokens: 4096
  #   timeout: 300

# 网络代理配置 - 必需（根据网络环境）
proxy:
  http_proxy: "${HTTP_PROXY:-http://127.0.0.1:8118/}"
  https_proxy: "${HTTPS_PROXY:-http://127.0.0.1:8118/}"
  no_proxy: "${NO_PROXY:-localhost,127.0.0.1}"

# ============================================================================
# 网络配置 - 智能缺省（可选，系统会使用默认网络配置）
# ============================================================================

# network:
#   # 网络请求重试配置
#   retry:
#     max_retries: 3                    # 最大重试次数（0-10，默认3）
#     initial_delay: 1.0                # 初始重试延迟（秒，0.1-10.0，默认1.0）
#     max_delay: 60.0                   # 最大重试延迟（秒，1.0-300.0，默认60.0）
#     backoff_factor: 2.0               # 指数退避因子（1.0-5.0，默认2.0）
#     jitter: true                      # 是否添加随机抖动（默认true）
#     retry_on_timeout: true            # 超时时是否重试（默认true）
#     retry_on_connection_error: true   # 连接错误时是否重试（默认true）
#     retry_on_http_error: false        # HTTP错误时是否重试（默认false）
#     retry_http_status_codes: [429, 502, 503, 504]  # 需要重试的HTTP状态码
#
#   # 基础网络配置
#   timeout: 30.0                       # 网络请求超时时间（秒，1.0-300.0，默认30.0）
#   verify_ssl: true                    # 是否验证SSL证书（默认true）
#   user_agent: "Mozilla/5.0 (compatible; TechDocBot/1.0)"  # HTTP请求User-Agent
#
#   # 重试策略说明：
#   # - 指数退避：每次重试延迟 = initial_delay * (backoff_factor ^ 重试次数)
#   # - 最大延迟限制：延迟不会超过 max_delay
#   # - 随机抖动：在计算延迟基础上添加 ±25% 的随机变化，避免雷群效应
#   # - 重试条件：可分别配置对不同类型错误的重试策略
#   # - 默认重试：超时错误、连接错误，不重试HTTP状态错误（除非明确配置）

# ============================================================================
# 数据库配置 - 智能缺省（可选，系统会自动创建 SQLite 数据库）
# ============================================================================

# database:
#   type: "sqlite"  # sqlite | postgresql
#   url: "${DATABASE_URL:-sqlite+aiosqlite:///./data/dev.db}"
#   echo: false
#   pool_size: 5
#   max_overflow: 10
#   pool_timeout: 30
#   pool_recycle: 3600

# ============================================================================
# 路径配置 - 系统文件和目录路径（可选，使用智能默认值）
# ============================================================================

# 项目根目录配置 - 所有相对路径的基准目录（可选，默认自动检测）
# project_root: "${PROJECT_ROOT:-/opt/app}"

# 数据目录配置 - 所有数据文件的根目录
data_directory: "${DATA_DIRECTORY:-./data}"

# 输出目录配置 - 生成的文档输出位置
output_directory: "${OUTPUT_DIRECTORY:-./data/output}"

# ============================================================================
# 缓存配置 - 智能缺省（可选，系统会自动创建文件缓存）
# ============================================================================

cache:
  # 文件系统缓存基础路径
  filesystem_base_path: "${CACHE_FILESYSTEM_PATH:-./data/cache}"

  # Sprint 2 简化版缓存配置
  # url_content_dir: "${CACHE_URL_CONTENT_DIR:-./data/cache/url_content}"
  # max_cache_age_days: 30
  # enable_content_update_check: false  # Sprint 2 暂时关闭
  #
  # 完整缓存配置（后续 Sprint 启用）
  # redis_enabled: false  # Sprint 2 不使用 Redis
  # redis_url: "${REDIS_URL:-redis://localhost:6379/0}"
  # redis_password: "${REDIS_PASSWORD:-}"
  # filesystem_enabled: true
  # filesystem_max_size_gb: 10
#
#   # 缓存策略
#   strategy:
#     # 原始文件缓存 TTL
#     raw_files:
#       web_pages: 86400      # 24小时
#       documents: 604800     # 7天
#       images: 2592000       # 30天
#
#     # LLM 响应缓存 TTL
#     llm_responses:
#       analysis: 3600        # 1小时
#       generation: 7200      # 2小时
#       review: 1800          # 30分钟
#
#     # 转换结果缓存 TTL
#     processed_content:
#       text_extraction: 86400    # 24小时
#       summarization: 43200      # 12小时
#       translation: 172800       # 48小时

# ============================================================================
# 多智能体系统配置 - 智能缺省（可选，系统会使用默认配置）
# ============================================================================

# agents:
#   # 全局智能体配置
#   global:
#     max_retries: 3
#     timeout: 300
#     parallel_limit: 10
#     checkpoint_enabled: true
#
#   # 具体智能体配置
#   requirement_analyst:
#     enabled: true
#     llm_model: "gpt-4o"
#     max_tokens: 2048
#     temperature: 0.1
#
#   information_retrieval:
#     enabled: true
#     llm_model: "gpt-4o"
#     max_tokens: 4096
#     temperature: 0.0
#     parallel_sources: 5
#     max_pages_per_source: 10
#
#   document_writer:
#     enabled: true
#     llm_model: "gpt-4o"
#     max_tokens: 8192
#     temperature: 0.2
#
#   quality_reviewer:
#     enabled: true
#     llm_model: "claude-3-5-sonnet-20241022"
#     max_tokens: 4096
#     temperature: 0.1

# ============================================================================
# 工作流配置 - 智能缺省（可选，系统会使用默认工作流配置）
# ============================================================================

# workflow:
#   max_concurrent_tasks: 10  # 最大并发任务数
#   default_task_timeout_seconds: 300  # 默认任务超时时间（秒）
#   default_retry_count: 3  # 默认重试次数
#   enable_checkpoints: true  # 启用检查点机制
#   checkpoint_interval_seconds: 60  # 检查点间隔（秒）

# ============================================================================
# 调度器配置 - 智能缺省（可选，系统会使用默认调度器配置）
# ============================================================================

# scheduler:
#   max_concurrent_tasks: 10  # 调度器最大并发任务数
#   task_queue_size: 1000  # 任务队列大小
#   heartbeat_interval_seconds: 30  # 心跳间隔（秒）
#   cleanup_interval_seconds: 300  # 清理间隔（秒）

# ============================================================================
# 信息检索配置 - 可选配置（系统会使用默认检索策略）
# ============================================================================

# retrieval:
#   # 搜索引擎配置
#   search_engines:
#     primary: "tavily"        # 首选搜索引擎：tavily 或 duckduckgo
#     fallback: "duckduckgo"   # 兜底搜索引擎：固定为 duckduckgo
#     tavily_api_key: "${TAVILY_API_KEY:-}"  # Tavily API 密钥，为空时自动使用 DuckDuckGo
#     max_results: 20          # 每个查询的最大结果数
#                              # Tavily: 硬性上限 20，推荐 15-20（充分利用 API 限制）
#                              # DuckDuckGo: 无硬性上限，推荐 10-30，建议不超过 50
#
#   # 调研配置 - 控制调研流程的各种数量限制
#   research:
#     max_search_results: 60        # 搜索结果最大数量（10-200，默认60）
#     max_source_list_size: 15      # 信息源清单最大数量（5-50，默认15）
#     max_concurrent_requests: 5    # 最大并发请求数（1-20，默认5）
#     search_query_limit: 5         # 搜索查询数量限制（1-10，默认5）
#     primary_keywords_limit: 3     # 主要关键词数量限制（1-5，默认3）
#     technical_terms_limit: 2      # 技术术语数量限制（1-5，默认2）
#     english_keywords_limit: 2     # 英文关键词数量限制（1-5，默认2）
#     information_summary_limit: 10 # 信息摘要处理结果数量限制（5-20，默认10）
#     # 说明：
#     # - max_source_list_size 控制从搜索结果中选择多少个高质量源进行深度处理
#     # - 这解决了之前硬编码15个源的问题，现在完全可配置
#     # - 增加数量会提高信息覆盖度，但也会增加处理时间和资源消耗
#     # - 建议根据调研深度需求和性能要求进行调整
#
#     # 传统搜索引擎配置（暂未集成）
#     google:
#       enabled: false
#       api_key: "${GOOGLE_SEARCH_API_KEY}"
#       search_engine_id: "${GOOGLE_SEARCH_ENGINE_ID}"
#       max_results: 10
#
#     bing:
#       enabled: false
#       api_key: "${BING_SEARCH_API_KEY}"
#       max_results: 10
#
#   # 学术数据库配置
#   academic:
#     arxiv:
#       enabled: true
#       max_results: 20
#       categories: ["cs.AI", "cs.CL", "cs.LG"]
#
#     pubmed:
#       enabled: false
#       api_key: "${PUBMED_API_KEY}"
#       max_results: 15
#
#   # 网页抓取配置
#   web_scraping:
#     user_agent: "TechDocWriter/1.0 (+https://example.com/bot)"
#     max_page_size_mb: 10
#     timeout: 30
#     respect_robots_txt: true
#     delay_between_requests: 1

# ============================================================================
# 日志配置 - 智能缺省（可选，系统会使用默认日志配置）
# ============================================================================

# logging:
#   level: "${LOG_LEVEL:-INFO}"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
#   format: "${LOG_FORMAT:-structured}"  # structured, simple
#   console_enabled: "${LOG_CONSOLE_ENABLED:-true}"
#   file_enabled: "${LOG_FILE_ENABLED:-true}"
#   file_path: "${LOG_FILE_PATH:-./log/app.log}"
#   file_max_size_mb: 100
#   file_backup_count: 5
#
#   # 组件日志级别
#   loggers:
#     "src.agents": "INFO"
#     "src.workflow": "INFO"
#     "src.cache": "WARNING"
#     "httpx": "WARNING"
#     "urllib3": "WARNING"
#     "litellm": "INFO"  # 控制 LiteLLM 日志级别
#     "openai": "WARNING"  # 控制 OpenAI 客户端日志
#     "anthropic": "WARNING"  # 控制 Anthropic 客户端日志
#
#   # LiteLLM 专用日志配置 - 符合 .augmentrules 要求
#   litellm:
#     enabled: true
#     level: "INFO"  # LiteLLM 交互日志级别
#     directory: "./log"  # 日志目录
#     filename_pattern: "little_llm.{date}.log"  # 文件名格式：little_llm.YYYYMMDD.log
#     max_size_mb: 50  # 单个文件最大大小
#     backup_count: 30  # 保留30天的日志文件
#     include_request_response: true  # 包含完整的请求和响应内容
#     include_metadata: true  # 包含元数据（模型、提供商、耗时等）
#     json_format: true  # 使用JSON格式便于解析

# ============================================================================
# 调试配置 - 智能缺省（可选，系统会使用默认调试配置）
# ============================================================================

# debug:
#   # 内存分配跟踪配置
#   tracemalloc:
#     enabled: false  # 缺省禁用，可通过环境变量 TRACEMALLOC_ENABLED 覆盖
#     nframe: 25      # 保存的调用帧数，更多帧提供更详细的回溯信息
#     # 注意：启用 tracemalloc 会有轻微的性能开销，建议仅在调试时使用
#     # 环境变量优先级：TRACEMALLOC_ENABLED > 配置文件 > 默认值(false)
#
#   # 异步调试配置
#   asyncio:
#     debug: false    # 启用 asyncio 调试模式，可通过环境变量 ASYNCIO_DEBUG 覆盖
#     log_slow_callbacks: true  # 记录慢回调
#     slow_callback_duration: 0.1  # 慢回调阈值（秒）

# ============================================================================
# 内容处理配置 - 智能缺省（可选，系统会使用默认配置）
# ============================================================================

# content_processing:
#   # Markdown 转换配置
#   markdown:
#     recursion_limit: 5000      # 递归限制，防止深度嵌套HTML导致栈溢出
#     max_html_depth: 100        # 最大HTML嵌套深度，超过此深度将进行预处理
#     enable_preprocessing: true  # 启用HTML预处理，简化复杂结构
#     fallback_strategies:       # 降级策略列表，按顺序尝试
#       - "preprocessing"         # 预处理HTML后重试
#       - "text_extraction"       # 仅提取文本内容
#     # 注意：
#     # - recursion_limit 建议设置为 3000-10000，过低可能导致转换失败
#     # - max_html_depth 建议设置为 50-200，过低可能过度简化内容
#     # - 启用预处理会轻微影响性能，但能显著提高转换成功率

# ============================================================================
# API 服务配置 - 可选配置（仅在需要 Web API 时配置）
# ============================================================================

# api:
#   # FastAPI 配置
#   host: "127.0.0.1"
#   port: 8000
#   debug: false
#   reload: false
#
#   # CORS 配置
#   cors:
#     allow_origins: ["http://localhost:3000", "http://127.0.0.1:3000"]
#     allow_credentials: true
#     allow_methods: ["GET", "POST", "PUT", "DELETE"]
#     allow_headers: ["*"]
#
#   # 认证配置（可选）
#   auth:
#     enabled: false
#     secret_key: "${API_SECRET_KEY}"
#     algorithm: "HS256"
#     access_token_expire_minutes: 30

# ============================================================================
# CLI 配置 - 智能缺省（可选，系统会使用默认 CLI 配置）
# ============================================================================

# cli:
#   # 默认输出格式
#   default_output_format: "markdown"  # markdown, html, pdf, docx
#
#   # 默认输出目录（符合 .augmentrules 规范）
#   output_directory: "./data/output"
#
#   # 进度显示
#   show_progress: true
#
#   # 详细输出
#   verbose: false

# ============================================================================
# 环境特定配置 - 自动检测（可选，系统会自动检测环境）
# ============================================================================

# environment:
#   # 当前环境（自动检测）
#   current: "${ENVIRONMENT:-development}"  # development, testing, production
#
#   # 环境特定覆盖
#   development:
#     logging:
#       level: "DEBUG"
#     api:
#       debug: true
#       reload: true
#     database:
#       echo: true
#
#   testing:
#     database:
#       url: "sqlite+aiosqlite:///:memory:"
#     cache:
#       redis:
#         enabled: false
#
#   production:
#     logging:
#       level: "WARNING"
#     api:
#       debug: false
#     cache:
#       filesystem:
#         max_size_gb: 50

# ============================================================================
# 性能和监控配置 - 可选配置（仅在生产环境需要）
# ============================================================================

# monitoring:
#   # 性能监控
#   performance:
#     enabled: false
#     metrics_endpoint: "/metrics"
#     collect_system_metrics: true
#
#   # 健康检查
#   health_check:
#     enabled: true
#     endpoint: "/health"
#     check_database: true
#     check_cache: true
#     check_llm: false  # 可能产生费用

# ============================================================================
# 安全配置 - 生产环境必需（开发环境可选）
# ============================================================================

# security:
#   # 数据加密
#   encryption:
#     enabled: false
#     key: "${ENCRYPTION_KEY}"
#
#   # 请求限制
#   rate_limiting:
#     enabled: false
#     requests_per_minute: 60
#     burst_size: 10
#
#   # 输入验证
#   input_validation:
#     max_request_size_mb: 50
#     allowed_file_types: [".txt", ".md", ".pdf", ".docx", ".html"]
#     sanitize_html: true
