"""
需求分析智能体核心类

基于LangGraph 0.6函数式API实现的需求分析智能体，
负责解析用户输入，生成结构化的调研需求。
"""

from typing import Dict, Any, List
from uuid import UUID
import structlog

from src.backend.agents.base import BaseAgent, AgentConfig
from src.backend.models.request import UserRequest, RequirementAnalysis
from src.backend.config import Config
from src.backend.llm.providers import LLMProviderFactory
from .parsers import IntentParser
from .generators import ObjectiveGenerator, ScopeGenerator, AudienceAnalyzer, ComplexityAssessor, ApproachRecommender

logger = structlog.get_logger(__name__)


class RequirementAnalystAgent(BaseAgent[UserRequest, RequirementAnalysis]):
    """
    需求分析智能体
    
    功能：
    1. 解析用户自然语言输入
    2. 提取调研目标和范围
    3. 生成搜索关键词
    4. 定义调研策略
    """
    
    def __init__(self, config: AgentConfig, system_config: Config):
        super().__init__(config, system_config)
        self.llm_config = system_config.llm_primary
        self.fallback_config = system_config.llm_fallback
        
        # 创建主要LLM提供者
        llm_config_dict = self.llm_config.model_dump()
        self.logger.debug(f"Creating primary LLM provider with config: {llm_config_dict}")
        self.llm_provider = LLMProviderFactory.create_provider(llm_config_dict)
        
        # 创建备用LLM提供者（如果配置了）
        self.fallback_provider = None
        if self.fallback_config:
            fallback_config_dict = self.fallback_config.model_dump()
            self.logger.debug(f"Creating fallback LLM provider with config: {fallback_config_dict}")
            self.fallback_provider = LLMProviderFactory.create_provider(fallback_config_dict)
        
        # 初始化组件
        self.intent_parser = IntentParser(self._call_llm_with_fallback)
        self.objective_generator = ObjectiveGenerator(self._call_llm_with_fallback)
        self.scope_generator = ScopeGenerator()
        self.audience_analyzer = AudienceAnalyzer(self._call_llm_with_fallback)
        self.complexity_assessor = ComplexityAssessor(self._call_llm_with_fallback)
        self.approach_recommender = ApproachRecommender(self._call_llm_with_fallback)
    
    async def _call_llm_with_fallback(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """调用LLM，支持备用提供者"""
        try:
            # 尝试主要提供者
            response = await self.llm_provider.generate_response(messages, **kwargs)
            return response
        except Exception as e:
            self.logger.warning(f"Primary LLM provider ({self.llm_provider.provider}) failed: {e}")
            
            # 如果有备用提供者，尝试使用
            if self.fallback_provider:
                try:
                    self.logger.info(f"Trying fallback LLM provider ({self.fallback_provider.provider})...")
                    response = await self.fallback_provider.generate_response(messages, **kwargs)
                    return response
                except Exception as fallback_error:
                    self.logger.error(f"Fallback LLM provider also failed: {fallback_error}")
            
            # 如果都失败了，重新抛出原始异常
            raise e
    
    async def _process(self, input_data: UserRequest, task_id: UUID) -> RequirementAnalysis:
        """
        核心需求分析处理逻辑
        
        Args:
            input_data: 用户请求
            task_id: 任务ID
            
        Returns:
            RequirementAnalysis: 需求分析结果
        """
        self.logger.info("Starting requirement analysis", user_request_id=str(input_data.id))
        
        try:
            # 1. 解析用户意图
            parsed_intent = await self.intent_parser.parse(input_data)
            
            # 2. 生成调研目标
            research_objectives = await self.objective_generator.generate(input_data, parsed_intent)
            
            # 3. 定义调研范围
            scope_definition = self.scope_generator.define(input_data, research_objectives)
            
            # 4. 分析目标受众
            target_audience = await self.audience_analyzer.analyze(input_data)
            
            # 5. 评估复杂度
            complexity = await self.complexity_assessor.assess(input_data, research_objectives)
            
            # 6. 推荐方法
            approach = await self.approach_recommender.recommend(input_data, complexity)
            
            # 构建分析结果
            analysis = RequirementAnalysis(
                user_request_id=str(input_data.id),
                parsed_topic=parsed_intent["topic"],
                research_objectives=research_objectives,
                scope_definition=scope_definition,
                target_audience=target_audience,
                output_format=input_data.format,
                estimated_complexity=complexity,
                recommended_approach=approach
            )
            
            self.logger.info(
                "Requirement analysis completed",
                user_request_id=str(input_data.id),
                objectives_count=len(research_objectives),
                complexity=complexity
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error(
                "Requirement analysis failed",
                user_request_id=str(input_data.id),
                error=str(e)
            )
            raise
