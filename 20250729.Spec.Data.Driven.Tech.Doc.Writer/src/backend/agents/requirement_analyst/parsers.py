"""
需求分析解析器模块

负责解析用户意图和提取关键信息。
"""

from typing import Dict, Any, List, Callable, Awaitable
import json
import structlog

from src.backend.models.request import UserRequest

logger = structlog.get_logger(__name__)


class IntentParser:
    """用户意图解析器"""
    
    def __init__(self, llm_caller: Callable[[List[Dict[str, str]], Any], Awaitable[str]]):
        self.llm_caller = llm_caller
    
    async def parse(self, request: UserRequest) -> Dict[str, Any]:
        """解析用户意图"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": """你是一个专业的需求分析专家。请分析用户的调研需求，提取关键信息并以JSON格式返回。

返回格式：
{
    "topic": "核心主题（简洁明确）",
    "technical_domains": ["技术领域1", "技术领域2"],
    "depth_requirement": "surface|moderate|comprehensive",
    "special_focus": ["特殊关注点1", "特殊关注点2"],
    "research_type": "技术调研|市场分析|产品分析|学术研究",
    "complexity_indicators": ["复杂度指标1", "复杂度指标2"]
}

请根据主题内容智能识别技术领域，根据范围描述判断深度要求。"""
                },
                {
                    "role": "user",
                    "content": f"""请分析以下用户调研需求：

主题：{request.topic}
范围：{request.scope}
格式：{request.format}
额外要求：{request.additional_requirements or "无"}

请提取关键信息并以JSON格式返回。"""
                }
            ]

            response = await self.llm_caller(messages, temperature=0.1, max_tokens=1000)

            # 尝试解析JSON响应
            try:
                # 提取JSON部分（可能包含在代码块中）
                if "```json" in response:
                    json_start = response.find("```json") + 7
                    json_end = response.find("```", json_start)
                    json_str = response[json_start:json_end].strip()
                elif "{" in response and "}" in response:
                    json_start = response.find("{")
                    json_end = response.rfind("}") + 1
                    json_str = response[json_start:json_end]
                else:
                    json_str = response

                parsed_intent = json.loads(json_str)

                # 验证必需字段
                if "topic" not in parsed_intent:
                    parsed_intent["topic"] = request.topic

                return parsed_intent

            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse LLM JSON response: {e}, using fallback")
                # 使用备用解析逻辑
                return self._fallback_parse(request)

        except Exception as e:
            logger.error(f"LLM intent parsing failed: {e}, using fallback")
            return self._fallback_parse(request)
    
    def _fallback_parse(self, request: UserRequest) -> Dict[str, Any]:
        """备用意图解析方法"""
        return {
            "topic": request.topic,
            "technical_domains": self._extract_technical_domains(request.topic),
            "depth_requirement": self._analyze_depth_requirement(request.scope),
            "special_focus": self._identify_special_focus(request.scope),
            "research_type": "技术调研",
            "complexity_indicators": []
        }
    
    def _extract_technical_domains(self, topic: str) -> List[str]:
        """提取技术领域"""
        domains = []
        topic_lower = topic.lower()
        
        domain_keywords = {
            "人工智能": ["ai", "人工智能", "机器学习", "深度学习"],
            "区块链": ["blockchain", "区块链", "加密货币", "智能合约"],
            "云计算": ["cloud", "云计算", "分布式", "微服务"],
            "物联网": ["iot", "物联网", "传感器", "边缘计算"],
            "大数据": ["big data", "大数据", "数据分析", "数据挖掘"]
        }
        
        for domain, keywords in domain_keywords.items():
            if any(keyword in topic_lower for keyword in keywords):
                domains.append(domain)
        
        return domains
    
    def _analyze_depth_requirement(self, scope: str) -> str:
        """分析深度要求"""
        scope_lower = scope.lower()
        
        if any(keyword in scope_lower for keyword in ["全面", "深入", "详细", "comprehensive"]):
            return "comprehensive"
        elif any(keyword in scope_lower for keyword in ["概述", "简要", "基础"]):
            return "surface"
        else:
            return "moderate"
    
    def _identify_special_focus(self, scope: str) -> List[str]:
        """识别特殊关注点"""
        focus_areas = []
        scope_lower = scope.lower()
        
        focus_keywords = {
            "安全性": ["安全", "隐私", "风险"],
            "性能": ["性能", "效率", "速度"],
            "成本": ["成本", "费用", "经济"],
            "可扩展性": ["扩展", "规模", "scalability"],
            "用户体验": ["用户", "体验", "界面", "交互"]
        }
        
        for focus, keywords in focus_keywords.items():
            if any(keyword in scope_lower for keyword in keywords):
                focus_areas.append(focus)
        
        return focus_areas
