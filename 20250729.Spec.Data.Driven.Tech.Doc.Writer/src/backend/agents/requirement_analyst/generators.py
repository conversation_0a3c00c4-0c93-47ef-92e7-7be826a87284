"""
需求分析生成器模块

负责生成调研目标、范围定义、受众分析等。
"""

from typing import Dict, Any, List, Callable, Awaitable
import structlog

from src.backend.models.request import UserRequest

logger = structlog.get_logger(__name__)


class ObjectiveGenerator:
    """调研目标生成器"""
    
    def __init__(self, llm_caller: Callable[[List[Dict[str, str]], Any], Awaitable[str]]):
        self.llm_caller = llm_caller
    
    async def generate(self, request: UserRequest, intent: Dict[str, Any]) -> List[str]:
        """生成调研目标"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": """你是一个专业的调研规划专家。请根据用户需求生成具体的调研目标。

要求：
1. 目标要具体、可执行
2. 根据主题特点个性化生成
3. 考虑技术领域和特殊关注点
4. 返回3-6个目标，每个目标一行
5. 不要使用固定模板，要根据具体主题灵活生成

格式：直接返回目标列表，每行一个目标，以"•"开头。"""
                },
                {
                    "role": "user",
                    "content": f"""请为以下调研需求生成具体的调研目标：

主题：{intent['topic']}
技术领域：{', '.join(intent.get('technical_domains', []))}
深度要求：{intent.get('depth_requirement', 'moderate')}
特殊关注点：{', '.join(intent.get('special_focus', []))}
调研类型：{intent.get('research_type', '技术调研')}

请生成3-6个具体的调研目标。"""
                }
            ]

            response = await self.llm_caller(messages, temperature=0.2, max_tokens=800)

            # 解析目标列表
            objectives = []
            for line in response.strip().split('\n'):
                line = line.strip()
                if line and (line.startswith('•') or line.startswith('-') or line.startswith('*')):
                    objective = line[1:].strip()
                    if objective:
                        objectives.append(objective)

            # 如果解析失败，使用备用方法
            if not objectives:
                return self._fallback_generate(request, intent)

            return objectives[:6]  # 限制目标数量

        except Exception as e:
            logger.warning(f"LLM objective generation failed: {e}, using fallback")
            return self._fallback_generate(request, intent)

    def _fallback_generate(self, request: UserRequest, intent: Dict[str, Any]) -> List[str]:
        """备用目标生成方法"""
        objectives = []
        topic = intent['topic']

        # 基于主题生成核心目标
        objectives.append(f"深入了解{topic}的技术现状和发展趋势")

        # 基于技术领域生成具体目标
        for domain in intent.get("technical_domains", []):
            objectives.append(f"分析{domain}在{topic}中的应用和挑战")

        # 基于深度要求生成目标
        if intent.get("depth_requirement") == "comprehensive":
            objectives.append(f"全面评估{topic}的市场前景和商业价值")
            objectives.append(f"识别{topic}领域的关键技术瓶颈和解决方案")

        # 基于特殊关注点生成目标
        for focus in intent.get("special_focus", []):
            objectives.append(f"重点关注{focus}相关的技术和应用")

        return objectives[:6]


class ScopeGenerator:
    """调研范围生成器"""
    
    def define(self, request: UserRequest, objectives: List[str]) -> str:
        """定义调研范围"""
        scope_elements = []
        
        # 基于目标定义范围
        scope_elements.append(f"技术调研：{request.topic}相关的核心技术和方法")
        scope_elements.append("市场分析：行业现状、竞争格局和发展趋势")
        scope_elements.append("应用案例：典型应用场景和成功案例分析")
        
        if "挑战" in request.scope or "问题" in request.scope:
            scope_elements.append("挑战识别：技术难点、限制因素和解决方案")
        
        if "前景" in request.scope or "未来" in request.scope:
            scope_elements.append("前景展望：未来发展方向和机遇分析")
        
        return "；".join(scope_elements)


class AudienceAnalyzer:
    """目标受众分析器"""
    
    def __init__(self, llm_caller: Callable[[List[Dict[str, str]], Any], Awaitable[str]]):
        self.llm_caller = llm_caller
    
    async def analyze(self, request: UserRequest) -> str:
        """分析目标受众"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": """你是一个专业的受众分析专家。请根据调研主题和格式要求，分析目标受众群体。

要求：
1. 根据主题特点识别最相关的受众群体
2. 考虑输出格式的专业程度
3. 返回简洁明确的受众描述
4. 不超过20个字"""
                },
                {
                    "role": "user",
                    "content": f"""请分析以下调研的目标受众：

主题：{request.topic}
输出格式：{request.format}
调研范围：{request.scope}

请返回目标受众群体描述。"""
                }
            ]

            response = await self.llm_caller(messages, temperature=0.1, max_tokens=200)
            return response.strip()

        except Exception as e:
            logger.warning(f"LLM audience analysis failed: {e}, using fallback")
            # 备用逻辑
            if "技术" in request.format.lower():
                return "技术专业人员、研发工程师、技术决策者"
            elif "商业" in request.format.lower() or "业务" in request.format.lower():
                return "业务决策者、产品经理、投资人员"
            else:
                return "技术和业务相关人员"


class ComplexityAssessor:
    """复杂度评估器"""
    
    def __init__(self, llm_caller: Callable[[List[Dict[str, str]], Any], Awaitable[str]]):
        self.llm_caller = llm_caller
    
    async def assess(self, request: UserRequest, objectives: List[str]) -> str:
        """评估复杂度"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": """你是一个专业的项目复杂度评估专家。请根据调研主题、范围和目标，评估调研复杂度。

评估标准：
- 低：主题相对简单，技术成熟，资料丰富，目标明确
- 中：主题有一定技术深度，需要多方面分析，资料需要筛选整理
- 高：主题技术前沿，涉及多个复杂领域，需要深度分析和专业判断

请只返回：低、中、高 其中之一。"""
                },
                {
                    "role": "user",
                    "content": f"""请评估以下调研的复杂度：

主题：{request.topic}
调研范围：{request.scope}
调研目标：
{chr(10).join(f"• {obj}" for obj in objectives)}

请返回复杂度评估：低、中、高"""
                }
            ]

            response = await self.llm_caller(messages, temperature=0.1, max_tokens=100)

            # 提取复杂度等级
            response_clean = response.strip().lower()
            if "低" in response_clean:
                return "低"
            elif "高" in response_clean:
                return "高"
            elif "中" in response_clean:
                return "中"
            else:
                # 备用逻辑
                return self._fallback_assess(request, objectives)

        except Exception as e:
            logger.warning(f"LLM complexity assessment failed: {e}, using fallback")
            return self._fallback_assess(request, objectives)

    def _fallback_assess(self, request: UserRequest, objectives: List[str]) -> str:
        """备用复杂度评估方法"""
        complexity_score = 0

        # 基于主题复杂度
        if any(keyword in request.topic.lower() for keyword in ["ai", "机器学习", "深度学习", "区块链"]):
            complexity_score += 2

        # 基于范围复杂度
        if len(request.scope) > 100:
            complexity_score += 1

        # 基于目标数量
        complexity_score += len(objectives) // 2

        if complexity_score <= 2:
            return "低"
        elif complexity_score <= 4:
            return "中"
        else:
            return "高"


class ApproachRecommender:
    """方法推荐器"""
    
    def __init__(self, llm_caller: Callable[[List[Dict[str, str]], Any], Awaitable[str]]):
        self.llm_caller = llm_caller
    
    async def recommend(self, request: UserRequest, complexity: str) -> str:
        """推荐调研方法"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": """你是一个专业的调研方法专家。请根据调研主题、复杂度和要求，推荐最适合的调研方法。

要求：
1. 根据主题特点选择合适的调研方法
2. 考虑复杂度等级调整方法深度
3. 返回3-4个具体的方法建议
4. 方法要实用可执行
5. 用逗号分隔，不超过30字"""
                },
                {
                    "role": "user",
                    "content": f"""请为以下调研推荐合适的方法：

主题：{request.topic}
复杂度：{complexity}
输出格式：{request.format}
调研范围：{request.scope}

请推荐3-4个调研方法，用逗号分隔。"""
                }
            ]

            response = await self.llm_caller(messages, temperature=0.2, max_tokens=300)
            return response.strip()

        except Exception as e:
            logger.warning(f"LLM approach recommendation failed: {e}, using fallback")
            # 备用逻辑
            approaches = []

            if complexity == "高":
                approaches.append("分阶段深入调研")
                approaches.append("多源信息交叉验证")
            else:
                approaches.append("并行信息收集")

            approaches.append("结构化文档编写")
            approaches.append("专家观点整合")

            return "，".join(approaches)
