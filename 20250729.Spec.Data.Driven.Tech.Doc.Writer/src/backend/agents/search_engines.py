"""
搜索引擎服务模块

实现双搜索引擎架构（Tavily + DuckDuckGo），支持智能降级和结果标准化。
遵循 .augmentrules 的代码质量规范和 Sprint 2 技术要求。
"""

from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod
from enum import Enum
import asyncio
import structlog
from datetime import datetime
import hashlib
import json

from langchain_community.tools import TavilySearchResults
try:
    from ddgs import DDGS
    DDGS_AVAILABLE = True
except ImportError:
    DDGS_AVAILABLE = False
    # Fallback to LangChain implementation if ddgs is not available
    try:
        from langchain_community.tools import DuckDuckGoSearchResults
        LANGCHAIN_DDG_AVAILABLE = True
    except ImportError:
        LANGCHAIN_DDG_AVAILABLE = False
from tenacity import retry, stop_after_attempt, wait_exponential
import httpx
import os

from src.backend.config import Config

logger = structlog.get_logger(__name__)


class SearchEngineType(Enum):
    """搜索引擎类型枚举"""
    TAVILY = "tavily"
    DUCKDUCKGO = "duckduckgo"


class SearchResult:
    """标准化搜索结果模型"""
    
    def __init__(
        self,
        title: str,
        content: str,
        url: str,
        source: str,
        relevance_score: float = 0.5,
        publication_date: Optional[str] = None,
        authors: Optional[List[str]] = None,
        tags: Optional[List[str]] = None
    ):
        self.title = title
        self.content = content
        self.url = url
        self.source = source
        self.relevance_score = relevance_score
        self.publication_date = publication_date
        self.authors = authors or []
        self.tags = tags or []
        self.timestamp = datetime.now().isoformat()
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "title": self.title,
            "content": self.content,
            "url": self.url,
            "source": self.source,
            "relevance_score": self.relevance_score,
            "publication_date": self.publication_date,
            "authors": self.authors,
            "tags": self.tags,
            "timestamp": self.timestamp
        }
    
    def get_url_hash(self) -> str:
        """获取URL的哈希值，用于去重"""
        return hashlib.md5(self.url.encode()).hexdigest()


class BaseSearchEngine(ABC):
    """搜索引擎基类"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = structlog.get_logger(self.__class__.__name__)
        
    @abstractmethod
    async def search(self, query: str, max_results: int = 10) -> List[SearchResult]:
        """执行搜索查询"""
        pass
    
    @abstractmethod
    def get_engine_type(self) -> SearchEngineType:
        """获取搜索引擎类型"""
        pass
    
    def _standardize_result(self, raw_result: Dict[str, Any], source: str) -> SearchResult:
        """标准化单个搜索结果"""
        # 根据不同搜索引擎处理字段映射
        if source == "duckduckgo":
            # DuckDuckGo 使用 href 和 body 字段
            title = raw_result.get("title", "")
            content = raw_result.get("body", raw_result.get("content", ""))
            url = raw_result.get("href", raw_result.get("url", ""))
        else:
            # Tavily 和其他搜索引擎使用标准字段
            title = raw_result.get("title", "")
            content = raw_result.get("content", "")
            url = raw_result.get("url", "")

        return SearchResult(
            title=title,
            content=content,
            url=url,
            source=source,
            relevance_score=raw_result.get("relevance_score", 0.5),
            publication_date=raw_result.get("publication_date"),
            authors=raw_result.get("authors", []),
            tags=raw_result.get("tags", [])
        )


class TavilySearchEngine(BaseSearchEngine):
    """Tavily 搜索引擎实现"""

    def __init__(self, config: Config):
        super().__init__(config)
        # 从配置中获取 Tavily API 密钥
        if hasattr(config, 'retrieval') and hasattr(config.retrieval, 'search_engines'):
            self.api_key = getattr(config.retrieval.search_engines, 'tavily_api_key', None)
        else:
            self.api_key = None
        self.search_tool = None
        self.proxy_config = self._get_proxy_config()

    def _get_proxy_config(self) -> Optional[Dict[str, str]]:
        """获取代理配置"""
        proxy_config = {}

        # 从配置对象获取代理设置
        if hasattr(self.config, 'proxy'):
            if self.config.proxy.http_proxy:
                proxy_config["http://"] = self.config.proxy.http_proxy
            if self.config.proxy.https_proxy:
                proxy_config["https://"] = self.config.proxy.https_proxy

        # 从环境变量获取代理设置（优先级更高）
        if os.getenv("HTTP_PROXY"):
            proxy_config["http://"] = os.getenv("HTTP_PROXY")
        if os.getenv("HTTPS_PROXY"):
            proxy_config["https://"] = os.getenv("HTTPS_PROXY")

        return proxy_config if proxy_config else None

    async def initialize(self) -> None:
        """初始化 Tavily 搜索工具"""
        if not self.api_key:
            raise ValueError("Tavily API key is required")

        try:
            # 设置代理环境变量（如果配置了代理）
            if self.proxy_config:
                self.logger.info("Configuring proxy for Tavily search", proxy_config=self.proxy_config)

            self.search_tool = TavilySearchResults(
                tavily_api_key=self.api_key,
                max_results=10,
                search_depth="advanced",
                include_answer=False,
                include_raw_content=True
            )
            self.logger.info("Tavily search engine initialized successfully")
        except Exception as e:
            self.logger.error("Failed to initialize Tavily search engine", error=str(e))
            raise
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def search(self, query: str, max_results: int = 10) -> List[SearchResult]:
        """执行 Tavily 搜索"""
        if not self.search_tool:
            await self.initialize()

        try:
            self.logger.info("Starting Tavily search", query=query, max_results=max_results)

            # 验证查询参数
            if not query or not query.strip():
                raise ValueError("Search query cannot be empty")

            # 执行搜索
            raw_results = await self.search_tool.ainvoke({"query": query.strip()})

            # 验证返回结果
            if not isinstance(raw_results, list):
                self.logger.warning("Unexpected result format from Tavily", result_type=type(raw_results))
                raw_results = []

            # 标准化结果
            standardized_results = []
            for raw_result in raw_results:
                try:
                    if isinstance(raw_result, dict):
                        result = self._standardize_result(raw_result, "tavily")
                        standardized_results.append(result)
                    else:
                        self.logger.warning("Skipping invalid result format", result=raw_result)
                except Exception as result_error:
                    self.logger.warning("Failed to standardize result", error=str(result_error))
                    continue

            # 限制结果数量
            limited_results = standardized_results[:max_results]

            self.logger.info(
                "Tavily search completed",
                query=query,
                result_count=len(limited_results),
                raw_count=len(raw_results)
            )

            return limited_results

        except Exception as e:
            self.logger.error("Tavily search failed", query=query, error=str(e), error_type=type(e).__name__)
            raise
    
    def get_engine_type(self) -> SearchEngineType:
        return SearchEngineType.TAVILY


class DuckDuckGoSearchEngine(BaseSearchEngine):
    """DuckDuckGo 搜索引擎实现"""

    def __init__(self, config: Config):
        super().__init__(config)
        self.search_tool = None
        self.use_ddgs = DDGS_AVAILABLE

    async def initialize(self) -> None:
        """初始化 DuckDuckGo 搜索工具"""
        try:
            if self.use_ddgs and DDGS_AVAILABLE:
                # 使用新的 ddgs 包
                self.search_tool = DDGS()
                self.logger.info("DuckDuckGo search engine initialized successfully with ddgs package")
            elif LANGCHAIN_DDG_AVAILABLE:
                # 降级到 LangChain 实现
                from langchain_community.tools import DuckDuckGoSearchResults
                self.search_tool = DuckDuckGoSearchResults(
                    output_format="list",
                    max_results=10
                )
                self.use_ddgs = False
                self.logger.info("DuckDuckGo search engine initialized with LangChain fallback")
            else:
                raise ImportError("Neither ddgs nor langchain DuckDuckGo implementation is available")
        except Exception as e:
            self.logger.error("Failed to initialize DuckDuckGo search engine", error=str(e))
            raise
    
    async def search(self, query: str, max_results: int = 10) -> List[SearchResult]:
        """执行 DuckDuckGo 搜索"""
        if not self.search_tool:
            await self.initialize()

        try:
            self.logger.info("Starting DuckDuckGo search", query=query, max_results=max_results)

            # 验证查询参数
            if not query or not query.strip():
                raise ValueError("Search query cannot be empty")

            # 执行搜索
            if self.use_ddgs and DDGS_AVAILABLE:
                # 使用新的 ddgs 包
                raw_results = list(self.search_tool.text(query.strip(), max_results=max_results))
            else:
                # 使用 LangChain 实现
                raw_results = self.search_tool.invoke(query.strip())

            # 处理结果格式
            if isinstance(raw_results, str):
                # 如果返回字符串，尝试解析为JSON
                try:
                    raw_results = json.loads(raw_results)
                except json.JSONDecodeError:
                    # 如果不是JSON，创建简单结果
                    self.logger.info("DuckDuckGo returned text result, converting to structured format")
                    raw_results = [{
                        "title": "Search Result",
                        "content": raw_results[:500] + "..." if len(raw_results) > 500 else raw_results,
                        "url": "",
                        "snippet": raw_results[:200] + "..." if len(raw_results) > 200 else raw_results
                    }]

            # 确保结果是列表格式
            if not isinstance(raw_results, list):
                self.logger.warning("Unexpected result format from DuckDuckGo", result_type=type(raw_results))
                raw_results = []

            # 标准化结果
            standardized_results = []
            for raw_result in raw_results:
                try:
                    if isinstance(raw_result, dict):
                        result = self._standardize_result(raw_result, "duckduckgo")
                        standardized_results.append(result)
                    else:
                        self.logger.warning("Skipping invalid result format", result=raw_result)
                except Exception as result_error:
                    self.logger.warning("Failed to standardize result", error=str(result_error))
                    continue

            # 限制结果数量
            limited_results = standardized_results[:max_results]

            self.logger.info(
                "DuckDuckGo search completed",
                query=query,
                result_count=len(limited_results),
                raw_count=len(raw_results) if isinstance(raw_results, list) else 1
            )

            return limited_results

        except Exception as e:
            self.logger.error("DuckDuckGo search failed", query=query, error=str(e), error_type=type(e).__name__)
            raise
    
    def get_engine_type(self) -> SearchEngineType:
        return SearchEngineType.DUCKDUCKGO


class SearchEngineSelector:
    """搜索引擎选择器 - 实现智能选择和降级逻辑"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = structlog.get_logger(__name__)
        
        # 初始化搜索引擎
        self.tavily_engine = TavilySearchEngine(config)
        self.duckduckgo_engine = DuckDuckGoSearchEngine(config)
        
        # 配置主要和备用搜索引擎
        # 从配置中获取搜索引擎设置，使用默认值作为兜底
        retrieval_config = getattr(config, 'retrieval', None)
        if retrieval_config and hasattr(retrieval_config, 'search_engines'):
            search_engines_config = retrieval_config.search_engines
            self.primary_engine = getattr(search_engines_config, 'primary', "tavily")
            self.fallback_engine = getattr(search_engines_config, 'fallback', "duckduckgo")
        else:
            # 使用默认配置
            self.primary_engine = "tavily"
            self.fallback_engine = "duckduckgo"
        
    async def search(self, query: str, max_results: int = 10) -> List[SearchResult]:
        """执行搜索，支持自动降级"""
        try:
            # 尝试使用主要搜索引擎
            if self.primary_engine == "tavily":
                return await self.tavily_engine.search(query, max_results)
            else:
                return await self.duckduckgo_engine.search(query, max_results)
                
        except Exception as primary_error:
            self.logger.warning(
                "Primary search engine failed, falling back",
                primary_engine=self.primary_engine,
                error=str(primary_error)
            )
            
            try:
                # 降级到备用搜索引擎
                if self.fallback_engine == "duckduckgo":
                    return await self.duckduckgo_engine.search(query, max_results)
                else:
                    return await self.tavily_engine.search(query, max_results)
                    
            except Exception as fallback_error:
                self.logger.error(
                    "Both search engines failed",
                    primary_error=str(primary_error),
                    fallback_error=str(fallback_error)
                )
                raise Exception(f"All search engines failed: {primary_error}, {fallback_error}")


class URLCache:
    """URL 缓存管理器 - 实现去重和缓存功能"""

    def __init__(self):
        self.url_cache = set()
        self.result_cache = {}
        self.logger = structlog.get_logger(__name__)

    def is_duplicate(self, url: str) -> bool:
        """检查URL是否重复"""
        url_hash = hashlib.md5(url.encode()).hexdigest()
        return url_hash in self.url_cache

    def add_url(self, url: str) -> None:
        """添加URL到缓存"""
        url_hash = hashlib.md5(url.encode()).hexdigest()
        self.url_cache.add(url_hash)

    def deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """去重搜索结果"""
        deduplicated = []
        seen_urls = set()

        for result in results:
            url_hash = result.get_url_hash()
            if url_hash not in seen_urls:
                seen_urls.add(url_hash)
                deduplicated.append(result)
                self.add_url(result.url)

        self.logger.info(
            "Results deduplicated",
            original_count=len(results),
            deduplicated_count=len(deduplicated)
        )

        return deduplicated


class ResultStandardizer:
    """结果标准化器 - 统一不同搜索引擎的返回格式"""

    def __init__(self):
        self.logger = structlog.get_logger(__name__)

    def standardize_results(self, results: List[SearchResult]) -> List[Dict[str, Any]]:
        """标准化搜索结果格式"""
        standardized = []

        for result in results:
            # 计算质量评分
            quality_score = self._calculate_quality_score(result)

            standardized_result = {
                "title": self._clean_text(result.title),
                "content": self._clean_text(result.content),
                "url": result.url,
                "source": result.source,
                "relevance_score": result.relevance_score,
                "quality_score": quality_score,
                "publication_date": result.publication_date,
                "authors": result.authors,
                "tags": result.tags,
                "timestamp": result.timestamp,
                "apa_citation": self._generate_apa_citation(result),
                "content_summary": self._generate_content_summary(result.content)
            }
            standardized.append(standardized_result)

        return standardized

    def _calculate_quality_score(self, result: SearchResult) -> float:
        """计算内容质量评分 (0.0 - 1.0)"""
        score = 0.0

        # 标题质量 (0.2权重)
        if result.title and len(result.title.strip()) > 10:
            score += 0.2
        elif result.title and len(result.title.strip()) > 5:
            score += 0.1

        # 内容质量 (0.3权重)
        if result.content:
            content_length = len(result.content.strip())
            if content_length > 200:
                score += 0.3
            elif content_length > 100:
                score += 0.25
            elif content_length > 50:
                score += 0.15
            elif content_length > 10:
                score += 0.1

        # URL有效性 (0.1权重)
        if result.url and result.url.startswith(('http://', 'https://')):
            score += 0.1

        # 作者信息 (0.05权重)
        if result.authors and len(result.authors) > 0:
            score += 0.05

        # 发布日期 (0.05权重)
        if result.publication_date:
            score += 0.05

        # 内容相关性检查 (0.3权重) - 新增
        relevance_bonus = self._calculate_content_relevance(result)
        score += relevance_bonus * 0.3

        return min(score, 1.0)

    def _calculate_content_relevance(self, result: SearchResult) -> float:
        """计算内容相关性评分 (0.0 - 1.0)"""
        # 定义相关性关键词
        cybersecurity_keywords = [
            'cisa', 'cybersecurity', 'infrastructure', 'security', 'cyber',
            'threat', 'vulnerability', 'incident', 'response', 'defense',
            'homeland', 'dhs', 'government', 'federal', 'critical',
            '网络安全', '基础设施', '安全', '威胁', '漏洞', '事件', '响应',
            '国土安全', '联邦', '关键', '政府'
        ]

        # 不相关内容的负面关键词
        irrelevant_keywords = [
            'crédit', 'credit', 'rachat', 'courtier', 'emprunt', 'finance',
            'banque', 'prêt', 'loan', 'mortgage', 'refinance', 'debt'
        ]

        text_to_check = (result.title + ' ' + result.content + ' ' + result.url).lower()

        # 检查负面关键词（如果包含，直接返回0）
        for keyword in irrelevant_keywords:
            if keyword in text_to_check:
                return 0.0

        # 计算相关关键词匹配度
        matches = 0
        for keyword in cybersecurity_keywords:
            if keyword in text_to_check:
                matches += 1

        # 根据匹配数量计算相关性评分
        if matches >= 3:
            return 1.0
        elif matches >= 2:
            return 0.8
        elif matches >= 1:
            return 0.6
        else:
            return 0.2  # 给予基础分数，避免完全过滤

    def _generate_content_summary(self, content: str) -> str:
        """生成内容摘要"""
        if not content:
            return ""

        # 简单的摘要生成：取前200个字符
        cleaned_content = self._clean_text(content)
        if len(cleaned_content) <= 200:
            return cleaned_content

        # 尝试在句号处截断
        summary = cleaned_content[:200]
        last_period = summary.rfind('.')
        if last_period > 100:  # 确保摘要不会太短
            summary = summary[:last_period + 1]
        else:
            summary = summary + "..."

        return summary

    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""

        # 移除多余的空白字符
        cleaned = " ".join(text.split())

        # 限制长度
        if len(cleaned) > 1000:
            cleaned = cleaned[:997] + "..."

        return cleaned

    def _generate_apa_citation(self, result: SearchResult) -> str:
        """生成 APA 第七版引用格式"""
        try:
            # APA 第七版网页引用格式：
            # 作者, A. A. (年份, 月 日). 标题. 网站名. URL

            # 处理作者
            if result.authors and len(result.authors) > 0:
                if len(result.authors) == 1:
                    authors = result.authors[0]
                elif len(result.authors) <= 20:
                    authors = ", ".join(result.authors[:-1]) + ", & " + result.authors[-1]
                else:
                    # 超过20个作者时，列出前19个，然后是省略号，最后是最后一个作者
                    authors = ", ".join(result.authors[:19]) + ", ... " + result.authors[-1]
            else:
                authors = "Unknown Author"

            # 处理日期
            if result.publication_date:
                # 尝试解析日期格式
                date_str = result.publication_date
                if len(date_str) >= 4 and date_str[:4].isdigit():
                    date = f"({date_str[:4]})"
                else:
                    date = f"({date_str})"
            else:
                date = "(n.d.)"

            # 处理标题
            title = result.title.strip() if result.title else "Untitled"
            # APA格式中，网页标题使用斜体（这里用下划线表示）
            if not title.endswith('.'):
                title += "."

            # 处理网站名（从URL推断）
            website_name = self._extract_website_name(result.url)

            # 构建引用
            if website_name:
                citation = f"{authors} {date} {title} {website_name}. {result.url}"
            else:
                citation = f"{authors} {date} {title} Retrieved from {result.url}"

            return citation

        except Exception as e:
            self.logger.warning("Failed to generate APA citation", error=str(e))
            return f"Retrieved from {result.url}"

    def _extract_website_name(self, url: str) -> str:
        """从URL提取网站名称"""
        if not url:
            return ""

        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            domain = parsed.netloc.lower()

            # 移除 www. 前缀
            if domain.startswith('www.'):
                domain = domain[4:]

            # 提取主域名
            domain_parts = domain.split('.')
            if len(domain_parts) >= 2:
                # 取倒数第二个部分作为网站名
                website_name = domain_parts[-2].capitalize()
                return website_name

            return domain.capitalize()

        except Exception:
            return ""


class SearchEngineService:
    """搜索引擎服务 - 整合所有组件的主要服务类"""

    def __init__(self, config: Config):
        self.config = config
        self.logger = structlog.get_logger(__name__)

        # 初始化组件
        self.selector = SearchEngineSelector(config)
        self.cache = URLCache()
        self.standardizer = ResultStandardizer()

    async def search(
        self,
        query: str,
        max_results: int = 10,
        enable_deduplication: bool = True
    ) -> Dict[str, Any]:
        """
        执行搜索并返回标准化结果

        Args:
            query: 搜索查询
            max_results: 最大结果数量
            enable_deduplication: 是否启用去重

        Returns:
            包含搜索结果和元数据的字典
        """
        try:
            self.logger.info("Starting search", query=query, max_results=max_results)

            # 执行搜索
            search_results = await self.selector.search(query, max_results)

            # 去重处理
            if enable_deduplication:
                search_results = self.cache.deduplicate_results(search_results)

            # 标准化结果
            standardized_results = self.standardizer.standardize_results(search_results)

            # 构建返回结果
            result = {
                "query": query,
                "results": standardized_results,
                "metadata": {
                    "total_count": len(standardized_results),
                    "search_timestamp": datetime.now().isoformat(),
                    "deduplication_enabled": enable_deduplication,
                    "primary_engine": self.selector.primary_engine,
                    "fallback_engine": self.selector.fallback_engine
                }
            }

            self.logger.info(
                "Search completed successfully",
                query=query,
                result_count=len(standardized_results)
            )

            return result

        except Exception as e:
            self.logger.error("Search failed", query=query, error=str(e))
            raise
