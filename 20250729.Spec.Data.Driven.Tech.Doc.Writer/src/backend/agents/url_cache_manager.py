"""
URL 缓存管理器 (重构版本 v2.0)

实现 URL 状态缓存，避免重复抓取相同内容。
支持内存缓存和文件持久化，确保数据一致性。

重构版本特性：
- 使用 MD5 作为缓存键
- 支持新的文件存储结构
- 实现 URL 标准化
- 保持向后兼容的 API
- 模块化设计，遵循300行代码限制
"""

from typing import Dict, Any, Set, Optional
import asyncio
import structlog
from datetime import datetime
from pathlib import Path
from threading import Lock

from src.backend.utils.url_utils import generate_url_hash, generate_file_paths
from src.backend.utils.cache_integrity import verify_file_integrity, cleanup_corrupted_files
from src.backend.utils.cache_persistence import (
    load_url_status_cache, save_cache_data, load_cache_metadata
)
from src.backend.utils.cache_stats import calculate_cache_stats, clear_all_cache_data
from src.backend.utils.cache_compat import CacheCompatibilityMixin
from src.backend.utils.project_paths import get_path_manager, resolve_config_path

logger = structlog.get_logger(__name__)


class URLCacheManager(CacheCompatibilityMixin):
    """
    URL 缓存管理器 (重构版本)
    
    新功能：
    1. 使用 MD5 作为缓存键，支持 URL 标准化
    2. 新的缓存结构：url_metadata[md5_hash] = {original_url, raw_content_path, markdown_path, ...}
    3. 文件路径管理：data/raw_content/ 和 data/markdown_content/
    4. 向后兼容的 API
    5. 原子写入，确保数据一致性
    """
    
    def __init__(self, cache_dir: Optional[Path] = None, config=None):
        self.logger = structlog.get_logger(self.__class__.__name__)

        # 获取路径管理器
        self.path_manager = get_path_manager(config)

        # 设置缓存目录 - 基于配置的绝对路径
        if cache_dir is None:
            # 使用配置中的缓存路径，并添加url_cache子目录
            base_cache_dir = self.path_manager.get_cache_dir()
            self.cache_dir = base_cache_dir / "url_cache"
        else:
            # 如果提供了自定义路径，确保它是绝对路径
            self.cache_dir = resolve_config_path(str(cache_dir), self.path_manager.project_root)
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # 设置内容存储目录 - 基于配置的绝对路径
        data_dir = self.path_manager.get_data_dir()
        self.raw_content_dir = data_dir / "raw_content"
        self.markdown_content_dir = data_dir / "markdown_content"
        self.raw_content_dir.mkdir(parents=True, exist_ok=True)
        self.markdown_content_dir.mkdir(parents=True, exist_ok=True)
        
        # 缓存文件路径
        self.url_status_file = self.cache_dir / "url_status.json"
        self.cache_metadata_file = self.cache_dir / "cache_metadata.json"
        
        # 内存缓存 - 新结构
        self.processed_url_hashes: Set[str] = set()
        self.url_metadata: Dict[str, Dict[str, Any]] = {}
        
        # 缓存统计
        self.stats = {
            "total_requests": 0,           # 总处理请求数
            "successful_requests": 0,      # 成功处理请求数
            "failed_requests": 0,          # 失败处理请求数
            "unique_urls_processed": 0,    # 唯一URL处理数
            "total_checks": 0,             # 缓存检查次数
            "cache_hits": 0,               # 缓存命中次数
            "cache_misses": 0,             # 缓存未命中次数
            "last_updated": None
        }
        
        # 线程锁，确保原子操作
        self._lock = Lock()
        self._save_lock = asyncio.Lock()  # 异步锁，防止并发保存
        self._initialized = False
    
    async def initialize(self) -> None:
        """初始化缓存管理器，加载持久化数据"""
        if self._initialized:
            return
        
        try:
            self.logger.info("Initializing URL cache manager", cache_dir=str(self.cache_dir))
            
            # 加载URL状态缓存
            self.processed_url_hashes, self.url_metadata = await load_url_status_cache(self.url_status_file)
            
            # 加载缓存元数据
            await load_cache_metadata(self.cache_metadata_file, self.stats)
            
            self._initialized = True
            self.logger.info(
                "URL cache manager initialized",
                cached_url_hashes_count=len(self.processed_url_hashes),
                cache_dir=str(self.cache_dir),
                raw_content_dir=str(self.raw_content_dir),
                markdown_content_dir=str(self.markdown_content_dir)
            )
            
        except Exception as e:
            self.logger.error("Failed to initialize URL cache manager", error=str(e))
            raise
    
    async def is_url_processed(self, url: str) -> bool:
        """检查URL是否已经处理过（向后兼容API）"""
        if not self._initialized:
            await self.initialize()
        
        with self._lock:
            # 统计检查次数（用于缓存命中率计算）
            self.stats["total_checks"] += 1
            url_hash = generate_url_hash(url)
            is_cached = url_hash in self.processed_url_hashes

            if is_cached:
                self.stats["cache_hits"] += 1
                self.logger.debug("URL cache hit", url=url, url_hash=url_hash)
            else:
                self.stats["cache_misses"] += 1
                self.logger.debug("URL cache miss", url=url, url_hash=url_hash)

            return is_cached
    
    async def mark_url_processed(
        self, 
        url: str, 
        success: bool, 
        raw_content_path: Optional[str] = None,
        markdown_path: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """标记URL为已处理（新版本，支持文件路径）"""
        if not self._initialized:
            await self.initialize()
        
        with self._lock:
            url_hash = generate_url_hash(url)

            # 统计处理请求
            self.stats["total_requests"] += 1

            # 统计成功/失败
            if success:
                self.stats["successful_requests"] += 1
            else:
                self.stats["failed_requests"] += 1

            # 如果是新URL，添加到缓存
            if url_hash not in self.processed_url_hashes:
                self.processed_url_hashes.add(url_hash)
                self.stats["unique_urls_processed"] += 1
            
            # 更新元数据
            self.url_metadata[url_hash] = {
                "original_url": url,
                "raw_content_path": raw_content_path,
                "markdown_path": markdown_path,
                "success": success,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            
            self.stats["last_updated"] = datetime.now().isoformat()
            
            self.logger.debug(
                "URL marked as processed", 
                url=url, 
                url_hash=url_hash,
                success=success,
                raw_content_path=raw_content_path,
                markdown_path=markdown_path
            )
        
        # 异步保存到文件
        await self._save_cache()
    
    async def mark_url_processed_legacy(self, url: str, success: bool, metadata: Optional[Dict[str, Any]] = None) -> None:
        """标记URL为已处理（向后兼容方法）"""
        await self.mark_url_processed(url, success, None, None, metadata)
    
    async def _save_cache(self) -> None:
        """保存缓存到文件（原子写入）"""
        # 使用异步锁防止并发保存
        async with self._save_lock:
            # 使用线程锁确保数据一致性
            with self._lock:
                # 创建数据副本以避免在保存过程中数据被修改
                processed_url_hashes_copy = self.processed_url_hashes.copy()
                url_metadata_copy = self.url_metadata.copy()
                stats_copy = self.stats.copy()

            # 在锁外进行实际的文件I/O操作
            try:
                await save_cache_data(
                    self.url_status_file,
                    self.cache_metadata_file,
                    processed_url_hashes_copy,
                    url_metadata_copy,
                    stats_copy
                )
                self.logger.debug("Cache saved successfully")
            except Exception as e:
                self.logger.error("Failed to save cache", error=str(e))
                # 不重新抛出异常，避免影响主要业务流程
    
    def generate_file_paths(self, url: str, file_extension: str = None) -> Dict[str, str]:
        """为URL生成文件路径"""
        return generate_file_paths(url, file_extension, self.path_manager.config)
    
    async def verify_file_integrity(self, url: str) -> Dict[str, bool]:
        """验证URL对应的文件是否存在且完整"""
        return await verify_file_integrity(url, self.url_metadata)
    
    async def cleanup_corrupted_entry(self, url: str) -> bool:
        """清理损坏的缓存条目"""
        url_hash = generate_url_hash(url)
        
        with self._lock:
            if url_hash not in self.processed_url_hashes:
                return False
            
            # 清理文件
            cleaned_files = await cleanup_corrupted_files(url, self.url_metadata)
            
            # 从缓存中移除
            self.processed_url_hashes.discard(url_hash)
            self.url_metadata.pop(url_hash, None)
            
            self.stats["last_updated"] = datetime.now().isoformat()
            
            self.logger.warning(
                "Cleaned up corrupted cache entry", 
                url=url, 
                url_hash=url_hash,
                cleaned_files=cleaned_files
            )
        
        # 保存更新后的缓存
        await self._save_cache()
        return True
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return calculate_cache_stats(
                self.stats,
                self.processed_url_hashes,
                self.url_metadata,
                self.cache_dir,
                self.raw_content_dir,
                self.markdown_content_dir,
                self.url_status_file,
                self.cache_metadata_file
            )
    
    async def clear_cache(self) -> None:
        """清空缓存"""
        with self._lock:
            await clear_all_cache_data(
                self.processed_url_hashes,
                self.url_metadata,
                self.stats,
                self.url_status_file,
                self.cache_metadata_file
            )
    
    async def remove_url(self, url: str) -> bool:
        """从缓存中移除URL"""
        if not self._initialized:
            await self.initialize()
        
        with self._lock:
            url_hash = generate_url_hash(url)
            
            if url_hash in self.processed_url_hashes:
                self.processed_url_hashes.remove(url_hash)
                self.url_metadata.pop(url_hash, None)
                self.stats["last_updated"] = datetime.now().isoformat()
                
                self.logger.debug("URL removed from cache", url=url, url_hash=url_hash)
                
                # 异步保存
                asyncio.create_task(self._save_cache())
                return True
            
            return False
    
    async def get_url_metadata(self, url: str) -> Optional[Dict[str, Any]]:
        """获取URL的元数据"""
        if not self._initialized:
            await self.initialize()

        url_hash = generate_url_hash(url)
        return self.url_metadata.get(url_hash)

    async def check_data_consistency(self) -> Dict[str, Any]:
        """检查缓存数据与文件系统的一致性"""
        if not self._initialized:
            await self.initialize()

        inconsistent_urls = []
        missing_files_count = 0
        total_cached_urls = len(self.processed_url_hashes)

        for url_hash in self.processed_url_hashes:
            if url_hash in self.url_metadata:
                metadata = self.url_metadata[url_hash]
                raw_path = metadata.get("raw_content_path")
                markdown_path = metadata.get("markdown_path")

                # 检查文件是否存在
                raw_exists = raw_path and Path(raw_path).exists()
                markdown_exists = markdown_path and Path(markdown_path).exists()

                # 如果文件路径为空或文件不存在，标记为不一致
                if not raw_path or not markdown_path or not raw_exists or not markdown_exists:
                    inconsistent_urls.append({
                        "url": metadata.get("original_url", "unknown"),
                        "url_hash": url_hash,
                        "raw_path": raw_path,
                        "markdown_path": markdown_path,
                        "raw_exists": raw_exists,
                        "markdown_exists": markdown_exists
                    })
                    missing_files_count += 1

        consistency_report = {
            "total_cached_urls": total_cached_urls,
            "inconsistent_urls_count": len(inconsistent_urls),
            "missing_files_count": missing_files_count,
            "consistency_rate": (total_cached_urls - len(inconsistent_urls)) / total_cached_urls if total_cached_urls > 0 else 1.0,
            "inconsistent_urls": inconsistent_urls
        }

        self.logger.info("Data consistency check completed",
                        total_urls=total_cached_urls,
                        inconsistent_count=len(inconsistent_urls),
                        consistency_rate=f"{consistency_report['consistency_rate']:.2%}")

        return consistency_report

    async def clean_inconsistent_data(self, consistency_report: Dict[str, Any] = None) -> Dict[str, Any]:
        """清理不一致的缓存数据"""
        if not consistency_report:
            consistency_report = await self.check_data_consistency()

        if not consistency_report["inconsistent_urls"]:
            self.logger.info("No inconsistent data found, nothing to clean")
            return {"cleaned_count": 0, "remaining_count": len(self.processed_url_hashes)}

        cleaned_count = 0

        with self._lock:
            for inconsistent_url in consistency_report["inconsistent_urls"]:
                url_hash = inconsistent_url["url_hash"]

                # 从缓存中移除不一致的记录
                if url_hash in self.processed_url_hashes:
                    self.processed_url_hashes.remove(url_hash)
                    cleaned_count += 1

                if url_hash in self.url_metadata:
                    del self.url_metadata[url_hash]

                self.logger.debug("Cleaned inconsistent cache entry",
                                url=inconsistent_url["url"],
                                url_hash=url_hash)

            # 更新统计
            self.stats["unique_urls_processed"] = len(self.processed_url_hashes)
            self.stats["last_updated"] = datetime.now().isoformat()

        # 保存清理后的缓存
        await self._save_cache()

        result = {
            "cleaned_count": cleaned_count,
            "remaining_count": len(self.processed_url_hashes)
        }

        self.logger.info("Inconsistent data cleaned",
                        cleaned_count=cleaned_count,
                        remaining_count=result["remaining_count"])

        return result
