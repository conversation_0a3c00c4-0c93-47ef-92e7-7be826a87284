"""
基础智能体类

基于LangGraph 0.6函数式API的BaseAgent基类实现，
提供标准化的智能体接口和生命周期管理。
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List, TypeVar, Generic, Callable
from uuid import UUID, uuid4
from datetime import datetime
import structlog
from pydantic import BaseModel, Field

# 临时使用StateGraph，等待LangGraph 0.6函数式API正式发布
from langgraph.graph import StateGraph, MessagesState
from langgraph.checkpoint.memory import InMemorySaver
import asyncio
from typing import Awaitable

from src.backend.models.base import BaseModel as BaseDataModel, TimestampMixin, IdentifiableMixin
from src.backend.config import Config

# 类型变量
InputType = TypeVar('InputType', bound=BaseDataModel)
OutputType = TypeVar('OutputType', bound=BaseDataModel)

logger = structlog.get_logger(__name__)

# 添加调试日志
logger.info("BaseAgent module loaded successfully",
           langgraph_available=True,
           state_graph_available=True)


class AgentConfig(BaseDataModel):
    """智能体配置模型"""
    
    agent_id: str
    agent_type: str
    llm_config: Dict[str, Any]
    tools: Optional[List[str]] = None
    max_retries: int = 3
    timeout: int = 300
    parallel_limit: int = 10
    checkpoint_enabled: bool = True


class AgentResult(BaseDataModel, TimestampMixin, IdentifiableMixin):
    """智能体执行结果模型"""
    
    agent_id: str
    agent_type: str
    task_id: UUID
    status: str = Field(default="pending")  # pending, running, completed, failed
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    execution_time_ms: Optional[int] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class BaseAgent(ABC, Generic[InputType, OutputType]):
    """
    基础智能体类 - 基于LangGraph 0.6函数式API
    
    所有智能体都应继承此类并实现核心方法。
    使用@task装饰器实现自动并行执行和检查点机制。
    """
    
    def __init__(self, config: AgentConfig, system_config: Config):
        self.config = config
        self.system_config = system_config
        self.agent_id = config.agent_id
        self.agent_type = config.agent_type
        self.logger = logger.bind(agent_id=self.agent_id, agent_type=self.agent_type)
        
        # 初始化检查点保存器
        self.checkpointer = InMemorySaver() if config.checkpoint_enabled else None
        
        # 初始化状态
        self._initialized = False
        self._tools = {}
        
        self.logger.info("Agent initialized", config=config.model_dump())
    
    async def initialize(self) -> None:
        """
        智能体初始化
        
        在第一次执行任务前调用，用于：
        - 加载工具和资源
        - 建立外部连接
        - 验证配置
        """
        if self._initialized:
            return
        
        try:
            self.logger.info("Initializing agent")
            
            # 加载工具
            await self._load_tools()
            
            # 验证配置
            await self._validate_config()
            
            # 执行子类特定初始化
            await self._initialize_agent()
            
            self._initialized = True
            self.logger.info("Agent initialization completed")
            
        except Exception as e:
            self.logger.error("Agent initialization failed", error=str(e))
            raise
    
    # 临时移除@task装饰器，等待LangGraph 0.6函数式API正式发布
    async def execute_task(self, input_data: InputType, task_id: Optional[UUID] = None) -> AgentResult:
        """
        执行智能体任务 - 使用@task装饰器实现并行执行
        
        Args:
            input_data: 输入数据
            task_id: 任务ID（可选）
            
        Returns:
            AgentResult: 执行结果
        """
        if task_id is None:
            task_id = uuid4()
        
        # 确保智能体已初始化
        if not self._initialized:
            await self.initialize()
        
        start_time = datetime.utcnow()
        result = AgentResult(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            task_id=task_id,
            status="running"
        )
        
        try:
            self.logger.info("Starting task execution", task_id=str(task_id))
            
            # 执行核心处理逻辑
            output_data = await self._process_with_retry(input_data, task_id)
            
            # 更新结果
            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            result.status = "completed"
            result.result_data = output_data.dict() if hasattr(output_data, 'dict') else output_data
            result.execution_time_ms = int(execution_time)
            
            self.logger.info(
                "Task execution completed",
                task_id=str(task_id),
                execution_time_ms=result.execution_time_ms
            )
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            result.status = "failed"
            result.error_message = str(e)
            result.execution_time_ms = int(execution_time)
            
            self.logger.error(
                "Task execution failed",
                task_id=str(task_id),
                error=str(e),
                execution_time_ms=result.execution_time_ms
            )
            
        finally:
            result.update_timestamp()
        
        return result
    
    async def _process_with_retry(self, input_data: InputType, task_id: UUID) -> OutputType:
        """带重试的处理逻辑"""
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.warning(
                        "Retrying task execution",
                        task_id=str(task_id),
                        attempt=attempt,
                        max_retries=self.config.max_retries
                    )
                
                # 调用子类实现的核心处理方法
                return await self._process(input_data, task_id)
                
            except Exception as e:
                last_exception = e
                if attempt < self.config.max_retries:
                    self.logger.warning(
                        "Task execution attempt failed, will retry",
                        task_id=str(task_id),
                        attempt=attempt,
                        error=str(e)
                    )
                    # 可以在这里添加退避策略
                    continue
                else:
                    break
        
        # 所有重试都失败了
        raise last_exception
    
    @abstractmethod
    async def _process(self, input_data: InputType, task_id: UUID) -> OutputType:
        """
        核心处理逻辑 - 子类必须实现
        
        Args:
            input_data: 输入数据
            task_id: 任务ID
            
        Returns:
            OutputType: 处理结果
        """
        pass
    
    async def _load_tools(self) -> None:
        """加载工具 - 子类可重写"""
        if self.config.tools:
            for tool_name in self.config.tools:
                # 这里可以实现工具加载逻辑
                self.logger.debug("Loading tool", tool_name=tool_name)
    
    async def _validate_config(self) -> None:
        """验证配置 - 子类可重写"""
        if not self.config.agent_id:
            raise ValueError("Agent ID is required")
        if not self.config.agent_type:
            raise ValueError("Agent type is required")
    
    async def _initialize_agent(self) -> None:
        """智能体特定初始化 - 子类可重写"""
        pass
    
    async def cleanup(self) -> None:
        """
        清理资源
        
        在智能体销毁前调用，用于：
        - 关闭连接
        - 释放资源
        - 保存状态
        """
        try:
            self.logger.info("Cleaning up agent")
            
            # 执行子类特定清理
            await self._cleanup_agent()
            
            self.logger.info("Agent cleanup completed")
            
        except Exception as e:
            self.logger.error("Agent cleanup failed", error=str(e))
            raise
    
    async def _cleanup_agent(self) -> None:
        """智能体特定清理 - 子类可重写"""
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "initialized": self._initialized,
            "config": self.config.dict(),
        }


class ParallelAgentExecutor:
    """
    并行智能体执行器
    
    使用LangGraph 0.6的Send API实现动态任务分发和负载均衡。
    """
    
    def __init__(self, agents: List[BaseAgent], max_concurrency: int = 10):
        self.agents = {agent.agent_id: agent for agent in agents}
        self.max_concurrency = max_concurrency
        self.logger = logger.bind(component="ParallelAgentExecutor")
    
    # 临时移除@entrypoint装饰器，等待LangGraph 0.6函数式API正式发布
    async def execute_parallel_tasks(self, tasks: List[Dict[str, Any]]) -> List[AgentResult]:
        """
        并行执行多个智能体任务
        
        Args:
            tasks: 任务列表，每个任务包含 agent_id 和 input_data
            
        Returns:
            List[AgentResult]: 执行结果列表
        """
        self.logger.info("Starting parallel task execution", task_count=len(tasks))
        
        # 使用Send API动态分发任务
        task_futures = []
        for task in tasks:
            agent_id = task["agent_id"]
            input_data = task["input_data"]
            task_id = task.get("task_id")
            
            if agent_id not in self.agents:
                self.logger.error("Agent not found", agent_id=agent_id)
                continue
            
            agent = self.agents[agent_id]
            future = agent.execute_task(input_data, task_id)
            task_futures.append(future)
        
        # 等待所有任务完成
        results = []
        for future in task_futures:
            try:
                result = await future
                results.append(result)
            except Exception as e:
                self.logger.error("Task execution failed", error=str(e))
                # 创建失败结果
                error_result = AgentResult(
                    agent_id="unknown",
                    agent_type="unknown", 
                    task_id=uuid4(),
                    status="failed",
                    error_message=str(e)
                )
                results.append(error_result)
        
        self.logger.info("Parallel task execution completed", result_count=len(results))
        return results
