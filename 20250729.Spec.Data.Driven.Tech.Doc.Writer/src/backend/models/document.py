"""
文档相关数据模型

定义文档结果、章节、引用等模型。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import Field, field_validator

from src.backend.models.base import BaseModel, TimestampMixin, IdentifiableMixin


class Reference(BaseModel):
    """引用模型"""
    
    title: str = Field(..., description="标题")
    authors: List[str] = Field(default_factory=list, description="作者列表")
    publication_date: Optional[datetime] = Field(None, description="发布日期")
    url: Optional[str] = Field(None, description="链接")
    source_type: str = Field(..., description="来源类型")
    citation_format: str = Field(default="APA", description="引用格式")
    
    @field_validator("source_type")
    @classmethod
    def validate_source_type(cls, v):
        allowed_types = ["academic", "industry", "news", "government", "opensource", "book", "website"]
        if v not in allowed_types:
            raise ValueError(f"Source type must be one of {allowed_types}")
        return v


class DocumentSection(BaseModel, TimestampMixin):
    """文档章节模型"""
    
    section_id: str = Field(..., description="章节ID")
    title: str = Field(..., description="章节标题")
    content: str = Field(..., description="章节内容")
    level: int = Field(default=1, description="章节级别")
    order: int = Field(default=0, description="章节顺序")
    references: List[Reference] = Field(default_factory=list, description="引用列表")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    @field_validator("level")
    @classmethod
    def validate_level(cls, v):
        if v < 1 or v > 6:
            raise ValueError("Section level must be between 1 and 6")
        return v


class DocumentResult(BaseModel, TimestampMixin, IdentifiableMixin):
    """文档结果模型"""
    
    title: str = Field(..., description="文档标题")
    abstract: Optional[str] = Field(None, description="摘要")
    sections: List[DocumentSection] = Field(default_factory=list, description="章节列表")
    references: List[Reference] = Field(default_factory=list, description="参考文献")
    keywords: List[str] = Field(default_factory=list, description="关键词")
    language: str = Field(default="zh-CN", description="语言")
    format_type: str = Field(default="markdown", description="格式类型")
    word_count: Optional[int] = Field(None, description="字数统计")
    
    # 生成信息
    user_request_id: str = Field(..., description="用户请求ID")
    generation_method: str = Field(default="multi_agent", description="生成方法")
    quality_score: Optional[float] = Field(None, description="质量评分")
    
    @field_validator("format_type")
    @classmethod
    def validate_format_type(cls, v):
        allowed_formats = ["markdown", "html", "pdf", "docx", "latex"]
        if v not in allowed_formats:
            raise ValueError(f"Format type must be one of {allowed_formats}")
        return v

    @field_validator("quality_score")
    @classmethod
    def validate_quality_score(cls, v):
        if v is not None and (v < 0 or v > 1):
            raise ValueError("Quality score must be between 0 and 1")
        return v
    
    def get_total_word_count(self) -> int:
        """计算总字数"""
        total = 0
        if self.abstract:
            total += len(self.abstract)
        
        for section in self.sections:
            total += len(section.content)
        
        return total
    
    def get_section_by_id(self, section_id: str) -> Optional[DocumentSection]:
        """根据ID获取章节"""
        for section in self.sections:
            if section.section_id == section_id:
                return section
        return None
    
    def add_section(self, section: DocumentSection) -> None:
        """添加章节"""
        # 设置章节顺序
        if section.order == 0:
            section.order = len(self.sections) + 1
        
        self.sections.append(section)
        self.sections.sort(key=lambda s: s.order)
    
    def add_reference(self, reference: Reference) -> None:
        """添加引用"""
        if reference not in self.references:
            self.references.append(reference)
