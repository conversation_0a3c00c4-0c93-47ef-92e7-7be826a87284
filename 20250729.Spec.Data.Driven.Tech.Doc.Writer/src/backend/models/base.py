"""
基础数据模型

定义所有数据模型的基类和通用混入类。
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel as PydanticBaseModel, Field
from uuid import uuid4, UUID


class BaseModel(PydanticBaseModel):
    """基础模型类"""
    
    class Config:
        # 启用任意类型
        arbitrary_types_allowed = True
        # 使用枚举值
        use_enum_values = True
        # 验证赋值
        validate_assignment = True
        # JSON编码器
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
        }


class TimestampMixin:
    """时间戳混入类 - 不继承BaseModel避免MRO冲突"""

    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None

    def update_timestamp(self) -> None:
        """更新时间戳"""
        self.updated_at = datetime.utcnow()


class IdentifiableMixin:
    """可标识混入类 - 不继承BaseModel避免MRO冲突"""

    id: UUID = Field(default_factory=uuid4)

    def __str__(self) -> str:
        return f"{self.__class__.__name__}({self.id})"

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(id={self.id})"


class MetadataMixin:
    """元数据混入类 - 不继承BaseModel避免MRO冲突"""

    metadata: Dict[str, Any] = Field(default_factory=dict)

    def set_metadata(self, key: str, value: Any) -> None:
        """设置元数据"""
        self.metadata[key] = value

    def get_metadata(self, key: str, default: Any = None) -> Any:
        """获取元数据"""
        return self.metadata.get(key, default)

    def has_metadata(self, key: str) -> bool:
        """检查是否有指定元数据"""
        return key in self.metadata
