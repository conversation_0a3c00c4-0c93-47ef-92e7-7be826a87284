"""
请求相关数据模型

定义用户请求、需求分析、搜索关键词等模型。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import Field, field_validator
import structlog

from src.backend.models.base import BaseModel, TimestampMixin, IdentifiableMixin

# 初始化结构化日志
logger = structlog.get_logger(__name__)


class UserRequest(BaseModel, TimestampMixin, IdentifiableMixin):
    """用户请求模型"""

    def __init_subclass__(cls, **kwargs):
        """子类初始化时的调试日志"""
        super().__init_subclass__(**kwargs)
        logger.debug("UserRequest subclass initialized",
                    class_name=cls.__name__,
                    mro=[c.__name__ for c in cls.__mro__])

    topic: str = Field(..., description="调研主题")
    scope: str = Field(..., description="调研范围和深度要求")
    format: str = Field(default="技术调研报告", description="输出格式要求")
    deadline: Optional[datetime] = Field(None, description="截止时间")
    language: str = Field(default="zh-CN", description="输出语言")
    additional_requirements: Optional[str] = Field(None, description="额外要求")
    
    @field_validator("topic")
    @classmethod
    def validate_topic(cls, v):
        if not v or len(v.strip()) < 5:
            raise ValueError("Topic must be at least 5 characters long")
        return v.strip()

    @field_validator("scope")
    @classmethod
    def validate_scope(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError("Scope must be at least 10 characters long")
        return v.strip()


class RequirementAnalysis(BaseModel, TimestampMixin):
    """需求分析结果模型"""
    
    user_request_id: str
    parsed_topic: str = Field(..., description="解析后的主题")
    research_objectives: List[str] = Field(..., description="调研目标列表")
    scope_definition: str = Field(..., description="范围定义")
    target_audience: str = Field(..., description="目标受众")
    output_format: str = Field(..., description="输出格式")
    estimated_complexity: str = Field(..., description="复杂度评估")
    recommended_approach: str = Field(..., description="推荐方法")
    
    @field_validator("research_objectives")
    @classmethod
    def validate_objectives(cls, v):
        if not v or len(v) == 0:
            raise ValueError("At least one research objective is required")
        return v


class SearchKeywords(BaseModel, TimestampMixin):
    """搜索关键词模型"""
    
    requirement_analysis_id: str
    primary_keywords: List[str] = Field(..., description="主要关键词")
    secondary_keywords: List[str] = Field(default_factory=list, description="次要关键词")
    technical_terms: List[str] = Field(default_factory=list, description="技术术语")
    industry_terms: List[str] = Field(default_factory=list, description="行业术语")
    english_keywords: List[str] = Field(default_factory=list, description="英文关键词")
    search_combinations: List[List[str]] = Field(default_factory=list, description="搜索组合")
    
    @field_validator("primary_keywords")
    @classmethod
    def validate_primary_keywords(cls, v):
        if not v or len(v) == 0:
            raise ValueError("At least one primary keyword is required")
        return [kw.strip() for kw in v if kw.strip()]


class ResearchScope(BaseModel, TimestampMixin):
    """调研范围定义模型"""
    
    requirement_analysis_id: str
    information_sources: List[str] = Field(..., description="信息来源类型")
    coverage_areas: List[str] = Field(..., description="覆盖领域")
    depth_level: str = Field(..., description="深度级别")
    time_range: Optional[str] = Field(None, description="时间范围")
    geographical_scope: Optional[str] = Field(None, description="地理范围")
    exclusions: List[str] = Field(default_factory=list, description="排除项")
    
    @field_validator("information_sources")
    @classmethod
    def validate_sources(cls, v):
        if not v or len(v) == 0:
            raise ValueError("At least one information source is required")
        return v

    @field_validator("depth_level")
    @classmethod
    def validate_depth(cls, v):
        allowed_levels = ["surface", "moderate", "deep", "comprehensive"]
        if v not in allowed_levels:
            raise ValueError(f"Depth level must be one of {allowed_levels}")
        return v
