"""
数据模型模块

定义系统中使用的所有数据模型，包括：
- 用户请求模型
- 智能体状态模型
- 文档结果模型
- 缓存模型
"""

from src.backend.models.base import BaseModel, TimestampMixin
from src.backend.models.request import UserRequest, RequirementAnalysis, SearchKeywords
from src.backend.models.document import DocumentResult, DocumentSection, Reference
from src.backend.models.agent import AgentState, AgentConfig, TaskResult
from src.backend.models.cache import CacheEntry, CacheMetadata

__all__ = [
    # 基础模型
    "BaseModel",
    "TimestampMixin",
    
    # 请求模型
    "UserRequest",
    "RequirementAnalysis", 
    "SearchKeywords",
    
    # 文档模型
    "DocumentResult",
    "DocumentSection",
    "Reference",
    
    # 智能体模型
    "AgentState",
    "AgentConfig",
    "TaskResult",
    
    # 缓存模型
    "CacheEntry",
    "CacheMetadata",
]
