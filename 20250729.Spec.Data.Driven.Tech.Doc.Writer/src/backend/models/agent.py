"""
智能体相关数据模型

定义智能体状态、配置、任务结果等模型。
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum
from pydantic import Field, field_validator

from src.backend.models.base import BaseModel, TimestampMixin, IdentifiableMixin


class AgentStatus(str, Enum):
    """智能体状态枚举"""
    IDLE = "idle"
    INITIALIZING = "initializing"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    STOPPED = "stopped"


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentConfig(BaseModel):
    """智能体配置模型"""
    
    agent_id: str = Field(..., description="智能体ID")
    agent_type: str = Field(..., description="智能体类型")
    llm_config: Dict[str, Any] = Field(default_factory=dict, description="LLM配置")
    tools: List[str] = Field(default_factory=list, description="工具列表")
    max_retries: int = Field(default=3, description="最大重试次数")
    timeout: int = Field(default=300, description="超时时间（秒）")
    parallel_limit: int = Field(default=10, description="并行限制")
    checkpoint_enabled: bool = Field(default=True, description="是否启用检查点")
    
    @field_validator("max_retries")
    @classmethod
    def validate_max_retries(cls, v):
        if v < 0:
            raise ValueError("Max retries must be non-negative")
        return v

    @field_validator("timeout")
    @classmethod
    def validate_timeout(cls, v):
        if v <= 0:
            raise ValueError("Timeout must be positive")
        return v


class AgentState(BaseModel, TimestampMixin, IdentifiableMixin):
    """智能体状态模型"""
    
    agent_id: str = Field(..., description="智能体ID")
    agent_type: str = Field(..., description="智能体类型")
    status: AgentStatus = Field(default=AgentStatus.IDLE, description="当前状态")
    current_task_id: Optional[str] = Field(None, description="当前任务ID")
    last_activity: datetime = Field(default_factory=datetime.utcnow, description="最后活动时间")
    
    # 统计信息
    total_tasks: int = Field(default=0, description="总任务数")
    completed_tasks: int = Field(default=0, description="已完成任务数")
    failed_tasks: int = Field(default=0, description="失败任务数")
    average_execution_time: float = Field(default=0.0, description="平均执行时间（秒）")
    
    # 资源使用
    memory_usage_mb: float = Field(default=0.0, description="内存使用（MB）")
    cpu_usage_percent: float = Field(default=0.0, description="CPU使用率（%）")
    
    # 错误信息
    last_error: Optional[str] = Field(None, description="最后错误信息")
    error_count: int = Field(default=0, description="错误计数")
    
    def update_activity(self) -> None:
        """更新活动时间"""
        self.last_activity = datetime.utcnow()
        self.update_timestamp()
    
    def start_task(self, task_id: str) -> None:
        """开始任务"""
        self.current_task_id = task_id
        self.status = AgentStatus.RUNNING
        self.total_tasks += 1
        self.update_activity()
    
    def complete_task(self, execution_time: float) -> None:
        """完成任务"""
        self.current_task_id = None
        self.status = AgentStatus.IDLE
        self.completed_tasks += 1
        
        # 更新平均执行时间
        if self.completed_tasks == 1:
            self.average_execution_time = execution_time
        else:
            self.average_execution_time = (
                (self.average_execution_time * (self.completed_tasks - 1) + execution_time) 
                / self.completed_tasks
            )
        
        self.update_activity()
    
    def fail_task(self, error_message: str) -> None:
        """任务失败"""
        self.current_task_id = None
        self.status = AgentStatus.FAILED
        self.failed_tasks += 1
        self.last_error = error_message
        self.error_count += 1
        self.update_activity()


class TaskResult(BaseModel, TimestampMixin, IdentifiableMixin):
    """任务结果模型"""
    
    task_id: str = Field(..., description="任务ID")
    agent_id: str = Field(..., description="执行智能体ID")
    agent_type: str = Field(..., description="智能体类型")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    
    # 输入输出
    input_data: Dict[str, Any] = Field(default_factory=dict, description="输入数据")
    output_data: Optional[Dict[str, Any]] = Field(None, description="输出数据")
    
    # 执行信息
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    execution_time_ms: Optional[int] = Field(None, description="执行时间（毫秒）")
    
    # 错误信息
    error_message: Optional[str] = Field(None, description="错误信息")
    error_type: Optional[str] = Field(None, description="错误类型")
    retry_count: int = Field(default=0, description="重试次数")
    
    # 质量指标
    quality_score: Optional[float] = Field(None, description="质量评分")
    confidence_score: Optional[float] = Field(None, description="置信度评分")
    
    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    @field_validator("quality_score", "confidence_score")
    @classmethod
    def validate_scores(cls, v):
        if v is not None and (v < 0 or v > 1):
            raise ValueError("Score must be between 0 and 1")
        return v
    
    def start_execution(self) -> None:
        """开始执行"""
        self.status = TaskStatus.RUNNING
        self.start_time = datetime.utcnow()
        self.update_timestamp()
    
    def complete_execution(self, output_data: Dict[str, Any]) -> None:
        """完成执行"""
        self.status = TaskStatus.COMPLETED
        self.end_time = datetime.utcnow()
        self.output_data = output_data
        
        if self.start_time:
            execution_time = (self.end_time - self.start_time).total_seconds() * 1000
            self.execution_time_ms = int(execution_time)
        
        self.update_timestamp()
    
    def fail_execution(self, error_message: str, error_type: str = None) -> None:
        """执行失败"""
        self.status = TaskStatus.FAILED
        self.end_time = datetime.utcnow()
        self.error_message = error_message
        self.error_type = error_type
        
        if self.start_time:
            execution_time = (self.end_time - self.start_time).total_seconds() * 1000
            self.execution_time_ms = int(execution_time)
        
        self.update_timestamp()
    
    def cancel_execution(self) -> None:
        """取消执行"""
        self.status = TaskStatus.CANCELLED
        self.end_time = datetime.utcnow()
        
        if self.start_time:
            execution_time = (self.end_time - self.start_time).total_seconds() * 1000
            self.execution_time_ms = int(execution_time)
        
        self.update_timestamp()
