"""
缓存相关数据模型

定义缓存条目、元数据等模型。
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from enum import Enum
from pydantic import Field, field_validator

from src.backend.models.base import BaseModel, TimestampMixin, IdentifiableMixin


class CacheType(str, Enum):
    """缓存类型枚举"""
    RAW_FILE = "raw_file"
    PROCESSED_CONTENT = "processed_content"
    LLM_RESPONSE = "llm_response"
    SEARCH_RESULT = "search_result"
    ANALYSIS_RESULT = "analysis_result"


class CacheStatus(str, Enum):
    """缓存状态枚举"""
    VALID = "valid"
    EXPIRED = "expired"
    INVALID = "invalid"
    PENDING = "pending"


class CacheMetadata(BaseModel, TimestampMixin):
    """缓存元数据模型"""
    
    cache_key: str = Field(..., description="缓存键")
    cache_type: CacheType = Field(..., description="缓存类型")
    content_hash: str = Field(..., description="内容哈希")
    content_size: int = Field(..., description="内容大小（字节）")
    
    # TTL配置
    ttl_seconds: int = Field(..., description="生存时间（秒）")
    expires_at: datetime = Field(..., description="过期时间")
    
    # 访问统计
    access_count: int = Field(default=0, description="访问次数")
    last_accessed: Optional[datetime] = Field(None, description="最后访问时间")
    
    # 来源信息
    source_url: Optional[str] = Field(None, description="来源URL")
    source_type: Optional[str] = Field(None, description="来源类型")
    
    @field_validator("ttl_seconds")
    @classmethod
    def validate_ttl(cls, v):
        if v <= 0:
            raise ValueError("TTL must be positive")
        return v

    @field_validator("content_size")
    @classmethod
    def validate_content_size(cls, v):
        if v < 0:
            raise ValueError("Content size must be non-negative")
        return v
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return datetime.utcnow() > self.expires_at
    
    def get_status(self) -> CacheStatus:
        """获取缓存状态"""
        if self.is_expired():
            return CacheStatus.EXPIRED
        return CacheStatus.VALID
    
    def update_access(self) -> None:
        """更新访问信息"""
        self.access_count += 1
        self.last_accessed = datetime.utcnow()
        self.update_timestamp()


class CacheEntry(BaseModel, TimestampMixin, IdentifiableMixin):
    """缓存条目模型"""
    
    cache_key: str = Field(..., description="缓存键")
    cache_type: CacheType = Field(..., description="缓存类型")
    content: Any = Field(..., description="缓存内容")
    metadata: CacheMetadata = Field(..., description="缓存元数据")
    
    # 存储信息
    storage_path: Optional[str] = Field(None, description="存储路径")
    compression_type: Optional[str] = Field(None, description="压缩类型")
    
    # 依赖关系
    dependencies: list = Field(default_factory=list, description="依赖的缓存键")
    dependents: list = Field(default_factory=list, description="依赖此缓存的键")
    
    @field_validator("cache_key")
    @classmethod
    def validate_cache_key(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Cache key cannot be empty")
        return v.strip()
    
    def is_valid(self) -> bool:
        """检查缓存是否有效"""
        return self.metadata.get_status() == CacheStatus.VALID
    
    def invalidate(self) -> None:
        """使缓存失效"""
        self.metadata.expires_at = datetime.utcnow()
        self.update_timestamp()
    
    def extend_ttl(self, additional_seconds: int) -> None:
        """延长TTL"""
        self.metadata.expires_at += timedelta(seconds=additional_seconds)
        self.metadata.ttl_seconds += additional_seconds
        self.update_timestamp()
    
    def access(self) -> Any:
        """访问缓存内容"""
        if not self.is_valid():
            raise ValueError("Cache entry is not valid")
        
        self.metadata.update_access()
        return self.content
