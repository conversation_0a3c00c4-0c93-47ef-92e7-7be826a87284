"""
Tracemalloc 管理模块

提供内存分配跟踪的配置化管理，支持：
- 配置文件控制
- 环境变量覆盖
- 智能启用/禁用
- 性能监控
"""

import os
import tracemalloc
from typing import Optional, Tuple
import structlog

logger = structlog.get_logger(__name__)


class TraceMallocManager:
    """Tracemalloc 管理器"""
    
    def __init__(self):
        self._is_enabled = False
        self._nframe = 25
        self._config_enabled = False
        self._env_enabled = None
        
    def configure(self, config_enabled: bool = False, nframe: int = 25) -> None:
        """配置 tracemalloc 参数
        
        Args:
            config_enabled: 配置文件中的启用状态
            nframe: 保存的调用帧数
        """
        self._config_enabled = config_enabled
        self._nframe = max(1, min(nframe, 100))  # 限制在合理范围内
        
        # 检查环境变量覆盖
        env_value = os.getenv("TRACEMALLOC_ENABLED")
        if env_value is not None:
            self._env_enabled = env_value.lower() in ("true", "1", "yes", "on")
        
        # 检查 PYTHONTRACEMALLOC 环境变量
        python_tracemalloc = os.getenv("PYTHONTRACEMALLOC")
        if python_tracemalloc and not self._env_enabled:
            try:
                # PYTHONTRACEMALLOC 可以是数字（帧数）或布尔值
                if python_tracemalloc.isdigit():
                    self._env_enabled = True
                    self._nframe = int(python_tracemalloc)
                else:
                    self._env_enabled = python_tracemalloc.lower() in ("true", "1", "yes", "on")
            except ValueError:
                pass
        
        logger.debug(
            "Tracemalloc configuration updated",
            config_enabled=self._config_enabled,
            env_enabled=self._env_enabled,
            nframe=self._nframe,
            python_tracemalloc=python_tracemalloc
        )
    
    def should_enable(self) -> bool:
        """判断是否应该启用 tracemalloc
        
        优先级：环境变量 > 配置文件 > 默认值(False)
        
        Returns:
            bool: 是否应该启用
        """
        if self._env_enabled is not None:
            return self._env_enabled
        return self._config_enabled
    
    def start(self) -> bool:
        """启动 tracemalloc
        
        Returns:
            bool: 是否成功启动
        """
        if self._is_enabled:
            logger.debug("Tracemalloc already enabled")
            return True
            
        if not self.should_enable():
            logger.debug("Tracemalloc disabled by configuration")
            return False
            
        try:
            if tracemalloc.is_tracing():
                logger.debug("Tracemalloc already tracing, updating configuration")
                self._is_enabled = True
                return True
                
            tracemalloc.start(self._nframe)
            self._is_enabled = True
            
            logger.info(
                "Tracemalloc enabled successfully",
                nframe=self._nframe,
                enabled_by="environment" if self._env_enabled is not None else "config",
                memory_overhead_kb=tracemalloc.get_tracemalloc_memory() // 1024
            )
            return True
            
        except Exception as e:
            logger.error(
                "Failed to start tracemalloc",
                error=str(e),
                error_type=type(e).__name__
            )
            return False
    
    def stop(self) -> None:
        """停止 tracemalloc"""
        if not self._is_enabled:
            return
            
        try:
            if tracemalloc.is_tracing():
                tracemalloc.stop()
                logger.info("Tracemalloc stopped")
            self._is_enabled = False
        except Exception as e:
            logger.error(
                "Failed to stop tracemalloc",
                error=str(e),
                error_type=type(e).__name__
            )
    
    def is_enabled(self) -> bool:
        """检查是否已启用"""
        return self._is_enabled and tracemalloc.is_tracing()
    
    def get_memory_usage(self) -> Optional[Tuple[int, int]]:
        """获取内存使用情况
        
        Returns:
            Optional[Tuple[int, int]]: (当前内存, 峰值内存) 单位：字节，如果未启用则返回 None
        """
        if not self.is_enabled():
            return None
            
        try:
            return tracemalloc.get_traced_memory()
        except Exception as e:
            logger.error(
                "Failed to get traced memory",
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    def get_tracemalloc_overhead(self) -> Optional[int]:
        """获取 tracemalloc 自身的内存开销
        
        Returns:
            Optional[int]: 内存开销（字节），如果未启用则返回 None
        """
        if not self.is_enabled():
            return None
            
        try:
            return tracemalloc.get_tracemalloc_memory()
        except Exception as e:
            logger.error(
                "Failed to get tracemalloc memory overhead",
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    def take_snapshot(self):
        """获取内存快照
        
        Returns:
            Optional[tracemalloc.Snapshot]: 内存快照，如果未启用则返回 None
        """
        if not self.is_enabled():
            logger.warning("Cannot take snapshot: tracemalloc not enabled")
            return None
            
        try:
            return tracemalloc.take_snapshot()
        except Exception as e:
            logger.error(
                "Failed to take memory snapshot",
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    def log_memory_stats(self) -> None:
        """记录内存统计信息"""
        if not self.is_enabled():
            return
            
        try:
            current, peak = tracemalloc.get_traced_memory()
            overhead = tracemalloc.get_tracemalloc_memory()
            
            logger.info(
                "Memory statistics",
                current_mb=round(current / 1024 / 1024, 2),
                peak_mb=round(peak / 1024 / 1024, 2),
                overhead_kb=round(overhead / 1024, 2),
                traceback_limit=tracemalloc.get_traceback_limit()
            )
        except Exception as e:
            logger.error(
                "Failed to log memory statistics",
                error=str(e),
                error_type=type(e).__name__
            )
    
    def get_status(self) -> dict:
        """获取 tracemalloc 状态信息
        
        Returns:
            dict: 状态信息
        """
        status = {
            "enabled": self.is_enabled(),
            "config_enabled": self._config_enabled,
            "env_enabled": self._env_enabled,
            "should_enable": self.should_enable(),
            "nframe": self._nframe,
        }
        
        if self.is_enabled():
            try:
                current, peak = tracemalloc.get_traced_memory()
                overhead = tracemalloc.get_tracemalloc_memory()
                status.update({
                    "current_memory_bytes": current,
                    "peak_memory_bytes": peak,
                    "overhead_bytes": overhead,
                    "traceback_limit": tracemalloc.get_traceback_limit()
                })
            except Exception:
                pass
                
        return status


# 全局 tracemalloc 管理器实例
_tracemalloc_manager = TraceMallocManager()


def get_tracemalloc_manager() -> TraceMallocManager:
    """获取全局 tracemalloc 管理器实例"""
    return _tracemalloc_manager


def configure_tracemalloc(config_enabled: bool = False, nframe: int = 25) -> None:
    """配置 tracemalloc（便捷函数）"""
    _tracemalloc_manager.configure(config_enabled, nframe)


def start_tracemalloc() -> bool:
    """启动 tracemalloc（便捷函数）"""
    return _tracemalloc_manager.start()


def stop_tracemalloc() -> None:
    """停止 tracemalloc（便捷函数）"""
    _tracemalloc_manager.stop()


def is_tracemalloc_enabled() -> bool:
    """检查 tracemalloc 是否启用（便捷函数）"""
    return _tracemalloc_manager.is_enabled()


def log_memory_stats() -> None:
    """记录内存统计信息（便捷函数）"""
    _tracemalloc_manager.log_memory_stats()
