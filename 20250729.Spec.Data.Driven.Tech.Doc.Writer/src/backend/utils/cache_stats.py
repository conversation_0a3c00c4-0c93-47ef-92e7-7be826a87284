"""
缓存统计和管理工具

从 url_cache_manager.py 中提取，遵循代码行数限制。
"""

import structlog
from typing import Dict, Any, Set
from pathlib import Path
from datetime import datetime

logger = structlog.get_logger(__name__)


def calculate_cache_stats(
    stats: Dict[str, Any],
    processed_url_hashes: Set[str],
    url_metadata: Dict[str, Dict[str, Any]],
    cache_dir: Path,
    raw_content_dir: Path,
    markdown_content_dir: Path,
    url_status_file: Path,
    cache_metadata_file: Path
) -> Dict[str, Any]:
    """
    计算缓存统计信息
    
    Args:
        stats: 基础统计信息
        processed_url_hashes: 已处理的URL哈希集合
        url_metadata: URL元数据字典
        cache_dir: 缓存目录
        raw_content_dir: 原始内容目录
        markdown_content_dir: Markdown内容目录
        url_status_file: URL状态文件
        cache_metadata_file: 缓存元数据文件
        
    Returns:
        完整的统计信息字典
    """
    hit_rate = 0.0
    if stats["total_requests"] > 0:
        hit_rate = stats["cache_hits"] / stats["total_requests"]
    
    # 统计成功和失败的URL数量
    successful_urls = sum(1 for metadata in url_metadata.values() if metadata.get("success", False))
    failed_urls = len(url_metadata) - successful_urls
    
    return {
        **stats,
        "cached_url_hashes_count": len(processed_url_hashes),
        "successful_urls": successful_urls,
        "failed_urls": failed_urls,
        "cache_hit_rate": hit_rate,
        "cache_directory": str(cache_dir),
        "raw_content_directory": str(raw_content_dir),
        "markdown_content_directory": str(markdown_content_dir),
        "cache_file_exists": url_status_file.exists(),
        "metadata_file_exists": cache_metadata_file.exists()
    }


async def clear_all_cache_data(
    processed_url_hashes: Set[str],
    url_metadata: Dict[str, Dict[str, Any]],
    stats: Dict[str, Any],
    url_status_file: Path,
    cache_metadata_file: Path
) -> None:
    """
    清空所有缓存数据
    
    Args:
        processed_url_hashes: 要清空的URL哈希集合
        url_metadata: 要清空的URL元数据字典
        stats: 要重置的统计信息字典
        url_status_file: URL状态文件
        cache_metadata_file: 缓存元数据文件
    """
    # 清空内存数据
    processed_url_hashes.clear()
    url_metadata.clear()
    stats.clear()
    stats.update({
        "total_requests": 0,
        "cache_hits": 0,
        "cache_misses": 0,
        "urls_added": 0,
        "last_updated": datetime.now().isoformat()
    })
    
    # 删除缓存文件
    try:
        if url_status_file.exists():
            url_status_file.unlink()
        if cache_metadata_file.exists():
            cache_metadata_file.unlink()
            
        logger.info("Cache cleared")
        
    except Exception as e:
        logger.error("Failed to clear cache files", error=str(e))
