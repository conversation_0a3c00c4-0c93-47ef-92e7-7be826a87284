"""
并发处理器

处理多个URL的并发处理逻辑。
从 web_content_processor_v2.py 中提取，遵循代码行数限制。
"""

import asyncio
import structlog
from typing import List, Dict, Any, Callable

logger = structlog.get_logger(__name__)


class ConcurrentProcessor:
    """并发处理器"""
    
    def __init__(self):
        self.logger = structlog.get_logger(self.__class__.__name__)
    
    async def process_urls_concurrently(
        self, 
        urls: List[str], 
        process_func: Callable,
        max_concurrent: int = 5,
        stats: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        并发处理多个URL
        
        Args:
            urls: URL列表
            process_func: 处理单个URL的函数
            max_concurrent: 最大并发数
            stats: 统计信息字典（可选）
            
        Returns:
            处理结果列表
        """
        if not urls:
            return []
        
        self.logger.info("Starting batch URL processing", url_count=len(urls), max_concurrent=max_concurrent)
        
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_with_semaphore(url: str) -> Dict[str, Any]:
            async with semaphore:
                return await process_func(url)
        
        # 创建并发任务
        tasks = [process_with_semaphore(url) for url in urls]
        
        # 执行并发处理
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果和异常
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error("Processing task failed", url=urls[i], error=str(result))
                processed_results.append({
                    "url": urls[i],
                    "success": False,
                    "error": str(result),
                    "stage": "task_exception"
                })
            else:
                processed_results.append(result)
        
        # 记录统计信息
        if stats:
            self.logger.info(
                "Batch URL processing completed",
                total_urls=len(urls),
                successful_count=stats.get("successful_processed", 0),
                quality_passed_count=stats.get("quality_passed", 0),
                cache_hits=stats.get("cache_hits", 0),
                concurrent_skips=stats.get("concurrent_skips", 0)
            )
        else:
            successful_count = sum(1 for r in processed_results if r.get("success", False))
            self.logger.info(
                "Batch URL processing completed",
                total_urls=len(urls),
                successful_count=successful_count,
                failed_count=len(urls) - successful_count
            )
        
        return processed_results
