"""
URL 处理工具模块

提供 URL 标准化、哈希生成等工具函数。
从 url_cache_manager.py 中提取，遵循代码行数限制。
"""

import hashlib
from urllib.parse import urlparse, urlunparse, parse_qs, urlencode
from src.backend.utils.project_paths import get_path_manager


def normalize_url_for_hash(original_url: str) -> str:
    """
    URL标准化处理，用于生成一致的MD5哈希
    
    Args:
        original_url: 完整的原始URL
        
    Returns:
        标准化后的URL
    """
    # 1. 去除fragment部分
    if '#' in original_url:
        url_without_fragment = original_url.split('#')[0]
    else:
        url_without_fragment = original_url
    
    # 2. 解析URL
    parsed = urlparse(url_without_fragment)
    
    # 3. 标准化scheme和host为小写
    scheme = parsed.scheme.lower()
    netloc = parsed.netloc.lower()
    
    # 4. 移除默认端口
    if ':80' in netloc and scheme == 'http':
        netloc = netloc.replace(':80', '')
    elif ':443' in netloc and scheme == 'https':
        netloc = netloc.replace(':443', '')
    
    # 5. 标准化查询参数（排序）
    if parsed.query:
        query_params = parse_qs(parsed.query, keep_blank_values=True)
        # 排序参数
        sorted_params = sorted(query_params.items())
        normalized_query = urlencode(sorted_params, doseq=True)
    else:
        normalized_query = parsed.query
    
    # 6. 重新组装URL
    normalized_parsed = parsed._replace(
        scheme=scheme,
        netloc=netloc,
        query=normalized_query
    )
    
    return urlunparse(normalized_parsed)


def generate_url_hash(original_url: str) -> str:
    """
    为URL生成MD5哈希
    
    Args:
        original_url: 完整的原始URL
        
    Returns:
        32位MD5哈希字符串
    """
    normalized_url = normalize_url_for_hash(original_url)
    return hashlib.md5(normalized_url.encode('utf-8')).hexdigest()


def get_file_extension_from_url(url: str) -> str:
    """
    从URL中提取文件扩展名
    
    Args:
        url: URL字符串
        
    Returns:
        文件扩展名（不包含点），如果没有扩展名则返回'html'
    """
    parsed = urlparse(url)
    path = parsed.path
    
    if '.' in path:
        # 获取最后一个点后的内容
        extension = path.split('.')[-1].lower()
        # 验证是否为有效的扩展名（只包含字母数字）
        if extension.isalnum() and len(extension) <= 10:
            return extension
    
    # 默认返回html
    return 'html'


def generate_file_paths(url: str, file_extension: str = None, config=None) -> dict:
    """
    为URL生成文件路径

    Args:
        url: 原始URL
        file_extension: 文件扩展名（可选，如果不提供则从URL推断）
        config: 配置对象（可选，用于获取正确的数据目录路径）

    Returns:
        包含url_hash、raw_content_path和markdown_path的字典
    """
    url_hash = generate_url_hash(url)

    if file_extension is None:
        file_extension = get_file_extension_from_url(url)

    # 使用配置驱动的绝对路径
    path_manager = get_path_manager(config)
    data_dir = path_manager.get_data_dir()

    raw_content_path = str(data_dir / "raw_content" / f"{url_hash}.{file_extension}")
    markdown_path = str(data_dir / "markdown_content" / f"{url_hash}.md")

    return {
        "url_hash": url_hash,
        "raw_content_path": raw_content_path,
        "markdown_path": markdown_path
    }
