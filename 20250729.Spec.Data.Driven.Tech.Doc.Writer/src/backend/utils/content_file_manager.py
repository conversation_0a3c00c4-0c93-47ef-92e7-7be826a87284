"""
内容文件管理器

处理原始内容和 Markdown 文件的保存、验证等操作。
从 web_content_processor_v2.py 中提取，遵循代码行数限制。
"""

import structlog
from typing import Dict, Any, Optional
from pathlib import Path
import aiofiles

logger = structlog.get_logger(__name__)


class ContentFileManager:
    """内容文件管理器"""

    def __init__(self, config: Optional[Any] = None):
        self.config = config
        self.logger = structlog.get_logger(self.__class__.__name__)
    
    async def save_raw_content(self, file_path: Path, content: str) -> bool:
        """
        保存原始内容到文件
        
        Args:
            file_path: 文件路径
            content: 内容字符串
            
        Returns:
            True 如果保存成功，False 如果失败
        """
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write(content)
            
            self.logger.debug("Raw content saved", file_path=str(file_path))
            return True
            
        except Exception as e:
            self.logger.error("Failed to save raw content", file_path=str(file_path), error=str(e))
            return False
    
    async def save_markdown_content(self, file_path: Path, content: str) -> bool:
        """
        保存 Markdown 内容到文件
        
        Args:
            file_path: 文件路径
            content: Markdown 内容字符串
            
        Returns:
            True 如果保存成功，False 如果失败
        """
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write(content)
            
            self.logger.debug("Markdown content saved", file_path=str(file_path))
            return True
            
        except Exception as e:
            self.logger.error("Failed to save markdown content", file_path=str(file_path), error=str(e))
            return False
    
    async def save_binary_content(self, file_path: Path, content: bytes) -> bool:
        """
        保存二进制内容到文件
        
        Args:
            file_path: 文件路径
            content: 二进制内容
            
        Returns:
            True 如果保存成功，False 如果失败
        """
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)
            
            self.logger.debug("Binary content saved", file_path=str(file_path))
            return True
            
        except Exception as e:
            self.logger.error("Failed to save binary content", file_path=str(file_path), error=str(e))
            return False
    
    def create_error_result(self, url: str, error: str, stage: str) -> Dict[str, Any]:
        """
        创建错误结果字典
        
        Args:
            url: URL
            error: 错误信息
            stage: 错误阶段
            
        Returns:
            错误结果字典
        """
        return {
            "url": url,
            "success": False,
            "error": error,
            "stage": stage
        }
    
    def create_success_result(
        self, 
        url: str, 
        raw_content_path: str = None,
        markdown_path: str = None,
        quality_result: Dict[str, Any] = None,
        processing_time: float = 0.0,
        **kwargs
    ) -> Dict[str, Any]:
        """
        创建成功结果字典
        
        Args:
            url: URL
            raw_content_path: 原始内容文件路径
            markdown_path: Markdown 文件路径
            quality_result: 质量评估结果
            processing_time: 处理时间
            **kwargs: 其他参数
            
        Returns:
            成功结果字典
        """
        result = {
            "url": url,
            "success": True,
            "raw_content_path": raw_content_path,
            "markdown_path": markdown_path,
            "processing_time": processing_time,
            **kwargs
        }
        
        if quality_result:
            result["quality_result"] = quality_result
        
        return result
