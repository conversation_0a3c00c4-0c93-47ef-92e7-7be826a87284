"""
URL 处理锁机制

基于文件系统的分布式锁，确保同一URL不会被多个进程同时处理。
支持锁超时、自动清理和进程检测。
"""

import os
import time
import fcntl
import structlog
from pathlib import Path
from contextlib import contextmanager
from typing import Optional

from src.backend.utils.url_utils import generate_url_hash
from src.backend.utils.project_paths import get_path_manager, resolve_config_path

logger = structlog.get_logger(__name__)


class URLAlreadyProcessingError(Exception):
    """URL正在被其他进程处理的异常"""
    pass


class URLProcessingLock:
    """
    URL处理锁管理器
    
    使用文件系统锁确保同一URL不会被多个进程同时处理。
    支持锁超时、自动清理过期锁和进程存活检测。
    """
    
    def __init__(self, lock_dir: Optional[Path] = None, timeout: int = 300, config=None):
        """
        初始化锁管理器

        Args:
            lock_dir: 锁文件存储目录，默认为 data/locks
            timeout: 锁超时时间（秒），默认5分钟
            config: 配置对象（可选，用于获取正确的数据目录路径）
        """
        if lock_dir is None:
            # 使用配置驱动的路径
            path_manager = get_path_manager(config)
            data_dir = path_manager.get_data_dir()
            self.lock_dir = data_dir / "locks"
        else:
            # 如果提供了自定义路径，确保它是绝对路径
            path_manager = get_path_manager(config)
            self.lock_dir = resolve_config_path(str(lock_dir), path_manager.project_root)

        self.timeout = timeout
        self.lock_dir.mkdir(parents=True, exist_ok=True)
        
        logger.debug("URL processing lock manager initialized", 
                    lock_dir=str(self.lock_dir), timeout=timeout)
    
    @contextmanager
    def acquire_url_lock(self, url: str):
        """
        获取URL处理锁的上下文管理器
        
        Args:
            url: 要锁定的URL
            
        Yields:
            URL的MD5哈希值
            
        Raises:
            URLAlreadyProcessingError: 如果URL正在被其他进程处理
        """
        url_hash = generate_url_hash(url)
        lock_file = self.lock_dir / f"{url_hash}.lock"
        lock_fd = None
        
        try:
            # 创建锁文件
            lock_fd = os.open(lock_file, os.O_CREAT | os.O_WRONLY | os.O_TRUNC)
            
            # 尝试获取排他锁（非阻塞）
            try:
                fcntl.flock(lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
                logger.debug("Acquired URL lock", url=url, url_hash=url_hash)
            except BlockingIOError:
                # 锁被占用，检查是否超时
                if self._is_lock_stale(lock_file):
                    # 强制清理过期锁
                    os.close(lock_fd)
                    lock_file.unlink(missing_ok=True)
                    logger.warning("Cleaned up stale lock", url=url, url_hash=url_hash)
                    
                    # 重新尝试获取锁
                    lock_fd = os.open(lock_file, os.O_CREAT | os.O_WRONLY | os.O_TRUNC)
                    fcntl.flock(lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
                    logger.debug("Acquired URL lock after cleanup", url=url, url_hash=url_hash)
                else:
                    raise URLAlreadyProcessingError(
                        f"URL {url} (hash: {url_hash}) is being processed by another process"
                    )
            
            # 写入进程信息到锁文件
            process_info = f"{os.getpid()}:{time.time()}:{url}"
            os.write(lock_fd, process_info.encode('utf-8'))
            os.fsync(lock_fd)
            
            yield url_hash
            
        finally:
            # 释放锁并清理文件
            if lock_fd is not None:
                try:
                    fcntl.flock(lock_fd, fcntl.LOCK_UN)
                    os.close(lock_fd)
                    logger.debug("Released URL lock", url=url, url_hash=url_hash)
                except Exception as e:
                    logger.warning("Error releasing lock", url=url, error=str(e))
                
                # 清理锁文件
                try:
                    lock_file.unlink(missing_ok=True)
                except Exception as e:
                    logger.warning("Error cleaning up lock file", 
                                 lock_file=str(lock_file), error=str(e))
    
    def _is_lock_stale(self, lock_file: Path) -> bool:
        """
        检查锁是否过期
        
        Args:
            lock_file: 锁文件路径
            
        Returns:
            True如果锁过期，False如果锁仍然有效
        """
        try:
            if not lock_file.exists():
                return True
            
            # 检查文件修改时间
            mtime = lock_file.stat().st_mtime
            if time.time() - mtime > self.timeout:
                logger.debug("Lock file is stale (timeout)", 
                           lock_file=str(lock_file), age=time.time() - mtime)
                return True
            
            # 检查进程是否还存在
            try:
                with open(lock_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if ':' in content:
                        parts = content.split(':', 2)
                        if len(parts) >= 2:
                            pid_str = parts[0]
                            try:
                                pid = int(pid_str)
                                
                                # 检查进程是否存在（发送信号0不会实际发送信号）
                                os.kill(pid, 0)
                                logger.debug("Lock process is still alive", 
                                           lock_file=str(lock_file), pid=pid)
                                return False  # 进程存在，锁有效
                            except (ValueError, ProcessLookupError):
                                logger.debug("Lock process no longer exists", 
                                           lock_file=str(lock_file), pid_str=pid_str)
                                return True  # 进程不存在，锁过期
            except Exception as e:
                logger.warning("Error checking lock file content", 
                             lock_file=str(lock_file), error=str(e))
                return True  # 出错时认为锁过期
            
            return False
            
        except Exception as e:
            logger.warning("Error checking if lock is stale", 
                         lock_file=str(lock_file), error=str(e))
            return True  # 出错时认为锁过期
    
    def cleanup_stale_locks(self) -> int:
        """
        清理所有过期的锁文件
        
        Returns:
            清理的锁文件数量
        """
        cleaned_count = 0
        
        try:
            for lock_file in self.lock_dir.glob("*.lock"):
                if self._is_lock_stale(lock_file):
                    try:
                        lock_file.unlink()
                        cleaned_count += 1
                        logger.debug("Cleaned up stale lock file", lock_file=str(lock_file))
                    except Exception as e:
                        logger.warning("Failed to clean up stale lock file", 
                                     lock_file=str(lock_file), error=str(e))
            
            if cleaned_count > 0:
                logger.info("Cleaned up stale locks", count=cleaned_count)
                
        except Exception as e:
            logger.error("Error during stale lock cleanup", error=str(e))
        
        return cleaned_count
    
    def get_lock_info(self, url: str) -> Optional[dict]:
        """
        获取URL锁的信息
        
        Args:
            url: 要查询的URL
            
        Returns:
            锁信息字典，如果没有锁则返回None
        """
        url_hash = generate_url_hash(url)
        lock_file = self.lock_dir / f"{url_hash}.lock"
        
        if not lock_file.exists():
            return None
        
        try:
            with open(lock_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
            if ':' in content:
                parts = content.split(':', 2)
                if len(parts) >= 3:
                    pid_str, timestamp_str, locked_url = parts
                    return {
                        "url": locked_url,
                        "url_hash": url_hash,
                        "pid": int(pid_str) if pid_str.isdigit() else None,
                        "timestamp": float(timestamp_str) if timestamp_str.replace('.', '').isdigit() else None,
                        "lock_file": str(lock_file),
                        "is_stale": self._is_lock_stale(lock_file)
                    }
        except Exception as e:
            logger.warning("Error reading lock file", lock_file=str(lock_file), error=str(e))
        
        return {
            "url": url,
            "url_hash": url_hash,
            "lock_file": str(lock_file),
            "error": "Failed to read lock file",
            "is_stale": self._is_lock_stale(lock_file)
        }
    
    def force_unlock(self, url: str) -> bool:
        """
        强制解锁URL（谨慎使用）
        
        Args:
            url: 要解锁的URL
            
        Returns:
            True如果成功解锁，False如果没有锁或解锁失败
        """
        url_hash = generate_url_hash(url)
        lock_file = self.lock_dir / f"{url_hash}.lock"
        
        if not lock_file.exists():
            return False
        
        try:
            lock_file.unlink()
            logger.warning("Force unlocked URL", url=url, url_hash=url_hash)
            return True
        except Exception as e:
            logger.error("Failed to force unlock URL", url=url, error=str(e))
            return False
