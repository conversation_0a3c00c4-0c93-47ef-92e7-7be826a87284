"""
缓存管理器向后兼容性包装

提供向后兼容的API方法。
从 url_cache_manager.py 中提取，遵循代码行数限制。
"""

from typing import Dict, Any, Optional


class CacheCompatibilityMixin:
    """缓存管理器向后兼容性混入类"""
    
    async def mark_url_processed_simple(self, url: str, success: bool, metadata: Optional[Dict[str, Any]] = None) -> None:
        """向后兼容的简单标记方法"""
        await self.mark_url_processed_legacy(url, success, metadata)
    
    def get_processed_urls_count(self) -> int:
        """获取已处理URL数量（向后兼容）"""
        return len(self.processed_url_hashes)
