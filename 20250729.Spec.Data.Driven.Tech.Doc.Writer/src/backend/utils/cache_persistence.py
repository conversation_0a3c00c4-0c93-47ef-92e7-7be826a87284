"""
缓存持久化工具

处理缓存数据的加载、保存和迁移。
从 url_cache_manager.py 中提取，遵循代码行数限制。
"""

import json
import structlog
from typing import Dict, Any, Set
from pathlib import Path
from datetime import datetime
import aiofiles

from src.backend.utils.url_utils import generate_url_hash

logger = structlog.get_logger(__name__)


async def test_file_creation(file_path: Path) -> bool:
    """
    测试文件创建权限和能力

    Args:
        file_path: 要测试的文件路径

    Returns:
        True 如果可以创建文件，False 如果不能
    """
    test_file = file_path.with_suffix('.test')
    try:
        # 确保目录存在
        test_file.parent.mkdir(parents=True, exist_ok=True)

        # 尝试创建测试文件
        async with aiofiles.open(test_file, 'w', encoding='utf-8') as f:
            await f.write('test')

        # 清理测试文件
        if test_file.exists():
            test_file.unlink()

        logger.debug("File creation test successful", test_file=str(test_file))
        return True

    except Exception as e:
        logger.error("File creation test failed",
                    test_file=str(test_file),
                    error=str(e),
                    error_type=type(e).__name__)
        return False


async def load_url_status_cache(url_status_file: Path) -> tuple[Set[str], Dict[str, Dict[str, Any]]]:
    """
    加载URL状态缓存，支持新旧格式兼容
    
    Args:
        url_status_file: 缓存文件路径
        
    Returns:
        (processed_url_hashes, url_metadata) 元组
    """
    processed_url_hashes: Set[str] = set()
    url_metadata: Dict[str, Dict[str, Any]] = {}
    
    if not url_status_file.exists():
        logger.info("URL status file not found, starting with empty cache")
        return processed_url_hashes, url_metadata
    
    try:
        async with aiofiles.open(url_status_file, 'r', encoding='utf-8') as f:
            content = await f.read()
            if content.strip():
                data = json.loads(content)
                
                # 检查是否为新格式（使用MD5哈希作为键）
                if "processed_url_hashes" in data:
                    # 新格式
                    processed_url_hashes = set(data.get("processed_url_hashes", []))
                    url_metadata = data.get("url_metadata", {})
                    logger.info("Loaded new format cache")
                else:
                    # 旧格式兼容性处理
                    processed_url_hashes, url_metadata = await migrate_old_cache_format(data)
                    logger.info("Migrated old format cache to new format")
                
                logger.info(
                    "URL status cache loaded",
                    processed_url_hashes_count=len(processed_url_hashes)
                )
            
    except Exception as e:
        logger.warning("Failed to load URL status cache", error=str(e))
        # 继续使用空缓存
        processed_url_hashes = set()
        url_metadata = {}
    
    return processed_url_hashes, url_metadata


async def migrate_old_cache_format(old_data: Dict[str, Any]) -> tuple[Set[str], Dict[str, Dict[str, Any]]]:
    """
    迁移旧缓存格式到新格式
    
    Args:
        old_data: 旧格式的缓存数据
        
    Returns:
        (processed_url_hashes, url_metadata) 元组
    """
    processed_url_hashes: Set[str] = set()
    url_metadata: Dict[str, Dict[str, Any]] = {}
    
    old_processed_urls = set(old_data.get("processed_urls", []))
    old_url_metadata = old_data.get("url_metadata", {})
    
    for url in old_processed_urls:
        url_hash = generate_url_hash(url)
        processed_url_hashes.add(url_hash)
        
        # 迁移元数据
        old_metadata = old_url_metadata.get(url, {})
        url_metadata[url_hash] = {
            "original_url": url,
            "raw_content_path": None,  # 旧格式没有这些路径
            "markdown_path": None,
            "success": old_metadata.get("success", False),
            "timestamp": old_metadata.get("timestamp", datetime.now().isoformat()),
            "migrated_from_old_format": True
        }
    
    logger.info("Cache migration completed", migrated_urls=len(old_processed_urls))
    return processed_url_hashes, url_metadata


async def save_cache_data(
    url_status_file: Path,
    cache_metadata_file: Path,
    processed_url_hashes: Set[str],
    url_metadata: Dict[str, Dict[str, Any]],
    stats: Dict[str, Any]
) -> None:
    """
    保存缓存到文件（原子写入）

    Args:
        url_status_file: URL状态文件路径
        cache_metadata_file: 缓存元数据文件路径
        processed_url_hashes: 已处理的URL哈希集合
        url_metadata: URL元数据字典
        stats: 统计信息
    """
    try:
        # 确保目录存在
        url_status_file.parent.mkdir(parents=True, exist_ok=True)
        cache_metadata_file.parent.mkdir(parents=True, exist_ok=True)

        # 测试文件创建能力
        if not await test_file_creation(url_status_file):
            raise PermissionError(f"Cannot create files in directory: {url_status_file.parent}")

        # 准备数据 - 新格式
        cache_data = {
            "format_version": "2.0",  # 标识新格式
            "processed_url_hashes": list(processed_url_hashes),
            "url_metadata": url_metadata,
            "saved_at": datetime.now().isoformat()
        }

        # 原子写入URL状态文件
        temp_file = url_status_file.with_suffix('.tmp')

        temp_file_created = False
        try:
            # 写入临时文件
            try:
                async with aiofiles.open(temp_file, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(cache_data, indent=2, ensure_ascii=False))
                temp_file_created = True
                logger.debug("Temporary file created successfully", temp_file=str(temp_file))
            except (FileNotFoundError, PermissionError, OSError) as e:
                logger.error("Failed to create temporary file",
                           temp_file=str(temp_file),
                           error=str(e),
                           error_type=type(e).__name__)
                raise e

            # 原子移动 - 使用更安全的方法
            try:
                temp_file.replace(url_status_file)
                logger.debug("Atomic replace successful",
                           temp_file=str(temp_file),
                           target_file=str(url_status_file))
            except OSError as e:
                # 如果 replace 失败（可能是跨设备链接），尝试复制+删除
                logger.warning("Atomic replace failed, trying copy+delete",
                             error=str(e),
                             temp_file=str(temp_file),
                             target_file=str(url_status_file))
                try:
                    import shutil
                    shutil.copy2(temp_file, url_status_file)
                    temp_file.unlink()
                    logger.debug("Copy+delete fallback successful")
                except Exception as fallback_error:
                    logger.error("Copy+delete fallback also failed",
                               error=str(fallback_error),
                               error_type=type(fallback_error).__name__)
                    raise fallback_error

        except Exception as e:
            # 清理临时文件（只有在创建成功的情况下）
            if temp_file_created and temp_file.exists():
                try:
                    temp_file.unlink()
                    logger.debug("Temporary file cleaned up", temp_file=str(temp_file))
                except Exception as cleanup_error:
                    logger.warning("Failed to cleanup temporary file",
                                 temp_file=str(temp_file),
                                 error=str(cleanup_error))
            raise e

        # 保存统计元数据
        try:
            async with aiofiles.open(cache_metadata_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(stats, indent=2, ensure_ascii=False))
        except Exception as e:
            logger.warning("Failed to save cache metadata", error=str(e))
            # 不影响主要缓存保存

        logger.debug("Cache saved to disk",
                    url_status_file=str(url_status_file),
                    cache_metadata_file=str(cache_metadata_file))

    except Exception as e:
        logger.error("Failed to save cache",
                    error=str(e),
                    url_status_file=str(url_status_file),
                    cache_metadata_file=str(cache_metadata_file))


async def load_cache_metadata(cache_metadata_file: Path, stats: Dict[str, Any]) -> None:
    """
    加载缓存元数据
    
    Args:
        cache_metadata_file: 缓存元数据文件路径
        stats: 要更新的统计信息字典
    """
    if not cache_metadata_file.exists():
        return
    
    try:
        async with aiofiles.open(cache_metadata_file, 'r', encoding='utf-8') as f:
            content = await f.read()
            if content.strip():
                stats.update(json.loads(content))
                
    except Exception as e:
        logger.warning("Failed to load cache metadata", error=str(e))
