"""
环境变量解析工具

支持 ${VAR:-default} 语法的环境变量解析，
弥补 pydantic-settings 不支持此语法的问题。
"""

import os
import re
from typing import Any, Dict, Union


def parse_env_value(value: str) -> str:
    """
    解析包含环境变量的字符串值
    
    支持以下语法：
    - ${VAR} - 环境变量，如果不存在则保持原样
    - ${VAR:-default} - 环境变量，如果不存在或为空则使用默认值
    - ${VAR:+value} - 如果环境变量存在且非空则使用value，否则为空
    
    Args:
        value: 包含环境变量语法的字符串
        
    Returns:
        解析后的字符串
    """
    if not isinstance(value, str):
        return value
    
    # 匹配 ${VAR:-default} 或 ${VAR:+value} 或 ${VAR} 语法
    pattern = r'\$\{([^}:]+)(?::([+-])([^}]*))?\}'
    
    def replace_env_var(match):
        var_name = match.group(1)
        operator = match.group(2)  # '-' 或 '+' 或 None
        default_value = match.group(3) or ""
        
        env_value = os.getenv(var_name)
        
        if operator == '-':
            # ${VAR:-default} - 如果变量不存在或为空，使用默认值
            return env_value if env_value else default_value
        elif operator == '+':
            # ${VAR:+value} - 如果变量存在且非空，使用value，否则为空
            return default_value if env_value else ""
        else:
            # ${VAR} - 直接替换，如果不存在则保持原样
            return env_value if env_value is not None else match.group(0)
    
    return re.sub(pattern, replace_env_var, value)


def parse_config_dict(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    递归解析配置字典中的环境变量
    
    Args:
        config: 配置字典
        
    Returns:
        解析后的配置字典
    """
    if isinstance(config, dict):
        return {key: parse_config_dict(value) for key, value in config.items()}
    elif isinstance(config, list):
        return [parse_config_dict(item) for item in config]
    elif isinstance(config, str):
        return parse_env_value(config)
    else:
        return config


def expand_env_vars(text: str) -> str:
    """
    展开字符串中的环境变量（兼容性函数）
    
    Args:
        text: 包含环境变量的文本
        
    Returns:
        展开后的文本
    """
    return parse_env_value(text)


def validate_env_vars(*var_names: str) -> Dict[str, str]:
    """
    验证必需的环境变量是否存在
    
    Args:
        *var_names: 环境变量名称列表
        
    Returns:
        环境变量名称到值的映射
        
    Raises:
        ValueError: 如果任何必需的环境变量不存在
    """
    missing_vars = []
    env_vars = {}
    
    for var_name in var_names:
        value = os.getenv(var_name)
        if value is None:
            missing_vars.append(var_name)
        else:
            env_vars[var_name] = value
    
    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    return env_vars


def get_env_with_fallback(var_name: str, fallback: str = "", required: bool = False) -> str:
    """
    获取环境变量，支持回退值
    
    Args:
        var_name: 环境变量名称
        fallback: 回退值
        required: 是否为必需变量
        
    Returns:
        环境变量值或回退值
        
    Raises:
        ValueError: 如果是必需变量但不存在
    """
    value = os.getenv(var_name)
    
    if value is None:
        if required:
            raise ValueError(f"Required environment variable '{var_name}' is not set")
        return fallback
    
    return value


def parse_bool_env(var_name: str, default: bool = False) -> bool:
    """
    解析布尔类型的环境变量
    
    Args:
        var_name: 环境变量名称
        default: 默认值
        
    Returns:
        布尔值
    """
    value = os.getenv(var_name)
    if value is None:
        return default
    
    return value.lower() in ('true', '1', 'yes', 'on', 'enabled')


def parse_int_env(var_name: str, default: int = 0) -> int:
    """
    解析整数类型的环境变量
    
    Args:
        var_name: 环境变量名称
        default: 默认值
        
    Returns:
        整数值
    """
    value = os.getenv(var_name)
    if value is None:
        return default
    
    try:
        return int(value)
    except ValueError:
        return default


def parse_float_env(var_name: str, default: float = 0.0) -> float:
    """
    解析浮点数类型的环境变量
    
    Args:
        var_name: 环境变量名称
        default: 默认值
        
    Returns:
        浮点数值
    """
    value = os.getenv(var_name)
    if value is None:
        return default
    
    try:
        return float(value)
    except ValueError:
        return default


def parse_list_env(var_name: str, separator: str = ",", default: list = None) -> list:
    """
    解析列表类型的环境变量
    
    Args:
        var_name: 环境变量名称
        separator: 分隔符
        default: 默认值
        
    Returns:
        列表值
    """
    if default is None:
        default = []
    
    value = os.getenv(var_name)
    if value is None:
        return default
    
    return [item.strip() for item in value.split(separator) if item.strip()]


# 预定义的环境变量解析器
ENV_PARSERS = {
    'bool': parse_bool_env,
    'int': parse_int_env,
    'float': parse_float_env,
    'list': parse_list_env,
    'str': get_env_with_fallback,
}


def parse_typed_env(var_name: str, var_type: str = 'str', **kwargs) -> Any:
    """
    根据类型解析环境变量
    
    Args:
        var_name: 环境变量名称
        var_type: 变量类型 ('str', 'bool', 'int', 'float', 'list')
        **kwargs: 传递给解析器的额外参数
        
    Returns:
        解析后的值
    """
    parser = ENV_PARSERS.get(var_type, get_env_with_fallback)
    return parser(var_name, **kwargs)


# 常用环境变量的便捷函数
def get_debug_mode() -> bool:
    """获取调试模式"""
    return parse_bool_env('DEBUG', False)


def get_log_level() -> str:
    """获取日志级别"""
    return get_env_with_fallback('LOG_LEVEL', 'INFO')


def get_http_proxy() -> str:
    """获取HTTP代理URL"""
    return get_env_with_fallback('HTTP_PROXY', 'http://127.0.0.1:8118/')


def get_https_proxy() -> str:
    """获取HTTPS代理URL"""
    return get_env_with_fallback('HTTPS_PROXY', 'http://127.0.0.1:8118/')


def get_ssl_verify() -> bool:
    """获取SSL验证设置"""
    return parse_bool_env('VERIFY_SSL', True)
