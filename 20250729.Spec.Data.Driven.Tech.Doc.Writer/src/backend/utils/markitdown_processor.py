"""
Markitdown 处理器

用于处理 PDF 等特殊格式文件的转换。
从 web_content_processor_v2.py 中提取，遵循代码行数限制。
"""

import asyncio
import structlog
from typing import Dict, Any
from pathlib import Path

logger = structlog.get_logger(__name__)


class MarkitdownProcessor:
    """Markitdown 处理器，用于处理 PDF 等特殊格式"""
    
    def __init__(self):
        self.logger = structlog.get_logger(self.__class__.__name__)
        self._markitdown = None
    
    def _get_markitdown(self):
        """延迟加载 markitdown"""
        if self._markitdown is None:
            try:
                from markitdown import MarkItDown
                self._markitdown = MarkItDown(enable_plugins=False)
                self.logger.info("Markitdown initialized successfully")
            except ImportError as e:
                self.logger.error("Failed to import markitdown", error=str(e))
                raise ImportError("markitdown library is required. Install with: pip install 'markitdown[all]'")
        return self._markitdown
    
    async def convert_to_markdown(self, file_path: Path) -> Dict[str, Any]:
        """
        使用 markitdown 将文件转换为 Markdown
        
        Args:
            file_path: 文件路径
            
        Returns:
            转换结果字典
        """
        try:
            # 在线程池中运行同步的 markitdown 转换
            loop = asyncio.get_event_loop()
            markitdown = self._get_markitdown()
            
            result = await loop.run_in_executor(
                None, 
                lambda: markitdown.convert(str(file_path))
            )
            
            return {
                "success": True,
                "markdown_content": result.text_content,
                "title": getattr(result, 'title', None) or file_path.stem,
                "metadata": {
                    "converter": "markitdown",
                    "file_type": file_path.suffix.lower(),
                    "file_size": file_path.stat().st_size
                }
            }
            
        except Exception as e:
            self.logger.error("Markitdown conversion failed", file_path=str(file_path), error=str(e))
            return {
                "success": False,
                "error": str(e),
                "markdown_content": "",
                "title": file_path.stem
            }
    
    def is_supported_format(self, file_extension: str) -> bool:
        """
        检查文件格式是否被 markitdown 支持
        
        Args:
            file_extension: 文件扩展名（不包含点）
            
        Returns:
            True 如果支持，False 如果不支持
        """
        supported_formats = {
            'pdf', 'docx', 'doc', 'pptx', 'ppt', 'xlsx', 'xls',
            'epub', 'zip', 'csv', 'json', 'xml'
        }
        return file_extension.lower() in supported_formats
