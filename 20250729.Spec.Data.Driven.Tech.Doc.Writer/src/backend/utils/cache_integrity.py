"""
缓存完整性验证和清理工具

从 url_cache_manager.py 中提取，遵循代码行数限制。
"""

import structlog
from typing import Dict, Any
from pathlib import Path
from datetime import datetime

from src.backend.utils.url_utils import generate_url_hash

logger = structlog.get_logger(__name__)


async def verify_file_integrity(url: str, url_metadata: Dict[str, Dict[str, Any]]) -> Dict[str, bool]:
    """
    验证URL对应的文件是否存在且完整
    
    Args:
        url: 要验证的URL
        url_metadata: URL元数据字典
        
    Returns:
        文件完整性状态字典
    """
    url_hash = generate_url_hash(url)
    metadata = url_metadata.get(url_hash)
    
    if not metadata:
        return {"raw_content_exists": False, "markdown_exists": False, "metadata_exists": False}
    
    result = {"metadata_exists": True}
    
    # 检查原始内容文件
    raw_content_path = metadata.get("raw_content_path")
    if raw_content_path:
        raw_file = Path(raw_content_path)
        result["raw_content_exists"] = raw_file.exists() and raw_file.is_file()
    else:
        result["raw_content_exists"] = False
    
    # 检查Markdown文件
    markdown_path = metadata.get("markdown_path")
    if markdown_path:
        markdown_file = Path(markdown_path)
        result["markdown_exists"] = markdown_file.exists() and markdown_file.is_file()
    else:
        result["markdown_exists"] = False
    
    return result


async def cleanup_corrupted_files(url: str, url_metadata: Dict[str, Dict[str, Any]]) -> Dict[str, str]:
    """
    清理损坏的缓存文件
    
    Args:
        url: 要清理的URL
        url_metadata: URL元数据字典
        
    Returns:
        清理的文件路径字典
    """
    url_hash = generate_url_hash(url)
    metadata = url_metadata.get(url_hash, {})
    
    raw_content_path = metadata.get("raw_content_path")
    markdown_path = metadata.get("markdown_path")
    
    cleaned_files = {}
    
    # 删除原始内容文件
    if raw_content_path:
        try:
            raw_file = Path(raw_content_path)
            if raw_file.exists():
                raw_file.unlink()
                cleaned_files["raw_content"] = raw_content_path
        except Exception as e:
            logger.warning("Failed to delete raw content file", path=raw_content_path, error=str(e))
    
    # 删除Markdown文件
    if markdown_path:
        try:
            markdown_file = Path(markdown_path)
            if markdown_file.exists():
                markdown_file.unlink()
                cleaned_files["markdown"] = markdown_path
        except Exception as e:
            logger.warning("Failed to delete markdown file", path=markdown_path, error=str(e))
    
    return cleaned_files
