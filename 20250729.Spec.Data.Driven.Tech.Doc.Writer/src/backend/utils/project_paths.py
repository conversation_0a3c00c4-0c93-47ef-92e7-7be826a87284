"""
项目路径工具模块

基于配置文件的路径管理，确保路径基于项目根目录而不是当前工作目录。
遵循项目配置管理规范，通过配置文件确定根目录和相对路径。
"""

import os
from pathlib import Path
from typing import Union, Optional


def get_project_root() -> Path:
    """
    获取项目根目录

    通过查找包含特定标识文件的目录来确定项目根目录。
    这是一个轻量级的根目录检测，主要用于配置文件加载。

    Returns:
        项目根目录的Path对象

    Raises:
        RuntimeError: 如果无法找到项目根目录
    """
    # 从当前文件开始向上查找
    current_path = Path(__file__).resolve()

    # 项目根目录的标识文件
    root_markers = [
        "pyproject.toml",
        ".augmentrules",
        "main.py",
        "config.yaml.example"
    ]

    # 向上查找，最多查找10级目录
    for _ in range(10):
        current_path = current_path.parent

        # 检查是否包含任何标识文件
        for marker in root_markers:
            if (current_path / marker).exists():
                return current_path

    # 如果找不到，抛出异常
    raise RuntimeError(
        f"Could not find project root directory. "
        f"Looked for files: {root_markers}"
    )


def resolve_config_path(config_path: str, project_root: Optional[Path] = None) -> Path:
    """
    解析配置中的路径为绝对路径

    Args:
        config_path: 配置文件中的路径（可能是相对路径）
        project_root: 项目根目录，如果为None则自动检测

    Returns:
        解析后的绝对路径

    Example:
        resolve_config_path("./data/cache") -> /project/root/data/cache
        resolve_config_path("/absolute/path") -> /absolute/path
    """
    path = Path(config_path)

    # 如果已经是绝对路径，直接返回
    if path.is_absolute():
        return path

    # 相对路径需要基于项目根目录解析
    if project_root is None:
        project_root = get_project_root()

    return project_root / path


def ensure_config_dir(config_path: str, project_root: Optional[Path] = None) -> Path:
    """
    确保配置路径对应的目录存在

    Args:
        config_path: 配置文件中的路径
        project_root: 项目根目录，如果为None则自动检测

    Returns:
        创建后的目录路径
    """
    dir_path = resolve_config_path(config_path, project_root)
    dir_path.mkdir(parents=True, exist_ok=True)
    return dir_path


# 为了向后兼容，保留一些简化的函数
def get_project_path(*path_parts: Union[str, Path]) -> Path:
    """
    获取基于项目根目录的绝对路径（向后兼容）

    Args:
        *path_parts: 路径组件

    Returns:
        基于项目根目录的绝对路径
    """
    project_root = get_project_root()
    return project_root.joinpath(*path_parts)


# 配置驱动的路径管理
class ConfigBasedPathManager:
    """
    基于配置的路径管理器

    根据配置文件中的路径设置来管理项目路径，
    确保所有路径都基于项目根目录的绝对路径。
    """

    def __init__(self, config=None):
        """
        初始化路径管理器

        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config
        self._project_root = None

    @property
    def project_root(self) -> Path:
        """获取项目根目录"""
        if self._project_root is None:
            # 优先使用配置中的项目根目录
            if self.config and hasattr(self.config, 'project_root') and self.config.project_root:
                self._project_root = Path(self.config.project_root).resolve()
            else:
                # 如果配置中没有指定，则自动检测
                self._project_root = get_project_root()
        return self._project_root

    def get_cache_dir(self) -> Path:
        """获取缓存目录"""
        if self.config and hasattr(self.config, 'cache'):
            cache_path = self.config.cache.filesystem_base_path
        else:
            cache_path = "./data/cache"  # 默认值

        return resolve_config_path(cache_path, self.project_root)

    def get_output_dir(self) -> Path:
        """获取输出目录"""
        if self.config and hasattr(self.config, 'output_directory'):
            output_path = self.config.output_directory
        else:
            output_path = "./data/output"  # 默认值

        return resolve_config_path(output_path, self.project_root)

    def get_log_dir(self) -> Path:
        """获取日志目录"""
        if self.config and hasattr(self.config, 'logging'):
            log_path = Path(self.config.logging.file_path).parent
        else:
            log_path = "./log"  # 默认值

        return resolve_config_path(str(log_path), self.project_root)

    def get_data_dir(self) -> Path:
        """获取数据目录"""
        if self.config and hasattr(self.config, 'data_directory'):
            data_path = self.config.data_directory
        else:
            data_path = "./data"  # 默认值

        return resolve_config_path(data_path, self.project_root)


# 全局路径管理器实例（延迟初始化）
_path_manager: Optional[ConfigBasedPathManager] = None


def get_path_manager(config=None) -> ConfigBasedPathManager:
    """获取路径管理器实例"""
    global _path_manager
    if _path_manager is None or config is not None:
        _path_manager = ConfigBasedPathManager(config)
    return _path_manager
