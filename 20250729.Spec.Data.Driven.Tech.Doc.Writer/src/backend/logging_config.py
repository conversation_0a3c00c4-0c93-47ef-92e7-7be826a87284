"""
日志配置模块

负责初始化和配置项目的日志系统，包括：
- structlog 配置
- 日志级别设置
- 日志格式化
- 文件和控制台输出
- LiteLLM 日志集成
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path
from typing import Optional, Dict, Any

import structlog
from structlog.stdlib import LoggerFactory


def setup_logging(
    level: str = "INFO",
    format_type: str = "structured",
    console_enabled: bool = True,
    file_enabled: bool = True,
    file_path: str = "./log/app.log",
    file_max_size_mb: int = 100,
    file_backup_count: int = 5,
    component_levels: Optional[Dict[str, str]] = None
) -> None:
    """
    设置项目日志系统
    
    Args:
        level: 全局日志级别
        format_type: 日志格式类型 (structured/simple)
        console_enabled: 是否启用控制台输出
        file_enabled: 是否启用文件输出
        file_path: 日志文件路径
        file_max_size_mb: 日志文件最大大小(MB)
        file_backup_count: 日志文件备份数量
        component_levels: 组件特定日志级别
    """
    
    # 设置环境变量中的日志级别
    os.environ.setdefault('LOG_LEVEL', level)
    
    # 配置 LiteLLM 日志级别
    _setup_litellm_logging(level)
    
    # 创建日志目录
    if file_enabled:
        log_dir = Path(file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
    
    # 配置标准库日志
    _setup_stdlib_logging(
        level=level,
        console_enabled=console_enabled,
        file_enabled=file_enabled,
        file_path=file_path,
        file_max_size_mb=file_max_size_mb,
        file_backup_count=file_backup_count,
        component_levels=component_levels or {}
    )
    
    # 配置 structlog
    _setup_structlog(format_type=format_type)
    
    # 记录初始化成功
    logger = structlog.get_logger(__name__)
    logger.info("Logging system initialized successfully",
                level=level,
                format_type=format_type,
                console_enabled=console_enabled,
                file_enabled=file_enabled,
                file_path=file_path if file_enabled else None,
                litellm_log_level=os.environ.get('LITELLM_LOG'))


def _setup_litellm_logging(level: str) -> None:
    """设置 LiteLLM 日志级别"""
    level_upper = level.upper()
    
    if level_upper == 'DEBUG':
        os.environ['LITELLM_LOG'] = 'DEBUG'
    elif level_upper in ['INFO', 'WARNING']:
        os.environ['LITELLM_LOG'] = 'INFO'
    else:
        os.environ['LITELLM_LOG'] = 'ERROR'


def _setup_stdlib_logging(
    level: str,
    console_enabled: bool,
    file_enabled: bool,
    file_path: str,
    file_max_size_mb: int,
    file_backup_count: int,
    component_levels: Dict[str, str]
) -> None:
    """配置标准库日志"""
    
    # 获取日志级别
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    if console_enabled:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 文件处理器
    if file_enabled:
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=file_max_size_mb * 1024 * 1024,
            backupCount=file_backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # 设置组件特定日志级别
    for component, comp_level in component_levels.items():
        comp_logger = logging.getLogger(component)
        comp_log_level = getattr(logging, comp_level.upper(), logging.INFO)
        comp_logger.setLevel(comp_log_level)


def _setup_structlog(format_type: str) -> None:
    """配置 structlog"""
    
    # 选择处理器
    if format_type == "structured":
        processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ]
    else:  # simple
        processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.dev.ConsoleRenderer()
        ]
    
    # 配置 structlog
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """获取 structlog 日志记录器"""
    return structlog.get_logger(name)


def configure_from_config(config: Dict[str, Any]) -> None:
    """从配置字典配置日志系统"""
    logging_config = config.get('logging', {})
    
    setup_logging(
        level=logging_config.get('level', 'INFO'),
        format_type=logging_config.get('format', 'structured'),
        console_enabled=logging_config.get('console_enabled', True),
        file_enabled=logging_config.get('file_enabled', True),
        file_path=logging_config.get('file_path', './log/app.log'),
        file_max_size_mb=logging_config.get('file_max_size_mb', 100),
        file_backup_count=logging_config.get('file_backup_count', 5),
        component_levels=logging_config.get('loggers', {})
    )


# 自动初始化（如果环境变量存在）
def _auto_initialize():
    """自动初始化日志系统"""
    if os.getenv('LOG_LEVEL'):
        setup_logging(
            level=os.getenv('LOG_LEVEL', 'INFO'),
            format_type=os.getenv('LOG_FORMAT', 'structured'),
            console_enabled=os.getenv('LOG_CONSOLE_ENABLED', 'true').lower() == 'true',
            file_enabled=os.getenv('LOG_FILE_ENABLED', 'true').lower() == 'true',
            file_path=os.getenv('LOG_FILE_PATH', './log/app.log')
        )


# 模块导入时自动初始化
_auto_initialize()
