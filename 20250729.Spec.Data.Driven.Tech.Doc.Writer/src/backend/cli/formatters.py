"""
CLI输出格式化模块

负责格式化和显示CLI输出结果。
"""

from typing import Dict, Any
from rich.console import Console
from rich.table import Table

console = Console()


async def display_analysis_result(result: Dict[str, Any]) -> None:
    """显示分析结果"""
    analysis = result.get("analysis", {})
    keywords = result.get("keywords", {})
    
    # 创建结果表格
    table = Table(title="需求分析结果")
    table.add_column("项目", style="cyan")
    table.add_column("内容", style="white")
    
    # 添加分析结果
    table.add_row("解析主题", analysis.get("parsed_topic", ""))
    table.add_row("目标受众", analysis.get("target_audience", ""))
    table.add_row("复杂度评估", analysis.get("estimated_complexity", ""))
    table.add_row("推荐方法", analysis.get("recommended_approach", ""))
    
    # 添加调研目标
    objectives = analysis.get("research_objectives", [])
    if objectives:
        objectives_text = "\n".join(f"• {obj}" for obj in objectives)
        table.add_row("调研目标", objectives_text)
    
    # 添加关键词
    primary_keywords = keywords.get("primary_keywords", [])
    if primary_keywords:
        table.add_row("主要关键词", ", ".join(primary_keywords))
    
    console.print(table)


async def display_retrieval_result(result: Dict[str, Any]) -> None:
    """显示检索结果"""
    table = Table(title="信息检索结果")
    table.add_column("信息源", style="cyan")
    table.add_column("结果数量", style="green")
    table.add_column("状态", style="white")
    
    # 添加检索结果
    for source, data in result.items():
        if isinstance(data, dict):
            count = data.get("count", 0)
            status = "✓ 成功" if count > 0 else "- 无结果"
        else:
            count = len(data) if isinstance(data, list) else 0
            status = "✓ 成功" if count > 0 else "- 无结果"
        
        table.add_row(source, str(count), status)
    
    console.print(table)
