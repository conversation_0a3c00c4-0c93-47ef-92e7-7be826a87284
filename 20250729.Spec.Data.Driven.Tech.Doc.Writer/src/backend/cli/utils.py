"""
CLI工具函数模块

提供文件保存、JSON编码等工具函数。
"""

import json
import sys
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
from uuid import UUID

from src.backend.models.request import UserRequest
from .context import cli_context


class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理UUID和datetime对象"""

    def default(self, obj):
        if isinstance(obj, UUID):
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


async def save_analysis_result(result: Dict[str, Any], user_request: UserRequest) -> Path:
    """保存分析结果"""
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"analysis_{timestamp}.json"
    output_file = Path(cli_context.output_dir) / filename
    
    # 准备输出数据
    output_data = {
        "timestamp": datetime.now().isoformat(),
        "user_request": user_request.model_dump(),
        "analysis_result": result
    }
    
    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)
    
    return output_file


async def save_retrieval_result(result: Dict[str, Any], keywords: list) -> Path:
    """保存检索结果"""
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"retrieval_{timestamp}.json"
    output_file = Path(cli_context.output_dir) / filename
    
    # 准备输出数据
    output_data = {
        "timestamp": datetime.now().isoformat(),
        "keywords": keywords,
        "retrieval_result": result
    }
    
    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2, cls=CustomJSONEncoder)
    
    return output_file


def setup_event_loop():
    """设置事件循环（Windows兼容性）"""
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
