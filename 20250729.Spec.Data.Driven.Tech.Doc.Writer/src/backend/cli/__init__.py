"""
CLI模块

提供命令行界面功能。
"""

import os
import sys
import structlog

# 在导入任何其他模块之前设置环境变量来禁用 LiteLLM 清理
os.environ['LITELLM_DISABLE_ASYNC_CLEANUP'] = '1'

from .commands import cli
from .utils import setup_event_loop

# 配置日志
logger = structlog.get_logger(__name__)

# 禁用 LiteLLM 异步清理
def _disable_litellm_async_cleanup():
    """全局禁用 LiteLLM 的异步清理机制以防止 RuntimeWarning"""
    try:
        import atexit

        # 保存原始的 atexit.register 函数
        original_register = atexit.register

        def filtered_register(func, *args, **kwargs):
            """过滤的 atexit.register，阻止 LiteLLM 相关的清理函数注册"""
            func_name = getattr(func, '__name__', str(func))
            module_name = getattr(func, '__module__', 'unknown')

            # 检查是否是 LiteLLM 相关的清理函数
            if ('cleanup' in func_name.lower() and
                ('litellm' in module_name.lower() or 'async_client' in module_name.lower())):
                logger.debug(f"Blocked LiteLLM cleanup registration: {module_name}.{func_name}")
                return  # 不注册这个函数

            # 其他函数正常注册
            return original_register(func, *args, **kwargs)

        # 替换 atexit.register
        atexit.register = filtered_register

        # 现在导入 LiteLLM，这样它的清理函数就不会被注册
        import litellm

        # 同时也替换 register_async_client_cleanup 为无操作函数
        def disabled_cleanup():
            """禁用的清理函数，防止 RuntimeWarning"""
            pass

        litellm.register_async_client_cleanup = disabled_cleanup

        logger.debug("LiteLLM async cleanup globally disabled with atexit filtering")
        return True
    except ImportError:
        # LiteLLM 未安装，跳过
        return False
    except Exception as e:
        logger.warning(f"Failed to disable LiteLLM async cleanup: {e}")
        return False

# 配置异步支持
def _setup_async_support():
    """配置异步支持，允许嵌套事件循环"""
    try:
        import asyncio
        import nest_asyncio

        # 应用 nest_asyncio 补丁，允许嵌套事件循环
        nest_asyncio.apply()

        logger.info("Async support configured successfully",
                   nest_asyncio_applied=True,
                   event_loop_nesting_enabled=True)
        return True

    except ImportError as e:
        logger.warning("nest_asyncio not available, async support may be limited",
                      error=str(e),
                      recommendation="Install nest-asyncio: uv add nest-asyncio")
        return False
    except Exception as e:
        logger.error("Failed to configure async support",
                    error=str(e),
                    error_type=type(e).__name__)
        return False

# 初始化异步支持和禁用 LiteLLM 清理
_async_support_enabled = _setup_async_support()
_litellm_cleanup_disabled = _disable_litellm_async_cleanup()


def main():
    """主入口函数"""
    try:
        # 设置事件循环策略（Windows兼容性）
        setup_event_loop()

        # 记录异步支持状态
        logger.info("CLI starting",
                   async_support_enabled=_async_support_enabled,
                   litellm_cleanup_disabled=_litellm_cleanup_disabled,
                   event_loop_policy_configured=True)

        # 启动CLI
        cli()

    except KeyboardInterrupt:
        logger.info("User interrupted operation")
        sys.exit(0)
    except Exception as e:
        logger.error("CLI startup failed",
                    error=str(e),
                    error_type=type(e).__name__,
                    async_support_enabled=_async_support_enabled)
        sys.exit(1)


if __name__ == "__main__":
    main()


__all__ = ["cli", "main"]
