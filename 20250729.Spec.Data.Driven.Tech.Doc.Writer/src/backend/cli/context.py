"""
CLI上下文管理模块

管理CLI的全局状态和配置。
"""

from typing import Optional
from src.backend.config import Config


class CLIContext:
    """CLI上下文"""
    
    def __init__(self):
        self.config: Optional[Config] = None
        self.verbose: bool = False
        self.output_dir: str = "./data/output"  # 符合 .augmentrules 规范
        self.output_format: str = "markdown"
        self.config_path: Optional[str] = None


# 全局上下文
cli_context = CLIContext()
