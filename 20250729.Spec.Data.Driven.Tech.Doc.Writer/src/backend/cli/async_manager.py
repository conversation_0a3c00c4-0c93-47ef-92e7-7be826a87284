"""
异步资源管理模块

提供异步任务和资源的清理功能，防止 'cannot schedule new futures after shutdown' 错误。
"""

import asyncio
import concurrent.futures
import time
import threading
import weakref
from typing import Set, Optional
import structlog

logger = structlog.get_logger(__name__)


class AsyncResourceManager:
    """异步资源管理器"""
    
    def __init__(self):
        self._executors: Set[concurrent.futures.ThreadPoolExecutor] = set()
        self._tasks: Set[asyncio.Task] = set()
        self._cleanup_registered = False
        self._lock = threading.Lock()
    
    def register_executor(self, executor: concurrent.futures.ThreadPoolExecutor):
        """注册线程池执行器"""
        with self._lock:
            self._executors.add(executor)
    
    def register_task(self, task: asyncio.Task):
        """注册异步任务"""
        with self._lock:
            self._tasks.add(task)
            # 使用弱引用回调自动清理完成的任务
            task.add_done_callback(lambda t: self._tasks.discard(t))
    
    def cleanup_all(self, timeout: float = 2.0):
        """清理所有资源"""
        logger.debug("Starting async resource cleanup")
        
        # 清理任务
        self._cleanup_tasks(timeout)
        
        # 清理执行器
        self._cleanup_executors(timeout)
        
        logger.debug("Async resource cleanup completed")
    
    def _cleanup_tasks(self, timeout: float):
        """清理异步任务"""
        with self._lock:
            pending_tasks = [task for task in self._tasks if not task.done()]
        
        if not pending_tasks:
            return
        
        logger.debug(f"Cleaning up {len(pending_tasks)} pending tasks")
        
        try:
            # 尝试获取当前事件循环
            loop = asyncio.get_running_loop()
            
            # 给任务一些时间完成
            start_time = time.time()
            while pending_tasks and (time.time() - start_time) < timeout:
                pending_tasks = [task for task in pending_tasks if not task.done()]
                if pending_tasks:
                    time.sleep(0.1)
            
            # 取消剩余的任务
            for task in pending_tasks:
                if not task.done():
                    task.cancel()
                    
        except RuntimeError:
            # 没有运行中的事件循环
            pass
        except Exception as e:
            logger.warning(f"Error during task cleanup: {e}")
    
    def _cleanup_executors(self, timeout: float):
        """清理线程池执行器"""
        with self._lock:
            executors = list(self._executors)
        
        if not executors:
            return
        
        logger.debug(f"Cleaning up {len(executors)} thread pool executors")
        
        for executor in executors:
            try:
                executor.shutdown(wait=False)
            except Exception as e:
                logger.warning(f"Error shutting down executor: {e}")
        
        # 等待执行器关闭
        start_time = time.time()
        for executor in executors:
            remaining_time = timeout - (time.time() - start_time)
            if remaining_time > 0:
                try:
                    executor.shutdown(wait=True)
                except Exception:
                    pass


# 全局资源管理器实例
_resource_manager: Optional[AsyncResourceManager] = None


def get_resource_manager() -> AsyncResourceManager:
    """获取全局资源管理器实例"""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = AsyncResourceManager()
    return _resource_manager


def cleanup_async_resources(timeout: float = 2.0):
    """清理所有异步资源的便捷函数"""
    try:
        manager = get_resource_manager()
        manager.cleanup_all(timeout)
        
        # 额外的清理步骤
        _additional_cleanup()
        
    except Exception as e:
        logger.warning(f"Error during async resource cleanup: {e}")


def _additional_cleanup():
    """额外的清理步骤"""
    try:
        # 等待一小段时间让后台任务完成
        time.sleep(0.2)

        # 尝试清理 LiteLLM 相关的资源
        try:
            import litellm
            # 清空 LiteLLM 的回调以防止新的后台任务
            litellm.success_callback = []
            litellm.failure_callback = []

            # 尝试关闭 LiteLLM 的异步客户端
            try:
                # 检查是否有 close_litellm_async_clients 函数
                if hasattr(litellm, 'close_litellm_async_clients'):
                    # 在当前事件循环中运行清理
                    try:
                        loop = asyncio.get_running_loop()
                        # 创建一个任务来关闭客户端并正确等待
                        close_task = loop.create_task(litellm.close_litellm_async_clients())

                        # 使用 asyncio.wait_for 设置超时，避免无限等待
                        try:
                            # 等待任务完成，最多等待 1 秒
                            loop.run_until_complete(asyncio.wait_for(close_task, timeout=1.0))
                        except asyncio.TimeoutError:
                            # 超时则取消任务
                            close_task.cancel()
                            try:
                                loop.run_until_complete(close_task)
                            except asyncio.CancelledError:
                                pass
                        except Exception:
                            # 忽略其他异常
                            pass

                    except RuntimeError:
                        # 没有运行中的事件循环，尝试创建新的事件循环
                        try:
                            new_loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(new_loop)
                            new_loop.run_until_complete(
                                asyncio.wait_for(litellm.close_litellm_async_clients(), timeout=1.0)
                            )
                            new_loop.close()
                        except Exception:
                            # 忽略所有异常
                            pass
            except Exception:
                # 忽略异步客户端清理中的错误
                pass

            # 尝试访问和清理 LiteLLM 的内部执行器
            if hasattr(litellm, '_executor'):
                executor = getattr(litellm, '_executor', None)
                if executor and isinstance(executor, concurrent.futures.ThreadPoolExecutor):
                    executor.shutdown(wait=False)

        except ImportError:
            pass
        except Exception:
            # 忽略 LiteLLM 清理中的错误
            pass

        # 清理默认的线程池执行器
        try:
            loop = asyncio.get_running_loop()
            executor = getattr(loop, '_default_executor', None)
            if executor and isinstance(executor, concurrent.futures.ThreadPoolExecutor):
                executor.shutdown(wait=False)

            # 强制设置为 None 以防止后续使用
            if hasattr(loop, '_default_executor'):
                loop._default_executor = None

        except RuntimeError:
            # 没有运行中的事件循环
            pass
        except Exception:
            # 忽略清理过程中的错误
            pass

        # 额外等待时间让所有后台任务完成
        time.sleep(0.3)

    except Exception:
        # 清理过程中的任何错误都应该被忽略
        pass


def register_task(task: asyncio.Task):
    """注册异步任务以便清理"""
    manager = get_resource_manager()
    manager.register_task(task)


def register_executor(executor: concurrent.futures.ThreadPoolExecutor):
    """注册线程池执行器以便清理"""
    manager = get_resource_manager()
    manager.register_executor(executor)


class AsyncContextManager:
    """异步上下文管理器，确保资源正确清理"""
    
    def __init__(self, cleanup_timeout: float = 2.0):
        self.cleanup_timeout = cleanup_timeout
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        cleanup_async_resources(self.cleanup_timeout)


def with_async_cleanup(timeout: float = 2.0):
    """装饰器，为异步函数添加资源清理"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            finally:
                cleanup_async_resources(timeout)
        return wrapper
    return decorator
