"""
CLI命令定义模块

定义所有的CLI命令和参数。
"""

import asyncio
import click
import concurrent.futures
import time
from datetime import datetime
from typing import Dict, Any

from src.backend.models.request import UserRequest
from src.backend.agents.requirement_analyst import requirement_analysis_workflow
from src.backend.agents.information_retrieval import parallel_information_retrieval
from .context import cli_context
from .formatters import display_analysis_result, display_retrieval_result
from .utils import save_analysis_result, save_retrieval_result
from .async_manager import cleanup_async_resources


def _run_with_cleanup(coro):
    """运行协程并确保正确清理"""
    loop = None
    try:
        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # 运行协程
        return loop.run_until_complete(coro)

    finally:
        if loop:
            try:
                # 尝试清理 LiteLLM 的异步客户端
                try:
                    import litellm
                    if hasattr(litellm, 'close_litellm_async_clients'):
                        # 在关闭循环前清理 LiteLLM 客户端
                        loop.run_until_complete(litellm.close_litellm_async_clients())
                except Exception:
                    # 忽略 LiteLLM 清理中的错误
                    pass

                # 等待一段时间让后台任务完成
                time.sleep(0.3)

                # 取消所有剩余的任务
                pending = asyncio.all_tasks(loop)
                for task in pending:
                    task.cancel()

                # 等待取消的任务完成
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

                # 关闭事件循环
                loop.close()

            except Exception:
                # 忽略清理过程中的错误
                pass


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='配置文件路径')
@click.option('--verbose', '-v', is_flag=True, help='详细输出')
@click.option('--output-dir', '-o', type=click.Path(), default=None, help='输出目录（覆盖配置文件设置）')
@click.option('--format', '-f', type=click.Choice(['markdown', 'html', 'pdf']), default='markdown', help='输出格式')
@click.pass_context
def cli(ctx, config, verbose, output_dir, format):
    """多智能体技术文档写作系统"""
    ctx.ensure_object(dict)

    # 加载配置文件
    if config:
        cli_context.config_path = config

    # 尝试加载配置以获取默认输出目录
    try:
        from src.backend.config import get_config
        import asyncio

        # 在同步上下文中获取配置
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，使用默认值
                config_output_dir = "./data/output"
            else:
                config_obj = loop.run_until_complete(get_config())
                config_output_dir = config_obj.output_directory
        except RuntimeError:
            # 没有事件循环，创建一个临时的
            config_obj = asyncio.run(get_config())
            config_output_dir = config_obj.output_directory
    except Exception:
        # 配置加载失败，使用默认值
        config_output_dir = "./data/output"

    # 设置上下文：CLI参数优先，否则使用配置文件，最后使用默认值
    cli_context.verbose = verbose
    cli_context.output_dir = output_dir if output_dir is not None else config_output_dir
    cli_context.output_format = format

    # 创建输出目录
    import os
    os.makedirs(cli_context.output_dir, exist_ok=True)


@cli.command()
@click.argument('topic', required=True)
@click.option('--scope', '-s', help='调研范围和深度要求（可选，系统会根据主题自动生成）')
@click.option('--format', '-f', default='技术调研报告', help='输出格式要求')
@click.option('--deadline', '-d', help='截止时间 (YYYY-MM-DD)')
@click.option('--language', '-l', default='zh-CN', help='输出语言')
@click.option('--additional', '-a', help='额外要求')
def analyze(topic, scope, format, deadline, language, additional):
    """分析用户需求并生成调研计划"""

    async def _analyze_async():
        """异步分析函数"""
        # 如果没有提供scope，根据topic自动生成
        actual_scope = scope if scope else f"全面分析{topic}的技术现状、发展趋势、应用场景和未来前景"

        # 创建用户请求
        user_request = UserRequest(
            topic=topic,
            scope=actual_scope,
            format=format,
            deadline=datetime.strptime(deadline, "%Y-%m-%d").date() if deadline else None,
            language=language,
            additional_requirements=additional
        )

        try:
            from rich.console import Console
            from rich.panel import Panel
            from rich.progress import Progress, SpinnerColumn, TextColumn

            console = Console()
            
            # 显示开始信息
            console.print(Panel.fit(f"[bold blue]开始分析需求[/bold blue]\n主题: {topic}", title="需求分析"))

            # 显示进度
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
                transient=True
            ) as progress:
                task = progress.add_task("正在分析需求...", total=None)
                
                # 执行需求分析工作流
                result = await requirement_analysis_workflow(user_request)
                
                progress.update(task, description="分析完成")

            # 显示结果
            await display_analysis_result(result)

            # 保存结果
            output_file = await save_analysis_result(result, user_request)
            console.print(f"\n[green]✓[/green] 分析结果已保存到: {output_file}")

        except Exception as e:
            console.print(f"[red]✗[/red] 分析失败: {e}")
            if cli_context.verbose:
                console.print_exception()

    # 运行异步函数，确保正确清理
    try:
        # 使用自定义的事件循环运行器，更好地控制清理过程
        _run_with_cleanup(_analyze_async())
    finally:
        # 确保所有异步任务完成并清理资源
        cleanup_async_resources(timeout=3.0)


@cli.command()
@click.argument('keywords', nargs=-1, required=True)
@click.option('--sources', '-s', multiple=True,
              type=click.Choice(['academic', 'industry', 'news', 'government', 'opensource']),
              help='指定信息源')
@click.option('--parallel', '-p', is_flag=True, default=True, help='启用并行检索')
def retrieve(keywords, sources, parallel):
    """并行检索信息"""

    async def _retrieve_async():
        """异步检索函数"""
        keywords_list = list(keywords)
        sources_list = list(sources) if sources else ['academic', 'industry', 'news']

        try:
            from rich.console import Console
            from rich.panel import Panel
            from rich.progress import Progress, SpinnerColumn, TextColumn

            console = Console()
            
            # 显示开始信息
            console.print(Panel.fit(
                f"[bold blue]开始信息检索[/bold blue]\n"
                f"关键词: {', '.join(keywords_list)}\n"
                f"信息源: {', '.join(sources_list)}\n"
                f"并行模式: {'启用' if parallel else '禁用'}",
                title="信息检索"
            ))

            # 显示进度
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
                transient=True
            ) as progress:
                task = progress.add_task("正在检索信息...", total=None)
                
                # 执行信息检索工作流
                result = await parallel_information_retrieval(keywords_list)
                
                progress.update(task, description="检索完成")

            # 显示结果
            await display_retrieval_result(result)

            # 保存结果
            output_file = await save_retrieval_result(result, keywords_list)
            console.print(f"\n[green]✓[/green] 检索结果已保存到: {output_file}")

        except Exception as e:
            console.print(f"[red]✗[/red] 检索失败: {e}")
            if cli_context.verbose:
                console.print_exception()

    # 运行异步函数，确保正确清理
    try:
        # 使用自定义的事件循环运行器，更好地控制清理过程
        _run_with_cleanup(_retrieve_async())
    finally:
        # 确保所有异步任务完成并清理资源
        cleanup_async_resources(timeout=3.0)


@cli.command()
def status():
    """查询系统状态"""
    
    from rich.console import Console
    from rich.panel import Panel
    from rich.table import Table
    
    console = Console()
    console.print(Panel.fit("[bold blue]系统状态[/bold blue]", title="状态查询"))
    
    try:
        # 创建状态表格
        table = Table(title="系统组件状态")
        table.add_column("组件", style="cyan")
        table.add_column("状态", style="green")
        table.add_column("版本/信息", style="white")

        # 检查各组件状态
        table.add_row("Python", "✓ 正常", f"Python {'.'.join(map(str, __import__('sys').version_info[:3]))}")
        
        try:
            import structlog
            table.add_row("日志系统", "✓ 正常", f"structlog {structlog.__version__}")
        except ImportError:
            table.add_row("日志系统", "✗ 异常", "structlog 未安装")

        try:
            import click
            table.add_row("CLI框架", "✓ 正常", f"click {click.__version__}")
        except ImportError:
            table.add_row("CLI框架", "✗ 异常", "click 未安装")

        try:
            import rich
            try:
                version = rich.__version__
            except AttributeError:
                # 某些版本的 rich 可能没有 __version__ 属性
                version = "已安装"
            table.add_row("界面框架", "✓ 正常", f"rich {version}")
        except ImportError:
            table.add_row("界面框架", "✗ 异常", "rich 未安装")

        # 检查 tracemalloc 状态
        try:
            from src.backend.debug import get_tracemalloc_manager
            manager = get_tracemalloc_manager()
            status = manager.get_status()

            if status["enabled"]:
                memory_info = ""
                if "current_memory_bytes" in status:
                    current_mb = status["current_memory_bytes"] / 1024 / 1024
                    peak_mb = status["peak_memory_bytes"] / 1024 / 1024
                    memory_info = f"当前: {current_mb:.1f}MB, 峰值: {peak_mb:.1f}MB"
                table.add_row("内存跟踪", "✓ 启用", memory_info)
            else:
                if status["env_enabled"] is not None:
                    enabled_by = "环境变量"
                elif status["config_enabled"]:
                    enabled_by = "配置文件"
                else:
                    enabled_by = "默认"
                table.add_row("内存跟踪", "○ 禁用", f"由{enabled_by}控制")
        except Exception:
            table.add_row("内存跟踪", "? 未知", "无法获取状态")

        console.print(table)
        console.print(f"\n[green]✓[/green] 系统运行正常")

    except Exception as e:
        console.print(f"[red]✗[/red] 状态查询失败: {e}")
        if cli_context.verbose:
            console.print_exception()
