"""
LLM提供者模块

使用 LiteLLM 实现统一的多种LLM服务接口，支持：
- Google Gemini
- OpenAI GPT
- DeepSeek
- 其他100+种LLM提供者
"""

import asyncio
import json
import os
import time
from typing import Dict, Any, List, Optional
import structlog

# 确保日志配置已初始化
try:
    from src.backend.logging_config import get_logger
    logger = get_logger(__name__)
except ImportError:
    # 回退到标准 structlog
    logger = structlog.get_logger(__name__)

# 导入 LiteLLM 专用日志处理器
try:
    from src.backend.llm.logging_handler import (
        log_llm_request,
        log_llm_response,
        log_llm_error
    )
except ImportError:
    # 如果导入失败，创建空函数
    def log_llm_request(*args, **kwargs): pass
    def log_llm_response(*args, **kwargs): pass
    def log_llm_error(*args, **kwargs): pass


class LiteLLMProvider:
    """基于 LiteLLM 的统一LLM提供者"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.provider = config.get('provider', 'unknown')
        self.logger = logger.bind(provider=self.provider)

        # 设置代理配置
        self._setup_proxy()

        # 设置LiteLLM配置
        self._setup_litellm()

    def _setup_proxy(self):
        """设置代理配置"""
        # 获取代理配置
        https_proxy = os.getenv('HTTPS_PROXY') or os.getenv('https_proxy')
        http_proxy = os.getenv('HTTP_PROXY') or os.getenv('http_proxy')

        if https_proxy or http_proxy:
            proxy_url = https_proxy or http_proxy
            self.logger.debug(f"Using proxy: {proxy_url}")

            # 为LiteLLM设置代理环境变量
            if https_proxy:
                os.environ['HTTPS_PROXY'] = https_proxy
            if http_proxy:
                os.environ['HTTP_PROXY'] = http_proxy

    def _setup_litellm(self):
        """设置LiteLLM配置"""
        try:
            import litellm

            # 设置API密钥
            api_key = self.config.get('api_key')
            if api_key:
                if self.provider == 'gemini':
                    os.environ['GEMINI_API_KEY'] = api_key
                elif self.provider == 'openai':
                    os.environ['OPENAI_API_KEY'] = api_key
                elif self.provider == 'deepseek':
                    # DeepSeek使用OpenAI兼容接口
                    os.environ['OPENAI_API_KEY'] = api_key

            # 设置基础URL（如果有）
            base_url = self.config.get('base_url')
            if base_url and self.provider == 'deepseek':
                os.environ['OPENAI_API_BASE'] = base_url

            # 设置LiteLLM日志级别（使用新的环境变量方式）
            # 优先使用已设置的 LITELLM_LOG，否则根据当前日志级别设置
            if 'LITELLM_LOG' not in os.environ:
                current_log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
                if current_log_level == 'DEBUG':
                    os.environ['LITELLM_LOG'] = 'DEBUG'
                elif current_log_level in ['INFO', 'WARNING']:
                    os.environ['LITELLM_LOG'] = 'INFO'
                else:
                    os.environ['LITELLM_LOG'] = 'ERROR'

            # 配置 LiteLLM 以减少异步任务冲突
            # 禁用某些可能导致后台任务的功能
            litellm.drop_params = True  # 自动删除不支持的参数
            litellm.telemetry = False   # 禁用遥测以减少后台任务

            # 禁用异步日志回调以避免 ThreadPoolExecutor 问题
            litellm.success_callback = []  # 清空成功回调
            litellm.failure_callback = []  # 清空失败回调

            # 设置更短的超时以减少后台任务持续时间
            litellm.request_timeout = 300

            # 禁用 LiteLLM 的异步清理机制以防止 RuntimeWarning
            # 检查环境变量控制
            if os.environ.get('LITELLM_DISABLE_ASYNC_CLEANUP') == '1':
                def disabled_async_cleanup():
                    """
                    禁用的异步清理函数，用于替换 LiteLLM 的 register_async_client_cleanup。
                    这可以防止程序退出时的 RuntimeWarning: coroutine never awaited。
                    """
                    pass

                # 应用猴子补丁
                litellm.register_async_client_cleanup = disabled_async_cleanup
                self.logger.debug("LiteLLM async cleanup disabled via environment variable")
            else:
                self.logger.debug("LiteLLM async cleanup enabled (default behavior)")

            # 禁用已弃用的 set_verbose 设置
            # 注意：不再使用 litellm.set_verbose，改用环境变量控制

            self.logger.debug("LiteLLM configured successfully",
                            provider=self.provider,
                            litellm_log_level=os.environ.get('LITELLM_LOG'),
                            telemetry_disabled=True,
                            async_cleanup_disabled=True,
                            deprecated_verbose_disabled=True)

        except ImportError:
            raise ImportError("LiteLLM package not installed. Run: pip install litellm")

    async def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """使用LiteLLM生成响应"""
        start_time = time.time()
        model = self._get_litellm_model_name()

        # 记录请求日志
        log_llm_request(
            provider=self.provider,
            model=model,
            messages=messages,
            **kwargs
        )

        try:
            import litellm

            # 为推理模型调整参数
            max_tokens = kwargs.get('max_tokens', 4096)
            if self.provider == 'gemini' and '2.5' in self.config.get('model', ''):
                # Gemini 2.5 系列是推理模型，需要更多token用于思考和输出
                max_tokens = max(max_tokens, 8192)
                self.logger.debug(f"Adjusted max_tokens for Gemini 2.5: {max_tokens}")

            # 调用LiteLLM，使用 asyncio.wait_for 确保超时控制
            timeout = kwargs.get('timeout', 300)

            response = await asyncio.wait_for(
                litellm.acompletion(
                    model=model,
                    messages=messages,
                    temperature=kwargs.get('temperature', 0.1),
                    max_tokens=max_tokens,
                    timeout=timeout
                ),
                timeout=timeout + 10  # 给额外的缓冲时间
            )

            content = response.choices[0].message.content
            duration = time.time() - start_time

            # 处理空内容的情况
            if not content or content.strip() == "":
                self.logger.warning(f"Empty response from {model}, finish_reason: {response.choices[0].finish_reason}")
                # 对于推理模型，如果内容为空，返回一个提示信息
                if self.provider == 'gemini' and '2.5' in self.config.get('model', ''):
                    content = "模型正在思考中，请稍后重试或使用更大的max_tokens参数。"
                else:
                    content = "响应为空，请重试。"

            # 记录成功响应日志
            log_llm_response(
                provider=self.provider,
                model=model,
                messages=messages,
                response=content,
                duration=duration,
                **kwargs
            )

            return content

        except asyncio.TimeoutError:
            duration = time.time() - start_time
            error_msg = f"请求超时（{timeout}秒），请稍后重试"

            # 记录错误日志
            log_llm_error(
                provider=self.provider,
                model=model,
                messages=messages,
                error=f"Timeout after {timeout} seconds",
                duration=duration,
                **kwargs
            )

            self.logger.error(f"LiteLLM request timeout after {timeout} seconds")
            raise Exception(error_msg)
        except Exception as e:
            duration = time.time() - start_time
            error_msg = str(e)

            # 记录错误日志
            log_llm_error(
                provider=self.provider,
                model=model,
                messages=messages,
                error=error_msg,
                duration=duration,
                **kwargs
            )

            self.logger.error(f"LiteLLM API error: {e}")
            raise

    def _get_litellm_model_name(self) -> str:
        """获取LiteLLM格式的模型名称"""
        model = self.config.get('model', '')

        if self.provider == 'gemini':
            # Gemini模型格式：gemini/gemini-pro
            if not model.startswith('gemini/'):
                return f"gemini/{model}"
            return model
        elif self.provider == 'deepseek':
            # DeepSeek使用OpenAI兼容接口
            return model
        elif self.provider == 'openai':
            return model
        else:
            return model

    def validate_config(self) -> bool:
        """验证配置"""
        return bool(self.config.get('api_key'))


class LLMProviderFactory:
    """LLM提供者工厂类"""

    @staticmethod
    def create_provider(config: Dict[str, Any]) -> LiteLLMProvider:
        """根据配置创建LLM提供者"""
        return LiteLLMProvider(config)


# 全局LLM提供者实例
_primary_provider: Optional[LiteLLMProvider] = None
_fallback_provider: Optional[LiteLLMProvider] = None


async def get_llm_provider(use_fallback: bool = False) -> LiteLLMProvider:
    """获取LLM提供者实例"""
    global _primary_provider, _fallback_provider

    if use_fallback and _fallback_provider:
        return _fallback_provider
    elif _primary_provider:
        return _primary_provider
    else:
        # 如果没有初始化，尝试从配置中创建
        from src.backend.config import get_config
        config = await get_config()

        # 创建主提供者
        if config.llm_primary:
            _primary_provider = LLMProviderFactory.create_provider(config.llm_primary.model_dump())

        # 创建备用提供者
        if config.llm_fallback:
            _fallback_provider = LLMProviderFactory.create_provider(config.llm_fallback.model_dump())

        return _primary_provider if not use_fallback else (_fallback_provider or _primary_provider)


async def generate_llm_response(messages: List[Dict[str, str]], **kwargs) -> str:
    """生成LLM响应的便捷函数"""
    try:
        provider = await get_llm_provider()
        return await provider.generate_response(messages, **kwargs)
    except Exception as e:
        logger.warning(f"Primary LLM provider failed: {e}, trying fallback")
        try:
            fallback_provider = await get_llm_provider(use_fallback=True)
            if fallback_provider != provider:  # 确保不是同一个提供者
                return await fallback_provider.generate_response(messages, **kwargs)
        except Exception as fallback_error:
            logger.error(f"Fallback LLM provider also failed: {fallback_error}")
        raise e
