"""
LiteLLM 日志处理模块

根据 .augmentrules 要求实现 LiteLLM 的专用日志功能：
- 遵循统一的日志级别
- 将日志保存到指定目录下
- 以 little_llm.YYYYMMDD.log 为文件名
- 将所有和 LLM 的交互以日志形式保存下来
"""

import os
import json
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import structlog

try:
    from src.backend.logging_config import get_logger
    logger = get_logger(__name__)
except ImportError:
    logger = structlog.get_logger(__name__)


class LiteLLMLoggingHandler:
    """LiteLLM 专用日志处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 LiteLLM 日志处理器
        
        Args:
            config: LiteLLM 日志配置
        """
        self.config = config
        self.enabled = config.get('enabled', True)
        self.level = config.get('level', 'INFO')
        self.directory = Path(config.get('directory', './log'))
        self.filename_pattern = config.get('filename_pattern', 'little_llm.{date}.log')
        self.max_size_mb = config.get('max_size_mb', 50)
        self.backup_count = config.get('backup_count', 30)
        self.include_request_response = config.get('include_request_response', True)
        self.include_metadata = config.get('include_metadata', True)
        self.json_format = config.get('json_format', True)
        
        # 创建日志目录
        self.directory.mkdir(parents=True, exist_ok=True)
        
        # 初始化日志记录器
        self._setup_logger()
        
        logger.info("LiteLLM logging handler initialized",
                   enabled=self.enabled,
                   directory=str(self.directory),
                   level=self.level,
                   json_format=self.json_format)
    
    def _setup_logger(self):
        """设置专用的 LiteLLM 日志记录器"""
        if not self.enabled:
            return
            
        # 创建专用的日志记录器
        self.litellm_logger = logging.getLogger('litellm_interactions')
        self.litellm_logger.setLevel(getattr(logging, self.level.upper(), logging.INFO))
        
        # 清除现有处理器
        for handler in self.litellm_logger.handlers[:]:
            self.litellm_logger.removeHandler(handler)
        
        # 创建按日期轮转的文件处理器
        today = datetime.now().strftime('%Y%m%d')
        log_filename = self.filename_pattern.format(date=today)
        log_filepath = self.directory / log_filename
        
        # 使用 TimedRotatingFileHandler 按日期轮转
        file_handler = logging.handlers.TimedRotatingFileHandler(
            filename=str(log_filepath),
            when='midnight',
            interval=1,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        # 设置文件名后缀格式
        file_handler.suffix = '%Y%m%d'
        
        # 设置日志格式
        if self.json_format:
            formatter = logging.Formatter('%(message)s')
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        
        file_handler.setFormatter(formatter)
        file_handler.setLevel(getattr(logging, self.level.upper(), logging.INFO))
        
        self.litellm_logger.addHandler(file_handler)
        
        # 防止日志传播到根日志记录器
        self.litellm_logger.propagate = False
    
    def log_llm_interaction(self, 
                           interaction_type: str,
                           provider: str,
                           model: str,
                           messages: List[Dict[str, str]],
                           response: Optional[str] = None,
                           metadata: Optional[Dict[str, Any]] = None,
                           error: Optional[str] = None):
        """
        记录 LLM 交互日志
        
        Args:
            interaction_type: 交互类型 (request, response, error)
            provider: LLM 提供商
            model: 模型名称
            messages: 请求消息
            response: 响应内容
            metadata: 元数据（耗时、token数等）
            error: 错误信息
        """
        if not self.enabled:
            return
            
        timestamp = datetime.now().isoformat()
        
        log_entry = {
            'timestamp': timestamp,
            'interaction_type': interaction_type,
            'provider': provider,
            'model': model
        }
        
        # 添加请求信息
        if self.include_request_response and messages:
            log_entry['request'] = {
                'messages': messages,
                'message_count': len(messages),
                'total_chars': sum(len(msg.get('content', '')) for msg in messages)
            }
        
        # 添加响应信息
        if self.include_request_response and response:
            log_entry['response'] = {
                'content': response,
                'char_count': len(response)
            }
        
        # 添加元数据
        if self.include_metadata and metadata:
            log_entry['metadata'] = metadata
        
        # 添加错误信息
        if error:
            log_entry['error'] = error
            log_entry['success'] = False
        else:
            log_entry['success'] = True
        
        # 记录日志
        if self.json_format:
            log_message = json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))
        else:
            log_message = f"[{interaction_type.upper()}] {provider}/{model} - {response[:100] if response else error}"
        
        if error:
            self.litellm_logger.error(log_message)
        else:
            self.litellm_logger.info(log_message)
    
    def log_request(self, provider: str, model: str, messages: List[Dict[str, str]], **kwargs):
        """记录 LLM 请求"""
        metadata = {
            'temperature': kwargs.get('temperature'),
            'max_tokens': kwargs.get('max_tokens'),
            'timeout': kwargs.get('timeout')
        }
        
        self.log_llm_interaction(
            interaction_type='request',
            provider=provider,
            model=model,
            messages=messages,
            metadata=metadata
        )
    
    def log_response(self, provider: str, model: str, messages: List[Dict[str, str]], 
                    response: str, duration: float, **kwargs):
        """记录 LLM 响应"""
        metadata = {
            'duration_seconds': duration,
            'response_length': len(response),
            'temperature': kwargs.get('temperature'),
            'max_tokens': kwargs.get('max_tokens')
        }
        
        self.log_llm_interaction(
            interaction_type='response',
            provider=provider,
            model=model,
            messages=messages,
            response=response,
            metadata=metadata
        )
    
    def log_error(self, provider: str, model: str, messages: List[Dict[str, str]], 
                 error: str, **kwargs):
        """记录 LLM 错误"""
        metadata = {
            'temperature': kwargs.get('temperature'),
            'max_tokens': kwargs.get('max_tokens'),
            'timeout': kwargs.get('timeout')
        }
        
        self.log_llm_interaction(
            interaction_type='error',
            provider=provider,
            model=model,
            messages=messages,
            error=error,
            metadata=metadata
        )


# 全局日志处理器实例
_litellm_logging_handler: Optional[LiteLLMLoggingHandler] = None


def get_litellm_logging_handler() -> Optional[LiteLLMLoggingHandler]:
    """获取 LiteLLM 日志处理器实例"""
    return _litellm_logging_handler


def initialize_litellm_logging(config: Dict[str, Any]) -> LiteLLMLoggingHandler:
    """初始化 LiteLLM 日志处理器"""
    global _litellm_logging_handler
    
    litellm_config = config.get('logging', {}).get('litellm', {})
    _litellm_logging_handler = LiteLLMLoggingHandler(litellm_config)
    
    return _litellm_logging_handler


def log_llm_request(provider: str, model: str, messages: List[Dict[str, str]], **kwargs):
    """记录 LLM 请求的便捷函数"""
    handler = get_litellm_logging_handler()
    if handler:
        handler.log_request(provider, model, messages, **kwargs)


def log_llm_response(provider: str, model: str, messages: List[Dict[str, str]], 
                    response: str, duration: float, **kwargs):
    """记录 LLM 响应的便捷函数"""
    handler = get_litellm_logging_handler()
    if handler:
        handler.log_response(provider, model, messages, response, duration, **kwargs)


def log_llm_error(provider: str, model: str, messages: List[Dict[str, str]], 
                 error: str, **kwargs):
    """记录 LLM 错误的便捷函数"""
    handler = get_litellm_logging_handler()
    if handler:
        handler.log_error(provider, model, messages, error, **kwargs)
