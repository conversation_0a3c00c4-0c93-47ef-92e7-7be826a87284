"""
工作流编排器核心类

负责整体工作流的编排和执行。
"""

from typing import Dict, Any, List
import structlog

from src.backend.config import Config
from .models import TaskDefinition, TaskResult
from .scheduler import TaskScheduler

logger = structlog.get_logger(__name__)


class WorkflowOrchestrator:
    """工作流编排器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.task_scheduler = TaskScheduler(config)
        self.logger = logger.bind(component="workflow_orchestrator")
    
    async def execute_workflow(self, workflow_definition: Dict[str, Any]) -> Dict[str, TaskResult]:
        """执行工作流"""
        self.logger.info("Starting workflow execution", workflow_id=workflow_definition.get("id", "unknown"))
        
        # 解析工作流定义
        tasks = self._parse_workflow_definition(workflow_definition)
        
        # 执行任务
        results = await self.task_scheduler.execute_tasks(tasks, self.config)
        
        self.logger.info("Workflow execution completed", 
                        total_tasks=len(tasks),
                        successful_tasks=len([r for r in results.values() if r.status == "completed"]),
                        failed_tasks=len([r for r in results.values() if r.status == "failed"]))
        
        return results
    
    def _parse_workflow_definition(self, workflow_def: Dict[str, Any]) -> List[TaskDefinition]:
        """解析工作流定义"""
        tasks = []
        task_definitions = workflow_def.get("tasks", [])
        
        for task_def in task_definitions:
            task = TaskDefinition(
                task_id=task_def["task_id"],
                task_type=task_def["task_type"],
                agent_type=task_def["agent_type"],
                input_data=task_def["input_data"],
                dependencies=task_def.get("dependencies", []),
                priority=task_def.get("priority", 1),
                timeout_seconds=task_def.get("timeout_seconds", self.config.workflow.default_task_timeout_seconds),
                retry_count=task_def.get("retry_count", self.config.workflow.default_retry_count),
                metadata=task_def.get("metadata", {})
            )
            tasks.append(task)
        
        self.logger.debug("Workflow definition parsed", tasks_count=len(tasks))
        return tasks
