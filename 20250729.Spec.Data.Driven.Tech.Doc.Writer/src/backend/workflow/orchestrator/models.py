"""
工作流编排器数据模型

定义任务、结果和依赖图的数据结构。
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, Optional, List
import structlog

logger = structlog.get_logger(__name__)


@dataclass
class TaskDefinition:
    """任务定义"""
    task_id: str
    task_type: str
    agent_type: str
    input_data: Dict[str, Any]
    dependencies: List[str] = field(default_factory=list)
    priority: int = 1
    timeout_seconds: Optional[int] = None  # 将从配置中获取默认值
    retry_count: Optional[int] = None  # 将从配置中获取默认值
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    status: str  # pending, running, completed, failed
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_time_ms: Optional[int] = None


class TaskDependencyGraph:
    """任务依赖图"""
    
    def __init__(self):
        self.tasks: Dict[str, TaskDefinition] = {}
        self.dependencies: Dict[str, List[str]] = {}
        self.logger = logger.bind(component="dependency_graph")
    
    def add_task(self, task: TaskDefinition) -> None:
        """添加任务"""
        self.tasks[task.task_id] = task
        self.dependencies[task.task_id] = task.dependencies.copy()
        self.logger.debug("Task added to graph", task_id=task.task_id, dependencies=task.dependencies)
    
    def get_ready_tasks(self, completed_tasks: set) -> List[TaskDefinition]:
        """获取可以执行的任务（依赖已满足）"""
        ready_tasks = []
        
        for task_id, task in self.tasks.items():
            if task_id in completed_tasks:
                continue
                
            # 检查依赖是否都已完成
            dependencies_met = all(dep in completed_tasks for dep in self.dependencies[task_id])
            
            if dependencies_met:
                ready_tasks.append(task)
        
        # 按优先级排序
        ready_tasks.sort(key=lambda t: t.priority, reverse=True)
        return ready_tasks
    
    def validate_dependencies(self) -> bool:
        """验证依赖图是否有效（无循环依赖）"""
        visited = set()
        rec_stack = set()
        
        def has_cycle(task_id: str) -> bool:
            visited.add(task_id)
            rec_stack.add(task_id)
            
            for dep in self.dependencies.get(task_id, []):
                if dep not in visited:
                    if has_cycle(dep):
                        return True
                elif dep in rec_stack:
                    return True
            
            rec_stack.remove(task_id)
            return False
        
        for task_id in self.tasks:
            if task_id not in visited:
                if has_cycle(task_id):
                    self.logger.error("Circular dependency detected", task_id=task_id)
                    return False
        
        return True
    
    def get_execution_levels(self) -> List[List[TaskDefinition]]:
        """获取任务执行层级（用于并行执行规划）"""
        levels = []
        completed = set()
        
        while len(completed) < len(self.tasks):
            current_level = self.get_ready_tasks(completed)
            
            if not current_level:
                # 如果没有可执行的任务，可能存在循环依赖
                remaining_tasks = set(self.tasks.keys()) - completed
                self.logger.error("No ready tasks found, possible circular dependency", 
                                remaining_tasks=list(remaining_tasks))
                break
            
            levels.append(current_level)
            completed.update(task.task_id for task in current_level)
        
        return levels
