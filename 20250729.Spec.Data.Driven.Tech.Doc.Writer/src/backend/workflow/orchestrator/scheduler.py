"""
任务调度器

负责任务的并行执行和状态管理。
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional
import structlog

from src.backend.config import Config
from src.backend.agents.base import BaseAgent, AgentConfig
from .models import TaskDefinition, TaskResult, TaskDependencyGraph

logger = structlog.get_logger(__name__)


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.max_concurrent_tasks = config.scheduler.max_concurrent_tasks
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_results: Dict[str, TaskResult] = {}
        self.logger = logger.bind(component="task_scheduler")
    
    async def execute_tasks(self, tasks: List[TaskDefinition], config: Config) -> Dict[str, TaskResult]:
        """执行任务列表"""
        self.logger.info("Starting task execution", task_count=len(tasks))
        
        # 创建依赖图
        dependency_graph = TaskDependencyGraph()
        for task in tasks:
            dependency_graph.add_task(task)
        
        # 验证依赖图
        if not dependency_graph.validate_dependencies():
            raise ValueError("Invalid task dependencies detected")
        
        # 获取执行层级
        execution_levels = dependency_graph.get_execution_levels()
        self.logger.info("Task execution levels planned", levels_count=len(execution_levels))
        
        # 逐层执行任务
        for level_index, level_tasks in enumerate(execution_levels):
            self.logger.info("Executing task level", level=level_index, tasks_count=len(level_tasks))
            await self._execute_task_level(level_tasks, config)
        
        self.logger.info("All tasks completed", total_tasks=len(tasks))
        return self.task_results
    
    async def _execute_task_level(self, tasks: List[TaskDefinition], config: Config) -> None:
        """执行一个层级的任务"""
        # 创建任务协程
        task_coroutines = []
        for task_def in tasks:
            coroutine = self._execute_single_task(task_def, config)
            task_coroutines.append(coroutine)
        
        # 并行执行任务（受并发限制）
        semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        
        async def limited_task(task_def: TaskDefinition, config: Config):
            async with semaphore:
                return await self._execute_single_task(task_def, config)
        
        # 等待所有任务完成
        results = await asyncio.gather(
            *[limited_task(task_def, config) for task_def in tasks],
            return_exceptions=True
        )
        
        # 处理结果
        for task_def, result in zip(tasks, results):
            if isinstance(result, Exception):
                self.logger.error("Task failed with exception", 
                                task_id=task_def.task_id, error=str(result))
                self.task_results[task_def.task_id] = TaskResult(
                    task_id=task_def.task_id,
                    status="failed",
                    error_message=str(result)
                )
    
    async def _execute_single_task(self, task_def: TaskDefinition, config: Config) -> TaskResult:
        """执行单个任务"""
        task_id = task_def.task_id
        start_time = datetime.now()
        
        # 创建任务结果
        task_result = TaskResult(
            task_id=task_id,
            status="running",
            start_time=start_time
        )
        self.task_results[task_id] = task_result
        
        self.logger.info("Starting task execution", task_id=task_id, task_type=task_def.task_type)
        
        try:
            # 创建智能体配置
            agent_config = AgentConfig(
                agent_id=f"{task_def.agent_type}_{task_id}",
                agent_type=task_def.agent_type,
                llm_config={}
            )
            
            # 根据智能体类型创建智能体实例
            agent = await self._create_agent(agent_config, config, task_def.agent_type)
            
            # 执行任务，使用配置的超时时间
            timeout_seconds = task_def.timeout_seconds or self.config.workflow.default_task_timeout_seconds
            result = await asyncio.wait_for(
                agent.execute_task(task_def.input_data),
                timeout=timeout_seconds
            )
            
            # 更新任务结果
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds() * 1000
            
            task_result.status = "completed"
            task_result.result_data = result.result_data if hasattr(result, 'result_data') else result
            task_result.end_time = end_time
            task_result.execution_time_ms = int(execution_time)
            
            self.logger.info("Task completed successfully", 
                           task_id=task_id, 
                           execution_time_ms=int(execution_time))
            
            return task_result
            
        except asyncio.TimeoutError:
            error_msg = f"Task timed out after {task_def.timeout_seconds} seconds"
            await self._handle_task_error(task_result, task_id, TimeoutError(error_msg), start_time)
            return task_result
            
        except Exception as error:
            await self._handle_task_error(task_result, task_id, error, start_time)
            return task_result
    
    async def _create_agent(self, agent_config: AgentConfig, config: Config, agent_type: str) -> BaseAgent:
        """创建智能体实例"""
        if agent_type == "requirement_analyst":
            from src.backend.agents.requirement_analyst import RequirementAnalystAgent
            agent = RequirementAnalystAgent(agent_config, config)
        elif agent_type == "information_retrieval":
            from src.backend.agents.information_retrieval import InformationRetrievalAgent
            agent = InformationRetrievalAgent(agent_config, config)
        else:
            raise ValueError(f"Unknown agent type: {agent_type}")
        
        await agent.initialize()
        return agent
    
    async def _handle_task_error(self, task_result: TaskResult, task_id: str, error: Exception, start_time: datetime) -> None:
        """处理任务错误"""
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds() * 1000
        
        task_result.status = "failed"
        task_result.error_message = str(error)
        task_result.end_time = end_time
        task_result.execution_time_ms = int(execution_time)
        
        self.logger.error("Task failed", task_id=task_id, error=str(error))
