"""
工作流监控模块

实现性能监控、错误处理和日志记录功能。
"""

import time
import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
import structlog
from functools import wraps

from src.backend.workflow.logger import WorkflowLogger

logger = structlog.get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    task_id: str
    task_type: str
    start_time: float
    end_time: Optional[float] = None
    execution_time_ms: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: datetime
    active_tasks: int
    completed_tasks: int
    failed_tasks: int
    average_execution_time_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    throughput_tasks_per_second: float


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        self.task_metrics: Dict[str, PerformanceMetrics] = {}
        self.system_metrics_history: deque = deque(maxlen=max_history_size)
        self.task_type_stats: Dict[str, List[float]] = defaultdict(list)
        self.logger = logger.bind(component="PerformanceMonitor")
        
        # 启动系统监控
        self._monitoring_task = None
        self._start_system_monitoring()
    
    def start_task_monitoring(self, task_id: str, task_type: str, metadata: Dict[str, Any] = None) -> None:
        """开始任务监控"""
        metrics = PerformanceMetrics(
            task_id=task_id,
            task_type=task_type,
            start_time=time.time(),
            metadata=metadata or {}
        )
        self.task_metrics[task_id] = metrics
        
        self.logger.debug("Task monitoring started", task_id=task_id, task_type=task_type)
    
    def end_task_monitoring(self, task_id: str, success: bool = True, error_message: str = None) -> PerformanceMetrics:
        """结束任务监控"""
        if task_id not in self.task_metrics:
            self.logger.warning("Task not found in monitoring", task_id=task_id)
            return None
        
        metrics = self.task_metrics[task_id]
        metrics.end_time = time.time()
        metrics.execution_time_ms = (metrics.end_time - metrics.start_time) * 1000
        metrics.success = success
        metrics.error_message = error_message
        
        # 更新任务类型统计
        self.task_type_stats[metrics.task_type].append(metrics.execution_time_ms)
        
        # 限制历史记录大小
        if len(self.task_type_stats[metrics.task_type]) > self.max_history_size:
            self.task_type_stats[metrics.task_type] = self.task_type_stats[metrics.task_type][-self.max_history_size:]
        
        self.logger.info(
            "Task monitoring completed",
            task_id=task_id,
            task_type=metrics.task_type,
            execution_time_ms=metrics.execution_time_ms,
            success=success
        )
        
        return metrics
    
    def get_task_metrics(self, task_id: str) -> Optional[PerformanceMetrics]:
        """获取任务指标"""
        return self.task_metrics.get(task_id)
    
    def get_task_type_stats(self, task_type: str) -> Dict[str, float]:
        """获取任务类型统计"""
        execution_times = self.task_type_stats.get(task_type, [])
        
        if not execution_times:
            return {}
        
        return {
            "count": len(execution_times),
            "average_ms": sum(execution_times) / len(execution_times),
            "min_ms": min(execution_times),
            "max_ms": max(execution_times),
            "median_ms": sorted(execution_times)[len(execution_times) // 2]
        }
    
    def get_system_metrics(self) -> Optional[SystemMetrics]:
        """获取最新系统指标"""
        if not self.system_metrics_history:
            return None
        return self.system_metrics_history[-1]
    
    def get_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        report = {
            "timestamp": datetime.utcnow().isoformat(),
            "task_types": {},
            "system_metrics": None,
            "summary": {}
        }
        
        # 任务类型统计
        for task_type in self.task_type_stats:
            report["task_types"][task_type] = self.get_task_type_stats(task_type)
        
        # 系统指标
        if self.system_metrics_history:
            report["system_metrics"] = self.system_metrics_history[-1].__dict__
        
        # 总体统计
        total_tasks = sum(len(times) for times in self.task_type_stats.values())
        if total_tasks > 0:
            all_times = []
            for times in self.task_type_stats.values():
                all_times.extend(times)
            
            report["summary"] = {
                "total_tasks": total_tasks,
                "average_execution_time_ms": sum(all_times) / len(all_times),
                "task_types_count": len(self.task_type_stats)
            }
        
        return report
    
    def _start_system_monitoring(self) -> None:
        """启动系统监控"""
        async def monitor_system():
            while True:
                try:
                    # 收集系统指标
                    metrics = self._collect_system_metrics()
                    self.system_metrics_history.append(metrics)
                    
                    # 每30秒收集一次
                    await asyncio.sleep(30)
                    
                except Exception as e:
                    self.logger.error("System monitoring error", error=str(e))
                    await asyncio.sleep(30)
        
        # 在后台启动监控任务
        try:
            loop = asyncio.get_event_loop()
            self._monitoring_task = loop.create_task(monitor_system())
        except RuntimeError:
            # 如果没有运行的事件循环，跳过系统监控
            self.logger.warning("No event loop available for system monitoring")
    
    def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        # 计算任务统计
        active_tasks = len([m for m in self.task_metrics.values() if m.end_time is None])
        completed_tasks = len([m for m in self.task_metrics.values() if m.end_time is not None and m.success])
        failed_tasks = len([m for m in self.task_metrics.values() if m.end_time is not None and not m.success])
        
        # 计算平均执行时间
        completed_metrics = [m for m in self.task_metrics.values() if m.execution_time_ms is not None]
        avg_execution_time = 0
        if completed_metrics:
            avg_execution_time = sum(m.execution_time_ms for m in completed_metrics) / len(completed_metrics)
        
        # 计算吞吐量（简化版本）
        throughput = 0
        if len(self.system_metrics_history) > 1:
            prev_metrics = self.system_metrics_history[-1]
            time_diff = (datetime.utcnow() - prev_metrics.timestamp).total_seconds()
            if time_diff > 0:
                task_diff = completed_tasks - prev_metrics.completed_tasks
                throughput = task_diff / time_diff
        
        return SystemMetrics(
            timestamp=datetime.utcnow(),
            active_tasks=active_tasks,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks,
            average_execution_time_ms=avg_execution_time,
            memory_usage_mb=0,  # 简化版本，实际应该使用psutil
            cpu_usage_percent=0,  # 简化版本，实际应该使用psutil
            throughput_tasks_per_second=throughput
        )
    
    def stop_monitoring(self) -> None:
        """停止监控"""
        if self._monitoring_task:
            self._monitoring_task.cancel()


# WorkflowLogger类已移动到 src/workflow/logger.py
    """工作流日志记录器"""
    
    def __init__(self):
        self.logger = logger.bind(component="WorkflowLogger")
        self.workflow_logs: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
    
    def log_workflow_start(self, workflow_id: str, workflow_type: str, input_data: Dict[str, Any]) -> None:
        """记录工作流开始"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event": "workflow_start",
            "workflow_id": workflow_id,
            "workflow_type": workflow_type,
            "input_data_size": len(str(input_data))
        }
        
        self.workflow_logs[workflow_id].append(log_entry)
        # 避免event参数冲突，分别传递参数
        self.logger.info("Workflow started",
                        timestamp=log_entry["timestamp"],
                        event_type=log_entry["event"],
                        workflow_id=workflow_id,
                        workflow_type=workflow_type,
                        input_data_size=log_entry["input_data_size"])
    
    def log_workflow_end(self, workflow_id: str, success: bool, result_data: Dict[str, Any] = None, error: str = None) -> None:
        """记录工作流结束"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event": "workflow_end",
            "workflow_id": workflow_id,
            "success": success,
            "result_data_size": len(str(result_data)) if result_data else 0,
            "error": error
        }
        
        self.workflow_logs[workflow_id].append(log_entry)

        # 避免event参数冲突，分别传递参数
        if success:
            self.logger.info("Workflow completed successfully",
                            timestamp=log_entry["timestamp"],
                            event_type=log_entry["event"],
                            workflow_id=workflow_id,
                            success=success,
                            result_data_size=log_entry["result_data_size"])
        else:
            self.logger.error("Workflow failed",
                            timestamp=log_entry["timestamp"],
                            event_type=log_entry["event"],
                            workflow_id=workflow_id,
                            success=success,
                            result_data_size=log_entry["result_data_size"],
                            error=error)
    
    def log_task_event(self, workflow_id: str, task_id: str, event: str, details: Dict[str, Any] = None) -> None:
        """记录任务事件"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event": f"task_{event}",
            "workflow_id": workflow_id,
            "task_id": task_id,
            "details": details or {}
        }
        
        self.workflow_logs[workflow_id].append(log_entry)
        # 避免event参数冲突，分别传递参数
        self.logger.debug("Task event",
                         timestamp=log_entry["timestamp"],
                         event_type=log_entry["event"],
                         workflow_id=workflow_id,
                         task_id=task_id,
                         details=log_entry["details"])
    
    def get_workflow_logs(self, workflow_id: str) -> List[Dict[str, Any]]:
        """获取工作流日志"""
        return self.workflow_logs.get(workflow_id, [])


def performance_monitor(monitor: PerformanceMonitor):
    """性能监控装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            task_id = kwargs.get('task_id', f"{func.__name__}_{int(time.time())}")
            task_type = func.__name__
            
            monitor.start_task_monitoring(task_id, task_type)
            
            try:
                result = await func(*args, **kwargs)
                monitor.end_task_monitoring(task_id, success=True)
                return result
            except Exception as e:
                monitor.end_task_monitoring(task_id, success=False, error_message=str(e))
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            task_id = kwargs.get('task_id', f"{func.__name__}_{int(time.time())}")
            task_type = func.__name__
            
            monitor.start_task_monitoring(task_id, task_type)
            
            try:
                result = func(*args, **kwargs)
                monitor.end_task_monitoring(task_id, success=True)
                return result
            except Exception as e:
                monitor.end_task_monitoring(task_id, success=False, error_message=str(e))
                raise
        
        # 检查函数是否是协程
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# 全局监控实例
global_performance_monitor = PerformanceMonitor()
global_workflow_logger = WorkflowLogger()
