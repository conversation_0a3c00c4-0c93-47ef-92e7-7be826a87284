"""
工作流日志记录器

负责记录工作流执行过程中的各种事件和状态。
"""

from collections import defaultdict
from datetime import datetime
from typing import Dict, Any, List
import structlog

logger = structlog.get_logger(__name__)


class WorkflowLogger:
    """工作流日志记录器"""
    
    def __init__(self):
        self.logger = logger.bind(component="WorkflowLogger")
        self.workflow_logs: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
    
    def log_workflow_start(self, workflow_id: str, workflow_type: str, input_data: Dict[str, Any]) -> None:
        """记录工作流开始"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event": "workflow_start",
            "workflow_id": workflow_id,
            "workflow_type": workflow_type,
            "input_data_size": len(str(input_data))
        }
        
        self.workflow_logs[workflow_id].append(log_entry)
        # 避免event参数冲突，分别传递参数
        self.logger.info("Workflow started",
                        timestamp=log_entry["timestamp"],
                        event_type=log_entry["event"],
                        workflow_id=workflow_id,
                        workflow_type=workflow_type,
                        input_data_size=log_entry["input_data_size"])
    
    def log_workflow_end(self, workflow_id: str, success: bool, result_data: Dict[str, Any] = None, error: str = None) -> None:
        """记录工作流结束"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event": "workflow_end",
            "workflow_id": workflow_id,
            "success": success,
            "result_data_size": len(str(result_data)) if result_data else 0,
            "error": error
        }
        
        self.workflow_logs[workflow_id].append(log_entry)
        self.logger.info("Workflow ended",
                        timestamp=log_entry["timestamp"],
                        event_type=log_entry["event"],
                        workflow_id=workflow_id,
                        success=success,
                        result_data_size=log_entry["result_data_size"],
                        error=error)
    
    def log_task_start(self, workflow_id: str, task_id: str, task_type: str) -> None:
        """记录任务开始"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event": "task_start",
            "workflow_id": workflow_id,
            "task_id": task_id,
            "task_type": task_type
        }
        
        self.workflow_logs[workflow_id].append(log_entry)
        self.logger.info("Task started",
                        timestamp=log_entry["timestamp"],
                        event_type=log_entry["event"],
                        workflow_id=workflow_id,
                        task_id=task_id,
                        task_type=task_type)
    
    def log_task_end(self, workflow_id: str, task_id: str, success: bool, execution_time_ms: int, error: str = None) -> None:
        """记录任务结束"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event": "task_end",
            "workflow_id": workflow_id,
            "task_id": task_id,
            "success": success,
            "execution_time_ms": execution_time_ms,
            "error": error
        }
        
        self.workflow_logs[workflow_id].append(log_entry)
        self.logger.info("Task ended",
                        timestamp=log_entry["timestamp"],
                        event_type=log_entry["event"],
                        workflow_id=workflow_id,
                        task_id=task_id,
                        success=success,
                        execution_time_ms=execution_time_ms,
                        error=error)
    
    def get_workflow_logs(self, workflow_id: str) -> List[Dict[str, Any]]:
        """获取工作流日志"""
        return self.workflow_logs.get(workflow_id, [])
    
    def get_workflow_summary(self, workflow_id: str) -> Dict[str, Any]:
        """获取工作流摘要"""
        logs = self.workflow_logs.get(workflow_id, [])
        
        if not logs:
            return {"error": "No logs found for workflow"}
        
        start_time = None
        end_time = None
        task_count = 0
        successful_tasks = 0
        failed_tasks = 0
        total_execution_time = 0
        
        for log in logs:
            if log["event"] == "workflow_start":
                start_time = log["timestamp"]
            elif log["event"] == "workflow_end":
                end_time = log["timestamp"]
            elif log["event"] == "task_end":
                task_count += 1
                if log["success"]:
                    successful_tasks += 1
                else:
                    failed_tasks += 1
                total_execution_time += log.get("execution_time_ms", 0)
        
        return {
            "workflow_id": workflow_id,
            "start_time": start_time,
            "end_time": end_time,
            "task_count": task_count,
            "successful_tasks": successful_tasks,
            "failed_tasks": failed_tasks,
            "total_execution_time_ms": total_execution_time,
            "average_task_time_ms": total_execution_time / task_count if task_count > 0 else 0
        }
