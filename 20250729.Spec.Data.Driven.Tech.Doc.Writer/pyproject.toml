[project]
name = "spec-data-driven-tech-doc-writer"
version = "0.1.0"
description = "基于规格数据驱动的技术文档写作多代理系统"
authors = [
    {name = "Development Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
keywords = ["ai", "multi-agent", "documentation", "langgraph", "llm"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Documentation",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    # Core LLM and Agent Framework
    "langgraph>=0.6.0",
    "langchain>=0.3.0",
    "langchain-core>=0.3.0",
    "langchain-community>=0.3.0",
    "litellm>=1.40.0",
    # Sprint 2 Search and Content Processing Dependencies
    "tavily-python>=0.7.0",
    "markdownify>=1.1.0",
    "readabilipy>=0.3.0",
    "markitdown>=0.0.1a2",
    # Web Framework and API
    "fastapi>=0.110.0",
    "uvicorn[standard]>=0.27.0",
    "httpx>=0.28.1",
    # Data Validation and Models
    "pydantic>=2.6.0",
    "pydantic-settings>=2.2.0",
    # Database and Caching
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "redis>=5.0.0",
    "aiosqlite>=0.20.0",
    "asyncpg>=0.29.0",
    # Logging and Monitoring
    "structlog>=24.1.0",
    "rich>=13.7.0",
    # File Processing and Web Scraping
    "beautifulsoup4>=4.12.0",
    "lxml>=5.1.0",
    "aiofiles>=23.2.0",
    "python-multipart>=0.0.9",
    # Configuration and Environment
    "pyyaml>=6.0.0",
    "python-dotenv>=1.0.0",
    # CLI and User Interface
    "click>=8.1.0",
    "typer>=0.12.0",
    # Utilities
    "tenacity>=8.2.0",
    "jinja2>=3.1.0",
    "markupsafe>=2.1.0",
    "google-generativeai>=0.8.5",
    "nest-asyncio>=1.6.0",
    "ddgs>=9.5.1",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.27.0",
    
    # Code Quality
    "black>=24.0.0",
    "isort>=5.13.0",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
    
    # Documentation
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=2.0.0",
    "myst-parser>=2.0.0",
]

test = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.27.0",
]

docs = [
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=2.0.0",
    "myst-parser>=2.0.0",
]

[project.scripts]
tech-doc-writer = "src.backend.cli:main"

[project.urls]
Homepage = "https://github.com/example/spec-data-driven-tech-doc-writer"
Documentation = "https://spec-data-driven-tech-doc-writer.readthedocs.io/"
Repository = "https://github.com/example/spec-data-driven-tech-doc-writer.git"
Issues = "https://github.com/example/spec-data-driven-tech-doc-writer/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "langgraph.*",
    "langchain.*",
    "litellm.*",
    "redis.*",
    "beautifulsoup4.*",
    "lxml.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "8.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["test"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "performance: marks tests as performance tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"test_*.py" = ["B011"]

[tool.bandit]
exclude_dirs = ["test", "tests"]
skips = ["B101", "B601"]

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
]
