# 20250729.Spec.Data.Driven.Tech.Doc.Writer 项目开发规则

## 项目概述
这是一个基于规格数据驱动的技术文档写作多智能体系统项目。

### 项目目标
- 构建智能化的技术文档生成系统
- 实现多智能体协作的文档写作流程
- 提供数据驱动的文档质量保证

### 核心特性
- 多智能体架构：支持专门化的智能体角色（分析、写作、审核、优化）
- 数据驱动：基于结构化规格数据生成文档
- 智能协作：智能体间通过消息传递和状态共享协作
- 质量保证：自动化的文档质量检查和优化

## 开发环境和工具
- 语言: Python 3.11+，PEP 8 规范，类型注解
- 包管理: uv（禁止手动编辑 pyproject.toml）
- 核心框架: LiteLLM, LangGraph>=0.6.2, LangChain, FastAPI, httpx, pydantic, structlog
- 内容处理: readabilipy (HTML清洁), markdownify (HTML转Markdown)
- 搜索集成: langchain-community (Brave Search, DuckDuckGo, Arxiv)
- 数据库: SQLite（开发环境）/ PostgreSQL（生产环境）
- 缓存: Redis（内存缓存）+ 文件系统（持久化缓存）
- 配置管理: config.yaml（实际配置）+ config.yaml.example（配置模板）
- 网络代理: 所有网络访问使用 HTTP_PROXY=http://127.0.0.1:8118/ HTTPS_PROXY=http://127.0.0.1:8118/

## 项目结构规范

### 目录结构
```
20250729.Spec.Data.Driven.Tech.Doc.Writer/
├── 3rd/                   # 第三方代码和工具
├── data/                  # 数据文件和配置
│   └── backups/           # 数据备份
│   └── output/            # 系统输出文件
├── doc/                   # 项目文档
│   ├── api/               # API文档
│   ├── debug/             # 调试和排错文档
│   ├── design/            # 设计文档和架构图
│   ├── dev/               # 开发过程记录
│   │   ├── architecture/  # 架构决策和设计
│   │   ├── deployment/    # 部署相关文档
│   │   ├── implementation/# 实现过程记录
│   │   ├── knowledge-base/# 知识库和最佳实践
│   │   ├── meetings/      # 会议记录
│   │   ├── processes/     # 开发流程和规范
│   │   ├── requirements/  # 开发需求和待办事项
│   │   ├── sprints/       # Sprint 规划和回顾
│   │   └── testing/       # 测试策略和计划
│   └── requirements/      # 用户需求文档（按需创建子目录）
├── log/                   # 日志文件
├── script/                # 脚本文件
├── src/                   # 主要源代码目录
│   └── backend/           # 后端代码
│   └── frontend/          # 前端代码
└── test/                  # 测试代码
```

### 文件放置规则
- 测试和调试代码放到 test 目录，按类型分类
- 文档按类型和用途分别放到指定目录：
  - 用户文档：`doc/` 根级别 - README、使用说明、项目概述
  - 原始用户需求：`doc/requirements/` - 用户直接提出的原始需求清单和简要信息
  - API文档：`doc/api/` - API规格说明、接口文档
  - 调试文档：`doc/debug/` - 调试、排错过程记录
  - 设计文档：`doc/design/` - 最终设计文档、架构图、系统规格说明
  - 开发文档：`doc/dev/` - 开发过程记录、会议纪要、技术决策
    - 开发需求：`doc/dev/requirements/` - 原始需求分析后形成的详细需求文档、技术需求、待办事项

### 文档命名规范
- `docs/` 二级目录文档格式：`YYYYMMDD.文档名.va.b.md`
- 更新时检查文件名：日期反映最后修改，重大变更更新主版本，小修改更新次版本

### 需求管理规范
#### 原始需求管理 (`doc/requirements/`)
- 目的: 保存用户直接提出的原始需求清单，尽量简练
- 内容: 原始需求、需求分析后形成的需求文档位置、当前处理状态
- 格式: 使用表格形式展现一览表，重点关注未完成开发、部分完成开发的原始需求
- 原则: 更多详细信息保存在原始需求分析后形成的需求文档中

#### 开发需求管理 (`doc/dev/requirements/`)
- 目的: 保存原始需求分析后形成的详细需求文档
- 内容: 详细的需求分析、技术需求、用户故事、验收标准、待办事项
- 结构: 按需求类型分类存放（user-stories/, acceptance-criteria/, backlog/）
- 关联: 与原始需求建立明确的追溯关系

### 根目录文件规范
- 允许：配置文件(`.env*`, `.gitignore`, `.augmentrules`, `config.yaml.example`, `config.yaml`)、运行脚本(`run.py`, `main.py`)、项目文档(`README.md`, `LICENSE`)、依赖管理(`pyproject.toml`, `uv.lock`)
- 禁止：测试/调试/演示脚本(`test_*.py`, `debug_*.py`, `demo*.py`)、临时文件(`*.tmp`, `*.log`)

### 配置文件管理规范
- `config.yaml.example`: 配置模板，含所有示例配置，提交到版本控制
- `config.yaml`: 实际配置文件，含敏感信息，不提交
- 使用 YAML 格式，支持嵌套和环境变量
- 敏感信息用环境变量或加密存储
- 启动时验证配置完整性

### 配置简化原则
- 智能缺省: 配置项有合理缺省值
- 环境感知: 自动适配开发/测试/生产环境
- 分层结构: 必需 < 可选 < 高级配置
- 快速启动: 仅需 LLM API 密钥即可运行
- 继承机制: 支持配置继承和覆盖
- 服务发现: 自动配置本地服务组件

## 多智能体系统开发规则

### 智能体设计原则
- 单一职责: 每个智能体专注于特定的功能领域
- 松耦合: 智能体间通过消息传递和共享状态通信
- 可扩展: 支持动态添加新的智能体类型
- 容错性: 智能体失败不应影响整个系统运行
- 可观测: 提供详细的智能体行为日志和监控

### 智能体实现规范
- 所有智能体必须继承自 `BaseAgent` 基类
- 实现标准的生命周期方法：`initialize()`, `execute()`, `cleanup()`
- 使用 Pydantic 模型定义智能体的输入输出接口
- 实现异步处理以支持并发执行
- 提供详细的类型注解和文档字符串

### 工作流设计规范
- 使用 LangGraph 定义智能体间的协作流程
- 明确定义状态转换和数据流
- 实现检查点机制支持工作流恢复
- 提供工作流可视化和调试功能
- 支持条件分支和循环处理

### 状态管理
- 使用结构化的状态模型（Pydantic）
- 实现状态持久化和恢复机制
- 提供状态版本控制和回滚功能
- 确保状态的线程安全和一致性

## 代码质量规范

### 核心开发原则
- 全面审视: 开发前必须全面审视相关文档和代码
- 表达准确: 选择准确完整表达、LLM 易理解、省 token 的方式
- 影响最小: 新功能开发选择对原有代码整体影响最小的方案
- 无副作用: 故障修复不影响原有业务逻辑、功能、输入输出
- 聚焦问题: 解决故障时聚焦问题本身，walk around 方案需用户确认
- 禁用硬编码: 禁止硬编码常量作为输入，定义全局常量便于修改
- 强制 LiteLLM: 必须使用 LiteLLM 库，禁止直接调用 LLM 大模型

### 代码组织
- 使用清晰的模块结构和命名约定
- 实现适当的异常处理和错误恢复
- 添加详细的文档字符串（支持 Sphinx 格式）
- 编写全面的单元测试和集成测试
- 使用依赖注入提高代码可测试性

### 类型安全
- 所有公共接口必须有类型注解
- 使用 Pydantic 进行数据验证
- 运行 mypy 进行静态类型检查
- 使用泛型提高代码复用性

### 日志和监控
- 代码注释使用英文
- 日志消息使用英文，采用结构化格式
- 文档和用户界面可以使用中文
- 实现分级日志（DEBUG, INFO, WARNING, ERROR）
- 缺省屏幕回显只有用户交互相关的，其余信息都以日志形式记录到日志文件
- 添加性能监控和指标收集
- LittleLLM 需要遵循统一的日志级别，将日志保存到指定目录下，并以 little_llm.YYYYMMDD.log 为文件名将所有和 LLM 的交互以日志形式保存下来。

### 代码行数控制
- 每个文件代码不超过 300 行（多智能体系统需要更细粒度的模块化）
- 每个函数不超过 50 行
- 每个类不超过 200 行
- 当超过限制时，按功能拆分为多个文件
- 优先保持功能内聚性，避免过度拆分

## 开发流程

### 环境设置
1. 开发、测试、生产环境都使用 `uv` 管理依赖和虚拟环境
2. 快速启动（可选）: 直接运行，系统使用内置缺省配置
3. 自定义配置（推荐）: 复制 `config.yaml.example` 为 `config.yaml`，仅配置必要参数
4. 最小配置: 仅需配置 LLM API 密钥，其他使用智能缺省值
5. 确保所有网络访问都通过代理
6. 配置开发工具（linter, formatter, type checker）

### 开发周期
1. 需求分析: 明确智能体职责和交互接口
2. 设计阶段: 设计智能体架构和工作流
3. 实现阶段: 编写智能体代码和测试
4. 集成测试: 测试智能体间协作
5. 性能优化: 优化智能体性能和资源使用
6. 文档更新: 更新API文档和用户指南

### 质量保证
- 代码审查：所有代码变更需要审查
- 自动化测试：CI/CD 流水线自动运行测试
- 性能基准：定期运行性能基准测试
- 安全检查：定期进行安全漏洞扫描

## LLM 协作指导原则

### LLM 协作态度
- 理性反对: 对整体有益且收益大于成本风险时，应及时提出反对意见
- 明确价值: 反对时须明确反对理由、价值收益、具体建议
- 避免迎合: 禁止无意义赞美，以项目成功为目标坦诚交流

## 开发原则

### 核心原则
- 实事求是: 客观评估，不过度承诺
- 业务优先: 先跑通业务流程，再考虑优化
- 全面审视: 开发前必须全面审视相关文档和代码
- 影响最小: 新功能开发选择对原有代码整体影响最小的方案

### 质量原则
- 无副作用: 故障修复不影响原有业务逻辑、功能、输入输出
- 聚焦问题: 解决故障时聚焦问题本身，walk around 方案需用户确认
- 禁用硬编码: 禁止硬编码常量作为输入，定义全局常量便于修改

## LLM 友好的开发实践

### 代码可读性
- 使用描述性的变量和函数名
- 添加详细的注释说明复杂逻辑
- 使用类型注解提高代码理解
- 保持一致的代码风格和格式

### 文档规范
- 每个模块都有清晰的模块级文档
- 每个类和函数都有详细的文档字符串
- 提供使用示例和最佳实践
- 维护最新的 API 文档

### 文档编写原则
- 全面审视: 更新、编写文档前要全面审视现有文档
- 表达准确: 选择准确完整表达、LLM 易理解、省 token 的方式
- 优先更新: 优先更新现有文档，而不是创建新文档
- 优先引用: 优先引用现有文档的内容，而不是重复编写

### 开源库使用规范
- 强制文档查询: 使用开源库前必须先通过 Context 7 或 DeepWiki 获取最新文档
- 问题驱动查询: 结合具体要解决的问题了解库的使用方法和最佳实践
- 版本兼容性: 确认文档版本与项目使用版本的兼容性
- API变更跟踪: 关注库的API变更和废弃警告

### 错误处理
- 使用自定义异常类型
- 提供详细的错误消息和上下文
- 实现优雅的错误恢复机制
- 记录错误日志便于调试

## 注意事项
- 考虑并正确处理网络超时和连接错误
- 实现适当的重试机制和熔断器模式
- 注意智能体间的死锁和竞态条件
- 监控系统资源使用和性能指标
- 定期备份重要数据和配置

