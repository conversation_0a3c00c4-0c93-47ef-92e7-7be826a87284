#!/usr/bin/env python3
"""
修复导入路径脚本

将所有 "from src." 的导入更新为 "from src.backend."
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path: Path):
    """修复单个文件中的导入路径"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换导入语句
        # from src.xxx import yyy -> from src.backend.xxx import yyy
        updated_content = re.sub(
            r'from src\.([^.][^.\s]*)',
            r'from src.backend.\1',
            content
        )
        
        # import src.xxx -> import src.backend.xxx
        updated_content = re.sub(
            r'import src\.([^.][^.\s]*)',
            r'import src.backend.\1',
            updated_content
        )
        
        # 只有内容发生变化时才写入文件
        if updated_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            print(f"Updated: {file_path}")
            return True
        return False
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """主函数"""
    backend_dir = Path("src/backend")
    
    if not backend_dir.exists():
        print("src/backend directory not found!")
        return
    
    updated_count = 0
    
    # 遍历所有 Python 文件
    for py_file in backend_dir.rglob("*.py"):
        if fix_imports_in_file(py_file):
            updated_count += 1
    
    print(f"\nTotal files updated: {updated_count}")

if __name__ == "__main__":
    main()
