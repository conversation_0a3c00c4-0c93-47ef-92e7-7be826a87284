#!/usr/bin/env python3
"""
简单验证重构后的系统功能

这个脚本验证重构后的核心组件是否正常工作，不依赖复杂的导入。
"""

import asyncio
import sys
import tempfile
import hashlib
from pathlib import Path
from urllib.parse import urlparse, urlunparse, parse_qs, urle<PERSON><PERSON>


def normalize_url_for_hash(original_url: str) -> str:
    """URL标准化处理，用于生成一致的MD5哈希"""
    # 1. 去除fragment部分
    if '#' in original_url:
        url_without_fragment = original_url.split('#')[0]
    else:
        url_without_fragment = original_url
    
    # 2. 解析URL
    parsed = urlparse(url_without_fragment)
    
    # 3. 标准化scheme和host为小写，path也要小写
    scheme = parsed.scheme.lower()
    netloc = parsed.netloc.lower()
    path = parsed.path.lower()
    
    # 4. 移除默认端口
    if ':80' in netloc and scheme == 'http':
        netloc = netloc.replace(':80', '')
    elif ':443' in netloc and scheme == 'https':
        netloc = netloc.replace(':443', '')
    
    # 5. 标准化查询参数（排序）
    if parsed.query:
        query_params = parse_qs(parsed.query, keep_blank_values=True)
        # 排序参数
        sorted_params = sorted(query_params.items())
        normalized_query = urlencode(sorted_params, doseq=True)
    else:
        normalized_query = parsed.query
    
    # 6. 重新组装URL
    normalized_parsed = parsed._replace(
        scheme=scheme,
        netloc=netloc,
        path=path,
        query=normalized_query
    )
    
    return urlunparse(normalized_parsed)


def generate_url_hash(original_url: str) -> str:
    """为URL生成MD5哈希"""
    normalized_url = normalize_url_for_hash(original_url)
    return hashlib.md5(normalized_url.encode('utf-8')).hexdigest()


def test_url_normalization():
    """测试URL标准化功能"""
    print("🔧 测试URL标准化功能...")
    
    # 测试用例
    test_cases = [
        ("https://Example.com/Page?b=2&a=1#section", "https://example.com/page?a=1&b=2"),
        ("https://example.com:443/page", "https://example.com/page"),
        ("http://example.com:80/page", "http://example.com/page"),
        ("HTTPS://EXAMPLE.COM/PAGE", "https://example.com/page"),
    ]
    
    for original, expected in test_cases:
        normalized = normalize_url_for_hash(original)
        print(f"  原始: {original}")
        print(f"  标准化: {normalized}")
        print(f"  期望: {expected}")
        
        if normalized == expected:
            print("  ✅ 通过")
        else:
            print("  ❌ 失败")
            return False
        print()
    
    # 测试哈希一致性
    url1 = "https://Example.com/Page?b=2&a=1#section"
    url2 = "https://example.com:443/page?a=1&b=2"
    
    hash1 = generate_url_hash(url1)
    hash2 = generate_url_hash(url2)
    
    print(f"URL1哈希: {hash1}")
    print(f"URL2哈希: {hash2}")
    
    if hash1 == hash2:
        print("✅ URL哈希一致性测试通过")
        return True
    else:
        print("❌ URL哈希一致性测试失败")
        return False


def test_file_operations():
    """测试文件操作功能"""
    print("📁 测试文件操作功能...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 测试目录创建
        raw_content_dir = temp_path / "raw_content"
        markdown_content_dir = temp_path / "markdown_content"
        cache_dir = temp_path / "cache"
        
        raw_content_dir.mkdir(parents=True, exist_ok=True)
        markdown_content_dir.mkdir(parents=True, exist_ok=True)
        cache_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"  创建目录: {raw_content_dir}")
        print(f"  创建目录: {markdown_content_dir}")
        print(f"  创建目录: {cache_dir}")
        
        # 测试文件写入
        test_url = "https://example.com/test"
        url_hash = generate_url_hash(test_url)
        
        raw_file = raw_content_dir / f"{url_hash}.html"
        markdown_file = markdown_content_dir / f"{url_hash}.md"
        
        raw_content = "<html><body>Test content</body></html>"
        markdown_content = "# Test\n\nThis is a test."
        
        raw_file.write_text(raw_content, encoding='utf-8')
        markdown_file.write_text(markdown_content, encoding='utf-8')
        
        print(f"  写入原始文件: {raw_file}")
        print(f"  写入Markdown文件: {markdown_file}")
        
        # 验证文件内容
        if raw_file.read_text(encoding='utf-8') == raw_content:
            print("  ✅ 原始文件内容正确")
        else:
            print("  ❌ 原始文件内容错误")
            return False
        
        if markdown_file.read_text(encoding='utf-8') == markdown_content:
            print("  ✅ Markdown文件内容正确")
        else:
            print("  ❌ Markdown文件内容错误")
            return False
        
        # 测试缓存文件
        cache_data = {
            "format_version": "2.0",
            "processed_url_hashes": [url_hash],
            "url_metadata": {
                url_hash: {
                    "original_url": test_url,
                    "raw_content_path": str(raw_file),
                    "markdown_path": str(markdown_file),
                    "success": True,
                    "timestamp": "2025-08-05T12:00:00"
                }
            }
        }
        
        cache_file = cache_dir / "url_status.json"
        import json
        cache_file.write_text(json.dumps(cache_data, indent=2), encoding='utf-8')
        
        print(f"  写入缓存文件: {cache_file}")
        
        # 验证缓存文件
        loaded_data = json.loads(cache_file.read_text(encoding='utf-8'))
        if loaded_data["format_version"] == "2.0":
            print("  ✅ 缓存文件格式正确")
        else:
            print("  ❌ 缓存文件格式错误")
            return False
        
        if url_hash in loaded_data["processed_url_hashes"]:
            print("  ✅ URL哈希记录正确")
        else:
            print("  ❌ URL哈希记录错误")
            return False
        
        print("✅ 文件操作功能测试通过")
        return True


def test_path_generation():
    """测试路径生成功能"""
    print("🛤️  测试路径生成功能...")
    
    test_urls = [
        ("https://example.com/document.pdf", "pdf"),
        ("https://example.com/page.html", "html"),
        ("https://example.com/data.json", "json"),
        ("https://example.com/page", "html"),  # 默认扩展名
    ]
    
    for url, expected_ext in test_urls:
        url_hash = generate_url_hash(url)
        
        # 从URL提取扩展名
        parsed = urlparse(url)
        path = parsed.path
        if '.' in path:
            extension = path.split('.')[-1].lower()
            if extension.isalnum() and len(extension) <= 10:
                actual_ext = extension
            else:
                actual_ext = 'html'
        else:
            actual_ext = 'html'
        
        raw_content_path = f"data/raw_content/{url_hash}.{actual_ext}"
        markdown_path = f"data/markdown_content/{url_hash}.md"
        
        print(f"  URL: {url}")
        print(f"  哈希: {url_hash}")
        print(f"  扩展名: {actual_ext} (期望: {expected_ext})")
        print(f"  原始路径: {raw_content_path}")
        print(f"  Markdown路径: {markdown_path}")
        
        if actual_ext == expected_ext:
            print("  ✅ 扩展名提取正确")
        else:
            print("  ❌ 扩展名提取错误")
            return False
        print()
    
    print("✅ 路径生成功能测试通过")
    return True


async def test_async_operations():
    """测试异步操作功能"""
    print("⚡ 测试异步操作功能...")
    
    async def mock_process_url(url: str, delay: float = 0.1):
        """模拟URL处理"""
        await asyncio.sleep(delay)
        return {
            "url": url,
            "success": True,
            "processing_time": delay
        }
    
    # 测试并发处理
    urls = [f"https://example.com/page{i}" for i in range(5)]
    
    import time
    start_time = time.time()
    
    # 并发处理
    tasks = [mock_process_url(url, 0.1) for url in urls]
    results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"  处理{len(urls)}个URL用时: {processing_time:.2f}秒")
    print(f"  平均每个URL: {processing_time/len(urls):.2f}秒")
    
    if processing_time < 0.3:  # 应该远小于 5 * 0.1 = 0.5秒
        print("  ✅ 并发处理性能正常")
    else:
        print("  ❌ 并发处理性能异常")
        return False
    
    # 验证结果
    if len(results) == len(urls):
        print("  ✅ 所有URL都被处理")
    else:
        print("  ❌ URL处理数量不正确")
        return False
    
    for result in results:
        if not result.get("success", False):
            print("  ❌ 存在处理失败的URL")
            return False
    
    print("  ✅ 所有URL处理成功")
    print("✅ 异步操作功能测试通过")
    return True


async def main():
    """主测试函数"""
    print("🚀 开始简单验证重构后的系统功能...\n")
    
    tests = [
        ("URL标准化", test_url_normalization),
        ("文件操作", test_file_operations),
        ("路径生成", test_path_generation),
        ("异步操作", test_async_operations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"📋 运行测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构后的核心功能正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查重构实现。")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
