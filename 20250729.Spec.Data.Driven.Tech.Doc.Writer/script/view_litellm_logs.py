#!/usr/bin/env python3
"""
LiteLLM 日志查看工具

用于查看和分析 LiteLLM 日志文件的便捷工具
"""

import json
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
import argparse


def load_log_entries(log_file: Path) -> List[Dict[str, Any]]:
    """加载日志条目"""
    entries = []
    
    if not log_file.exists():
        print(f"❌ 日志文件不存在: {log_file}")
        return entries
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                    
                try:
                    entry = json.loads(line)
                    entries.append(entry)
                except json.JSONDecodeError as e:
                    print(f"⚠️  第 {line_num} 行 JSON 解析错误: {e}")
                    
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")
    
    return entries


def format_timestamp(timestamp_str: str) -> str:
    """格式化时间戳"""
    try:
        dt = datetime.fromisoformat(timestamp_str)
        return dt.strftime('%H:%M:%S')
    except:
        return timestamp_str


def format_duration(duration: float) -> str:
    """格式化持续时间"""
    if duration < 1:
        return f"{duration*1000:.0f}ms"
    else:
        return f"{duration:.2f}s"


def print_summary(entries: List[Dict[str, Any]]):
    """打印日志摘要"""
    if not entries:
        print("📄 没有找到日志条目")
        return
    
    total = len(entries)
    requests = sum(1 for e in entries if e.get('interaction_type') == 'request')
    responses = sum(1 for e in entries if e.get('interaction_type') == 'response')
    errors = sum(1 for e in entries if e.get('interaction_type') == 'error')
    
    # 统计提供商
    providers = {}
    models = {}
    for entry in entries:
        provider = entry.get('provider', 'unknown')
        model = entry.get('model', 'unknown')
        providers[provider] = providers.get(provider, 0) + 1
        models[model] = models.get(model, 0) + 1
    
    # 计算成功率
    success_rate = (responses / requests * 100) if requests > 0 else 0
    
    print("📊 日志摘要")
    print("=" * 50)
    print(f"总条目数: {total}")
    print(f"请求数: {requests}")
    print(f"响应数: {responses}")
    print(f"错误数: {errors}")
    print(f"成功率: {success_rate:.1f}%")
    
    print(f"\n🤖 提供商统计:")
    for provider, count in sorted(providers.items()):
        print(f"  {provider}: {count} 次")
    
    print(f"\n📱 模型统计:")
    for model, count in sorted(models.items()):
        print(f"  {model}: {count} 次")


def print_interactions(entries: List[Dict[str, Any]], limit: Optional[int] = None):
    """打印交互详情"""
    if not entries:
        return
    
    print("\n💬 交互详情")
    print("=" * 80)
    
    # 按时间戳排序
    entries = sorted(entries, key=lambda x: x.get('timestamp', ''))
    
    if limit:
        entries = entries[-limit:]  # 显示最近的条目
    
    for i, entry in enumerate(entries, 1):
        timestamp = format_timestamp(entry.get('timestamp', ''))
        interaction_type = entry.get('interaction_type', 'unknown')
        provider = entry.get('provider', 'unknown')
        model = entry.get('model', 'unknown')
        success = entry.get('success', False)
        
        # 状态图标
        if interaction_type == 'request':
            icon = "📤"
        elif interaction_type == 'response':
            icon = "📥"
        elif interaction_type == 'error':
            icon = "❌"
        else:
            icon = "❓"
        
        print(f"{icon} [{timestamp}] {provider}/{model} - {interaction_type.upper()}")
        
        # 显示请求信息
        if 'request' in entry:
            request = entry['request']
            message_count = request.get('message_count', 0)
            total_chars = request.get('total_chars', 0)
            print(f"   📝 请求: {message_count} 条消息, {total_chars} 字符")
            
            # 显示第一条消息内容（截断）
            messages = request.get('messages', [])
            if messages:
                first_msg = messages[0].get('content', '')
                preview = first_msg[:50] + "..." if len(first_msg) > 50 else first_msg
                print(f"   💭 内容: {preview}")
        
        # 显示响应信息
        if 'response' in entry:
            response = entry['response']
            char_count = response.get('char_count', 0)
            content = response.get('content', '')
            preview = content[:100] + "..." if len(content) > 100 else content
            print(f"   ✅ 响应: {char_count} 字符")
            print(f"   📄 内容: {preview}")
        
        # 显示错误信息
        if 'error' in entry:
            error = entry['error']
            # 只显示错误的第一行
            error_line = error.split('\n')[0] if error else 'Unknown error'
            print(f"   ❌ 错误: {error_line}")
        
        # 显示元数据
        if 'metadata' in entry:
            metadata = entry['metadata']
            duration = metadata.get('duration_seconds')
            if duration:
                print(f"   ⏱️  耗时: {format_duration(duration)}")
            
            temperature = metadata.get('temperature')
            max_tokens = metadata.get('max_tokens')
            if temperature is not None or max_tokens is not None:
                params = []
                if temperature is not None:
                    params.append(f"temp={temperature}")
                if max_tokens is not None:
                    params.append(f"max_tokens={max_tokens}")
                print(f"   ⚙️  参数: {', '.join(params)}")
        
        print()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='LiteLLM 日志查看工具')
    parser.add_argument('--date', '-d', help='指定日期 (YYYYMMDD)，默认今天')
    parser.add_argument('--log-dir', '-l', default='./log', help='日志目录路径')
    parser.add_argument('--limit', '-n', type=int, help='限制显示的交互数量')
    parser.add_argument('--summary-only', '-s', action='store_true', help='只显示摘要')
    parser.add_argument('--errors-only', '-e', action='store_true', help='只显示错误')
    
    args = parser.parse_args()
    
    # 确定日期
    if args.date:
        date_str = args.date
    else:
        date_str = datetime.now().strftime('%Y%m%d')
    
    # 构造日志文件路径
    log_dir = Path(args.log_dir)
    log_file = log_dir / f"little_llm.{date_str}.log"
    
    print(f"📁 查看日志文件: {log_file}")
    
    # 加载日志条目
    entries = load_log_entries(log_file)
    
    if not entries:
        print("📄 没有找到日志条目")
        
        # 列出可用的日志文件
        available_logs = list(log_dir.glob("little_llm.*.log"))
        if available_logs:
            print(f"\n📂 可用的日志文件:")
            for log in sorted(available_logs):
                file_size = log.stat().st_size
                print(f"  {log.name} ({file_size} 字节)")
        return
    
    # 过滤错误条目
    if args.errors_only:
        entries = [e for e in entries if e.get('interaction_type') == 'error']
        if not entries:
            print("✅ 没有找到错误记录")
            return
    
    # 显示摘要
    print_summary(entries)
    
    # 显示详情
    if not args.summary_only:
        print_interactions(entries, args.limit)


if __name__ == "__main__":
    main()
