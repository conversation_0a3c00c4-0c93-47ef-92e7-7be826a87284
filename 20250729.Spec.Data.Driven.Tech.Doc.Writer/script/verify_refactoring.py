#!/usr/bin/env python3
"""
验证重构后的系统功能

这个脚本验证重构后的核心组件是否正常工作。
"""

import asyncio
import sys
import tempfile
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    from src.backend.agents.url_cache_manager import URLCacheManager
    from src.backend.utils.url_utils import generate_url_hash, normalize_url_for_hash, get_file_extension_from_url
    from src.backend.utils.url_processing_lock import URLProcessingLock, URLAlreadyProcessingError
    from src.backend.utils.content_file_manager import ContentFileManager
    from src.backend.utils.concurrent_processor import ConcurrentProcessor
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


async def test_url_utils():
    """测试URL工具函数"""
    print("🔧 测试URL工具函数...")
    
    # 测试URL标准化
    url1 = "https://Example.com/Page?b=2&a=1#section"
    url2 = "https://example.com:443/page?a=1&b=2"
    
    normalized1 = normalize_url_for_hash(url1)
    normalized2 = normalize_url_for_hash(url2)
    
    print(f"  原始URL1: {url1}")
    print(f"  标准化1: {normalized1}")
    print(f"  原始URL2: {url2}")
    print(f"  标准化2: {normalized2}")
    
    assert normalized1 == normalized2, "URL标准化失败"
    
    # 测试哈希生成
    hash1 = generate_url_hash(url1)
    hash2 = generate_url_hash(url2)
    
    print(f"  哈希1: {hash1}")
    print(f"  哈希2: {hash2}")
    
    assert hash1 == hash2, "URL哈希生成失败"
    assert len(hash1) == 32, "MD5哈希长度错误"
    
    # 测试文件扩展名提取
    assert get_file_extension_from_url("https://example.com/file.pdf") == "pdf"
    assert get_file_extension_from_url("https://example.com/page") == "html"
    
    print("  ✅ URL工具函数测试通过")


async def test_cache_manager():
    """测试缓存管理器"""
    print("💾 测试缓存管理器...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir) / "cache"
        cache_manager = URLCacheManager(cache_dir=cache_dir)
        
        # 初始化
        await cache_manager.initialize()
        print("  ✅ 缓存管理器初始化成功")
        
        test_url = "https://example.com/test-page"
        
        # 测试URL处理状态
        is_processed = await cache_manager.is_url_processed(test_url)
        assert not is_processed, "新URL不应该被标记为已处理"
        print("  ✅ URL处理状态检查正确")
        
        # 生成文件路径
        file_paths = cache_manager.generate_file_paths(test_url, "html")
        print(f"  生成的文件路径: {file_paths}")
        
        # 标记为已处理
        await cache_manager.mark_url_processed(
            url=test_url,
            success=True,
            raw_content_path=file_paths["raw_content_path"],
            markdown_path=file_paths["markdown_path"],
            metadata={"test": "data", "quality_score": 0.8}
        )
        print("  ✅ URL标记为已处理")
        
        # 再次检查状态
        is_processed = await cache_manager.is_url_processed(test_url)
        assert is_processed, "URL应该被标记为已处理"
        print("  ✅ URL处理状态更新正确")
        
        # 获取元数据
        metadata = await cache_manager.get_url_metadata(test_url)
        assert metadata is not None, "应该能获取到元数据"
        assert metadata["success"] is True, "元数据中success应该为True"
        assert metadata["metadata"]["quality_score"] == 0.8, "质量分数应该正确"
        print("  ✅ 元数据获取正确")
        
        # 获取统计信息
        stats = cache_manager.get_cache_stats()
        assert stats["cached_url_hashes_count"] == 1, "缓存URL数量应该为1"
        assert stats["successful_urls"] == 1, "成功URL数量应该为1"
        print(f"  统计信息: {stats}")
        print("  ✅ 缓存统计信息正确")


async def test_processing_lock():
    """测试处理锁"""
    print("🔒 测试处理锁...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        lock_dir = Path(temp_dir) / "locks"
        lock_manager = URLProcessingLock(lock_dir=lock_dir)
        
        test_url = "https://example.com/lock-test"
        
        # 测试正常锁获取和释放
        with lock_manager.acquire_url_lock(test_url) as url_hash:
            print(f"  获取锁成功，URL哈希: {url_hash}")
            
            # 在锁内部，同一URL应该无法再次获取锁
            try:
                with lock_manager.acquire_url_lock(test_url):
                    assert False, "不应该能够获取已被锁定的URL"
            except URLAlreadyProcessingError:
                print("  ✅ 并发锁定检测正确")
        
        # 锁释放后应该可以再次获取
        with lock_manager.acquire_url_lock(test_url):
            print("  ✅ 锁释放后可以重新获取")
        
        print("  ✅ 处理锁测试通过")


async def test_file_manager():
    """测试文件管理器"""
    print("📁 测试文件管理器...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        file_manager = ContentFileManager()
        
        # 测试保存原始内容
        raw_file = Path(temp_dir) / "test.html"
        raw_content = "<html><body>Test content</body></html>"
        
        success = await file_manager.save_raw_content(raw_file, raw_content)
        assert success, "保存原始内容应该成功"
        assert raw_file.exists(), "原始文件应该存在"
        assert raw_file.read_text(encoding='utf-8') == raw_content, "文件内容应该正确"
        print("  ✅ 原始内容保存正确")
        
        # 测试保存Markdown内容
        md_file = Path(temp_dir) / "test.md"
        md_content = "# Test\n\nThis is a test."
        
        success = await file_manager.save_markdown_content(md_file, md_content)
        assert success, "保存Markdown内容应该成功"
        assert md_file.exists(), "Markdown文件应该存在"
        assert md_file.read_text(encoding='utf-8') == md_content, "文件内容应该正确"
        print("  ✅ Markdown内容保存正确")
        
        # 测试结果创建
        error_result = file_manager.create_error_result("https://example.com", "Test error", "test")
        assert error_result["success"] is False, "错误结果应该标记为失败"
        assert error_result["error"] == "Test error", "错误信息应该正确"
        print("  ✅ 错误结果创建正确")
        
        success_result = file_manager.create_success_result(
            url="https://example.com",
            raw_content_path="test.html",
            markdown_path="test.md"
        )
        assert success_result["success"] is True, "成功结果应该标记为成功"
        assert success_result["raw_content_path"] == "test.html", "文件路径应该正确"
        print("  ✅ 成功结果创建正确")


async def test_concurrent_processor():
    """测试并发处理器"""
    print("⚡ 测试并发处理器...")
    
    processor = ConcurrentProcessor()
    
    # 模拟处理函数
    processed_urls = []
    
    async def mock_process_func(url: str):
        processed_urls.append(url)
        await asyncio.sleep(0.05)  # 模拟处理时间
        return {
            "url": url,
            "success": True,
            "processing_time": 0.05
        }
    
    urls = [f"https://example.com/page{i}" for i in range(5)]
    
    # 测试并发处理
    import time
    start_time = time.time()
    results = await processor.process_urls_concurrently(
        urls=urls,
        process_func=mock_process_func,
        max_concurrent=3
    )
    end_time = time.time()
    
    processing_time = end_time - start_time
    print(f"  处理时间: {processing_time:.2f}秒")
    
    assert len(results) == 5, "结果数量应该正确"
    assert len(processed_urls) == 5, "处理的URL数量应该正确"
    assert processing_time < 0.3, "并发处理应该比串行处理快"
    
    for result in results:
        assert result["success"] is True, "所有结果应该成功"
    
    print("  ✅ 并发处理器测试通过")


async def main():
    """主测试函数"""
    print("🚀 开始验证重构后的系统功能...\n")
    
    try:
        await test_url_utils()
        print()
        
        await test_cache_manager()
        print()
        
        await test_processing_lock()
        print()
        
        await test_file_manager()
        print()
        
        await test_concurrent_processor()
        print()
        
        print("🎉 所有测试通过！重构后的系统功能正常。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
