# 多智能体技术文档写作系统 - Git 忽略文件

# ============================================================================
# 项目特定忽略规则
# ============================================================================

# 数据目录 - 包含所有运行时生成的数据文件
data/
data/*
!data/.gitkeep

# 日志文件目录 - 包含应用日志和 LiteLLM 交互日志
log/
log/*
*.log

# 实际配置文件 - 包含敏感信息如 API 密钥
config.yaml

# ============================================================================
# Python 相关
# ============================================================================

# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# 分发 / 打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ============================================================================
# 操作系统相关
# ============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# 编辑器和 IDE
# ============================================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ============================================================================
# 数据库和存储
# ============================================================================

# SQLite
*.db
*.sqlite
*.sqlite3

# Redis
dump.rdb

# ============================================================================
# 临时文件和调试
# ============================================================================

# 临时文件
*.tmp
*.temp
*.bak
*.backup
*.orig

# 调试文件
debug.log
error.log
access.log

# 性能分析
*.prof

# ============================================================================
# 网络和安全
# ============================================================================

# 环境变量文件
.env.local
.env.development
.env.test
.env.production

# SSL 证书
*.pem
*.key
*.crt
*.csr

# ============================================================================
# 项目特定的临时文件
# ============================================================================

# LLM 响应缓存
*.llm_cache
*.response_cache

# 文档生成临时文件
*.md.tmp
*.html.tmp

# 测试输出
test_output/
test_results/

# 性能测试结果
benchmark_results/
performance_logs/
